<?php

namespace App\Http\Controllers;

use App\Models\Minister;
use Illuminate\Http\Request;

class Minister<PERSON><PERSON><PERSON><PERSON> extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Minister $minister)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Minister $minister)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Minister $minister)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Minister $minister)
    {
        //
    }
}
