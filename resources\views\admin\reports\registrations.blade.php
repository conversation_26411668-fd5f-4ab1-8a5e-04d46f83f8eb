@extends(auth()->user()->hasRole('admin') ? 'layouts.admin' : 'layouts.user')

@section('title', 'Registration Reports')
@if(auth()->user()->hasRole('admin'))
@section('page-title', 'Registration Reports')
@endif

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">Registration Reports</h2>
                <p class="text-muted">Detailed analysis of summit registrations and payment status.</p>
            </div>
            <a href="{{ route('admin.reports.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Reports
            </a>
        </div>
    </div>
</div>

<!-- Filter Form -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.reports.registrations') }}">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="summit_id" class="form-label">Summit</label>
                            <select name="summit_id" id="summit_id" class="form-select">
                                <option value="">All Summits</option>
                                @foreach($summits as $summit)
                                    <option value="{{ $summit->id }}" {{ request('summit_id') == $summit->id ? 'selected' : '' }}>
                                        {{ $summit->title }} ({{ $summit->year }})
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Payment Status</label>
                            <select name="status" id="status" class="form-select">
                                <option value="">All Status</option>
                                <option value="paid" {{ request('status') == 'paid' ? 'selected' : '' }}>Paid</option>
                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="conference_id" class="form-label">Conference</label>
                            <select name="conference_id" id="conference_id" class="form-select">
                                <option value="">All Conferences</option>
                                @foreach($conferences as $conference)
                                    <option value="{{ $conference->id }}" {{ request('conference_id') == $conference->id ? 'selected' : '' }}>
                                        {{ $conference->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">Date From</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" value="{{ request('date_from') }}">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">Date To</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" value="{{ request('date_to') }}">
                        </div>
                        <div class="col-md-9 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-2"></i>Apply Filters
                            </button>
                            <a href="{{ route('admin.reports.registrations') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Clear Filters
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-2 col-md-4 mb-3">
        <div class="card stat-card h-100">
            <div class="card-body text-center">
                <h3 class="mb-1">{{ number_format($filteredStats['total_registrations']) }}</h3>
                <p class="text-muted mb-0">Total Registrations</p>
            </div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 mb-3">
        <div class="card stat-card-success h-100">
            <div class="card-body text-center">
                <h3 class="mb-1">{{ number_format($filteredStats['paid_registrations']) }}</h3>
                <p class="text-muted mb-0">Paid</p>
            </div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 mb-3">
        <div class="card stat-card-warning h-100">
            <div class="card-body text-center">
                <h3 class="mb-1">{{ number_format($filteredStats['pending_registrations']) }}</h3>
                <p class="text-muted mb-0">Pending</p>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stat-card-info h-100">
            <div class="card-body text-center">
                <h3 class="mb-1">TSh {{ number_format($filteredStats['total_amount']) }}</h3>
                <p class="text-muted mb-0">Total Amount</p>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stat-card-success h-100">
            <div class="card-body text-center">
                <h3 class="mb-1">TSh {{ number_format($filteredStats['paid_amount']) }}</h3>
                <p class="text-muted mb-0">Paid Amount</p>
            </div>
        </div>
    </div>
</div>

<!-- Registrations Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>Registration Details</h5>
            </div>
            <div class="card-body">
                @if($registrations->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Registration #</th>
                                <th>User</th>
                                <th>Summit</th>
                                <th>Branch/Conference</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($registrations as $registration)
                            <tr>
                                <td>
                                    <span class="fw-bold">#{{ $registration->registration_number }}</span>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-bold">{{ $registration->user->full_name }}</div>
                                        <small class="text-muted">{{ $registration->user->email }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-bold">{{ Str::limit($registration->summit->title, 25) }}</div>
                                        <small class="text-muted">{{ $registration->summit->year }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-bold">{{ $registration->user->branch->name ?? 'N/A' }}</div>
                                        <small class="text-muted">{{ $registration->user->branch->zone->conference->name ?? 'N/A' }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-bold">TSh {{ number_format($registration->total_amount) }}</div>
                                        @if($registration->paid_amount > 0)
                                            <small class="text-success">Paid: TSh {{ number_format($registration->paid_amount) }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $registration->payment_complete ? 'success' : 'warning' }}">
                                        {{ $registration->payment_complete ? 'Paid' : 'Pending' }}
                                    </span>
                                </td>
                                <td>{{ $registration->created_at->format('M j, Y') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="#" class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if(!$registration->payment_complete)
                                        <a href="#" class="btn btn-outline-success" title="Mark as Paid">
                                            <i class="fas fa-check"></i>
                                        </a>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $registrations->withQueryString()->links() }}
                </div>
                @else
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No registrations found</h5>
                    <p class="text-muted">Try adjusting your filters to see more results.</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
