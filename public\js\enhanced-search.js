/**
 * Enhanced Search Functionality
 * Provides real-time table filtering and search suggestions
 */

class EnhancedSearch {
    constructor(options = {}) {
        this.options = {
            searchInputId: 'enhanced-search-input',
            tableSelector: '.table tbody',
            minSearchLength: 2,
            debounceDelay: 300,
            highlightClass: 'search-highlight',
            ...options
        };
        
        this.searchInput = null;
        this.tableRows = [];
        this.searchTimeout = null;
        this.originalRowsHtml = [];
        
        this.init();
    }
    
    init() {
        this.searchInput = document.getElementById(this.options.searchInputId);
        if (!this.searchInput) return;
        
        this.cacheTableRows();
        this.bindEvents();
        this.addStyles();
    }
    
    cacheTableRows() {
        const tableBody = document.querySelector(this.options.tableSelector);
        if (!tableBody) return;
        
        this.tableRows = Array.from(tableBody.querySelectorAll('tr'));
        this.originalRowsHtml = this.tableRows.map(row => row.innerHTML);
    }
    
    bindEvents() {
        // Real-time table filtering
        this.searchInput.addEventListener('input', (e) => {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.filterTable(e.target.value.trim());
            }, this.options.debounceDelay);
        });
        
        // Clear search
        const clearButton = document.getElementById('clear-search');
        if (clearButton) {
            clearButton.addEventListener('click', () => {
                this.clearSearch();
            });
        }
    }
    
    filterTable(searchTerm) {
        if (searchTerm.length < this.options.minSearchLength) {
            this.showAllRows();
            return;
        }
        
        let visibleCount = 0;
        
        this.tableRows.forEach((row, index) => {
            const rowText = row.textContent.toLowerCase();
            const isMatch = rowText.includes(searchTerm.toLowerCase());
            
            if (isMatch) {
                row.style.display = '';
                this.highlightSearchTerm(row, searchTerm, index);
                visibleCount++;
            } else {
                row.style.display = 'none';
                this.removeHighlight(row, index);
            }
        });
        
        this.updateResultsCount(visibleCount, searchTerm);
    }
    
    highlightSearchTerm(row, searchTerm, rowIndex) {
        // Restore original HTML first
        row.innerHTML = this.originalRowsHtml[rowIndex];
        
        if (!searchTerm) return;
        
        const regex = new RegExp(`(${this.escapeRegex(searchTerm)})`, 'gi');
        const cells = row.querySelectorAll('td');
        
        cells.forEach(cell => {
            // Skip cells with buttons/actions
            if (cell.querySelector('button, a.btn, .btn-group')) return;
            
            const originalHtml = cell.innerHTML;
            const highlightedHtml = originalHtml.replace(regex, `<mark class="${this.options.highlightClass}">$1</mark>`);
            cell.innerHTML = highlightedHtml;
        });
    }
    
    removeHighlight(row, rowIndex) {
        row.innerHTML = this.originalRowsHtml[rowIndex];
    }
    
    showAllRows() {
        this.tableRows.forEach((row, index) => {
            row.style.display = '';
            this.removeHighlight(row, index);
        });
        this.updateResultsCount(this.tableRows.length);
    }
    
    clearSearch() {
        this.searchInput.value = '';
        this.showAllRows();
        this.searchInput.focus();
    }
    
    updateResultsCount(count, searchTerm = '') {
        let countElement = document.getElementById('search-results-count');
        
        if (!countElement) {
            countElement = document.createElement('div');
            countElement.id = 'search-results-count';
            countElement.className = 'alert alert-info mt-3';
            
            const tableContainer = document.querySelector('.table-responsive') || document.querySelector('.table').parentNode;
            tableContainer.insertBefore(countElement, tableContainer.firstChild);
        }
        
        if (searchTerm) {
            countElement.innerHTML = `
                <i class="fas fa-search me-2"></i>
                Found <strong>${count}</strong> result${count !== 1 ? 's' : ''} for "<em>${this.escapeHtml(searchTerm)}</em>"
                ${count === 0 ? '<br><small class="text-muted">Try different keywords or check spelling</small>' : ''}
            `;
            countElement.style.display = 'block';
        } else {
            countElement.style.display = 'none';
        }
    }
    
    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    
    escapeHtml(string) {
        const div = document.createElement('div');
        div.textContent = string;
        return div.innerHTML;
    }
    
    addStyles() {
        if (document.getElementById('enhanced-search-styles')) return;
        
        const styles = document.createElement('style');
        styles.id = 'enhanced-search-styles';
        styles.textContent = `
            .${this.options.highlightClass} {
                background-color: #fff3cd;
                color: #856404;
                padding: 1px 2px;
                border-radius: 2px;
                font-weight: 500;
            }
            
            .table tbody tr {
                transition: opacity 0.2s ease-in-out;
            }
            
            .table tbody tr[style*="display: none"] {
                opacity: 0;
            }
            
            #search-results-count {
                margin-bottom: 1rem;
                border-left: 4px solid #0d6efd;
            }
            
            .search-no-results {
                text-align: center;
                padding: 2rem;
                color: #6c757d;
            }
            
            .search-no-results i {
                font-size: 3rem;
                margin-bottom: 1rem;
                opacity: 0.5;
            }
        `;
        
        document.head.appendChild(styles);
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if we have the enhanced search input
    if (document.getElementById('enhanced-search-input')) {
        window.enhancedSearch = new EnhancedSearch();
    }
});

// Export for manual initialization
window.EnhancedSearch = EnhancedSearch;
