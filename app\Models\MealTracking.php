<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MealTracking extends Model
{
    protected $fillable = [
        'user_id',
        'summit_id',
        'summit_activity_id',
        'meal_type',
        'scanned_at',
        'scanned_by',
        'notes'
    ];

    protected $casts = [
        'scanned_at' => 'datetime'
    ];

    /**
     * Get the user that owns the meal tracking.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the summit that owns the meal tracking.
     */
    public function summit(): BelongsTo
    {
        return $this->belongsTo(Summit::class);
    }

    /**
     * Get the summit activity that owns the meal tracking.
     */
    public function summitActivity(): BelongsTo
    {
        return $this->belongsTo(SummitActivity::class);
    }
}
