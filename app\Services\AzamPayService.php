<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\SummitRegistration;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;

class AzamPayService
{
    protected $baseUrl;
    protected $appName;
    protected $clientId;
    protected $clientSecret;
    protected $apiKey;
    protected $token;

    public function __construct()
    {
        $this->baseUrl = config('services.azampay.base_url', 'https://sandbox.azampay.co.tz');
        $this->appName = config('services.azampay.app_name', 'TUCASA_STU');
        $this->clientId = config('services.azampay.client_id');
        $this->clientSecret = config('services.azampay.client_secret');
        $this->apiKey = config('services.azampay.api_key');
    }

    /**
     * Get authentication token
     */
    private function getAuthToken()
    {
        if ($this->token) {
            return $this->token;
        }

        try {
            // Use correct authenticator URL and endpoint from documentation
            $authUrl = 'https://authenticator-sandbox.azampay.co.tz';

            $response = Http::withOptions([
                'verify' => false, // Disable SSL verification for sandbox
            ])->withHeaders([
                'Content-Type' => 'application/json',
            ])->post($authUrl . '/AppRegistration/GenerateToken', [
                'appName' => $this->appName,
                'clientId' => $this->clientId,
                'clientSecret' => $this->clientSecret,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $this->token = $data['data']['accessToken'] ?? null;
                return $this->token;
            }

            Log::error('AzamPay auth failed', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);
            return null;

        } catch (\Exception $e) {
            Log::error('AzamPay auth error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Initiate mobile money payment
     */
    public function initiateMobileMoneyPayment(SummitRegistration $registration, float $amount, string $phoneNumber, string $provider = 'Vodacom')
    {
        $token = $this->getAuthToken();
        if (!$token) {
            return ['success' => false, 'message' => 'Authentication failed'];
        }

        $paymentReference = 'AZAM_MM_' . $registration->id . '_' . time();

        // Create payment record
        $payment = Payment::create([
            'user_id' => $registration->user_id,
            'summit_registration_id' => $registration->id,
            'payment_reference' => $paymentReference,
            'amount' => $amount,
            'payment_method' => 'azampay_mobile',
            'status' => 'pending',
            'gateway_data' => [
                'provider' => $provider,
                'phone_number' => $phoneNumber,
                'gateway' => 'azampay'
            ],
        ]);

        try {
            $response = Http::withOptions([
                'verify' => false, // Disable SSL verification for sandbox
            ])->withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $token,
            ])->post($this->baseUrl . '/azampay/mno/checkout', [
                'accountNumber' => $phoneNumber,
                'amount' => (string)$amount, // Amount should be string according to docs
                'currency' => 'TZS',
                'externalId' => $paymentReference,
                'provider' => $this->mapProvider($provider),
                'additionalProperties' => [
                    'description' => 'TUCASA Summit Registration Payment',
                    'merchantName' => 'TUCASA STU',
                    'merchantMobileNumber' => config('services.azampay.merchant_mobile'),
                ]
            ]);

            if ($response->successful()) {
                $responseData = $response->json();

                // Check if the response indicates success
                if ($responseData['success'] ?? false) {
                    $payment->update([
                        'transaction_id' => $responseData['transactionId'] ?? null,
                        'gateway_data' => array_merge($payment->gateway_data, $responseData),
                        'status' => 'processing',
                    ]);

                    return [
                        'success' => true,
                        'payment' => $payment,
                        'message' => $responseData['message'] ?? 'Payment initiated successfully. Please check your phone for USSD prompt.',
                        'data' => $responseData,
                    ];
                } else {
                    $payment->update([
                        'status' => 'failed',
                        'gateway_response' => json_encode($responseData),
                    ]);

                    return [
                        'success' => false,
                        'message' => $responseData['message'] ?? 'Mobile money payment initiation failed.',
                        'data' => $responseData,
                    ];
                }
            } else {
                $payment->update([
                    'status' => 'failed',
                    'gateway_response' => $response->body(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Payment initialization failed',
                    'error' => $response->json()['message'] ?? 'Unknown error',
                ];
            }
        } catch (\Exception $e) {
            Log::error('AzamPay mobile money error', [
                'error' => $e->getMessage(),
                'payment_id' => $payment->id,
                'user_id' => $registration->user_id,
                'amount' => $amount,
                'provider' => $provider,
                'trace' => $e->getTraceAsString()
            ]);

            $payment->update([
                'status' => 'failed',
                'gateway_response' => $e->getMessage(),
            ]);

            // Don't expose internal errors to users
            return [
                'success' => false,
                'message' => 'Payment processing failed. Please try again or contact support.',
            ];
        }
    }

    /**
     * Initiate bank checkout payment
     */
    public function initiateBankPayment(SummitRegistration $registration, float $amount, string $bankCode = null)
    {
        $token = $this->getAuthToken();
        if (!$token) {
            return ['success' => false, 'message' => 'Authentication failed'];
        }

        $paymentReference = 'AZAM_BANK_' . $registration->id . '_' . time();

        // Create payment record
        $payment = Payment::create([
            'user_id' => $registration->user_id,
            'summit_registration_id' => $registration->id,
            'payment_reference' => $paymentReference,
            'amount' => $amount,
            'payment_method' => 'azampay_bank',
            'status' => 'pending',
            'gateway_data' => [
                'bank_code' => $bankCode,
                'gateway' => 'azampay'
            ],
        ]);

        try {
            $response = Http::withOptions([
                'verify' => false, // Disable SSL verification for sandbox
            ])->withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $token,
            ])->post($this->baseUrl . '/azampay/bank/checkout', [
                'amount' => (string)$amount, // Amount should be string according to docs
                'currencyCode' => 'TZS', // Note: currencyCode not currency for bank checkout
                'merchantAccountNumber' => config('services.azampay.merchant_account', ''),
                'merchantMobileNumber' => config('services.azampay.merchant_mobile'),
                'merchantName' => 'TUCASA STU',
                'provider' => $bankCode ?? 'CRDB',
                'referenceId' => $paymentReference,
                'otp' => '', // OTP will be handled separately if needed
            ]);

            if ($response->successful()) {
                $responseData = $response->json();

                // Check if the response indicates success
                if ($responseData['success'] ?? false) {
                    $payment->update([
                        'transaction_id' => $responseData['transactionId'] ?? null,
                        'gateway_data' => array_merge($payment->gateway_data, $responseData),
                        'status' => 'processing',
                    ]);

                    return [
                        'success' => true,
                        'payment' => $payment,
                        'message' => $responseData['message'] ?? 'Bank payment initiated successfully.',
                        'data' => $responseData,
                    ];
                } else {
                    $payment->update([
                        'status' => 'failed',
                        'gateway_response' => json_encode($responseData),
                    ]);

                    return [
                        'success' => false,
                        'message' => $responseData['message'] ?? 'Bank payment initiation failed.',
                        'data' => $responseData,
                    ];
                }
            } else {
                $payment->update([
                    'status' => 'failed',
                    'gateway_response' => $response->body(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Bank payment initialization failed',
                    'error' => $response->json()['message'] ?? 'Unknown error',
                ];
            }
        } catch (\Exception $e) {
            Log::error('AzamPay bank payment error: ' . $e->getMessage());
            
            $payment->update([
                'status' => 'failed',
                'gateway_response' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Bank payment processing error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check payment status
     */
    public function checkPaymentStatus(Payment $payment)
    {
        $token = $this->getAuthToken();
        if (!$token) {
            return ['success' => false, 'message' => 'Authentication failed'];
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'X-API-Key' => $this->apiKey,
            ])->get($this->baseUrl . '/azampay/gw/checkout/status', [
                'pgReferenceId' => $payment->transaction_id,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $status = $data['data']['status'] ?? null;
                
                $payment->update([
                    'status' => $this->mapAzamPayStatus($status),
                    'gateway_data' => array_merge($payment->gateway_data ?? [], $data['data'] ?? []),
                    'paid_at' => $status === 'success' ? now() : null,
                ]);

                return ['success' => true, 'status' => $status, 'data' => $data];
            }

            return ['success' => false, 'message' => 'Status check failed'];
            
        } catch (\Exception $e) {
            Log::error('AzamPay status check error: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Handle webhook callback
     */
    public function handleWebhook(array $data)
    {
        try {
            // AzamPay callback format: utilityref is our payment reference
            $utilityRef = $data['utilityref'] ?? null;
            $transactionStatus = $data['transactionstatus'] ?? null;
            $reference = $data['reference'] ?? null; // AzamPay transaction ID

            if (!$utilityRef) {
                return ['success' => false, 'message' => 'Invalid utility reference'];
            }

            $payment = Payment::where('payment_reference', $utilityRef)->first();

            if (!$payment) {
                return ['success' => false, 'message' => 'Payment not found'];
            }

            // Update payment status
            $payment->update([
                'status' => $this->mapAzamPayStatus($transactionStatus),
                'transaction_id' => $reference,
                'gateway_data' => array_merge($payment->gateway_data ?? [], $data),
                'gateway_callback_at' => now(),
                'paid_at' => $transactionStatus === 'success' ? now() : null,
                'receipt_number' => $reference,
            ]);

            // Update registration if payment is completed
            if ($transactionStatus === 'success') {
                $registration = $payment->summitRegistration;
                $registration->paid_amount += $payment->amount;
                $registration->balance = $registration->total_amount - $registration->paid_amount;
                $registration->payment_complete = $registration->balance <= 0;
                $registration->save();
            }

            return ['success' => true, 'payment' => $payment];

        } catch (\Exception $e) {
            Log::error('AzamPay webhook processing error', [
                'error' => $e->getMessage(),
                'data' => $data,
                'trace' => $e->getTraceAsString()
            ]);
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Map provider names to AzamPay enum values
     * Based on documentation: airtel=2, tigo=3, halopesa=4, azampesa=5, Mpesa=10
     */
    private function mapProvider($provider)
    {
        return match(strtolower($provider)) {
            'mpesa', 'm-pesa', 'vodacom' => 10, // Mpesa
            'tigo', 'tigopesa', 'tigo pesa' => 3, // Tigo
            'airtel', 'airtelmoney', 'airtel money' => 2, // Airtel
            'halopesa', 'halo pesa' => 4, // Halopesa
            'azampesa', 'azam pesa' => 5, // Azampesa
            default => 10, // Default to Mpesa
        };
    }

    /**
     * Map AzamPay status to our internal status
     */
    private function mapAzamPayStatus($azamPayStatus)
    {
        return match($azamPayStatus) {
            'success' => 'completed',
            'pending' => 'processing',
            'failed' => 'failed',
            'cancelled' => 'cancelled',
            default => 'pending',
        };
    }

    /**
     * Get available mobile money providers
     */
    public function getAvailableMobileProviders(): array
    {
        return [
            'Mpesa' => 'M-Pesa (Vodacom)',
            'Tigo' => 'Tigo Pesa',
            'Airtel' => 'Airtel Money',
            'Halopesa' => 'HaloPesa',
            'Azampesa' => 'Azam Pesa',
        ];
    }

    /**
     * Get available banks
     */
    public function getAvailableBanks(): array
    {
        return [
            'CRDB' => 'CRDB Bank',
            'NMB' => 'NMB Bank',
            'NBC' => 'NBC Bank',
            'EXIM' => 'EXIM Bank',
            'DTB' => 'Diamond Trust Bank',
            'STANBIC' => 'Stanbic Bank',
            'ABSA' => 'ABSA Bank',
        ];
    }
}
