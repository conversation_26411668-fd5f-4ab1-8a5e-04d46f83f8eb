<?php $__env->startSection('title', 'Zones Management'); ?>
<?php $__env->startSection('page-title', 'Zones Management'); ?>

<?php $__env->startPush('styles'); ?>
<style>
.admin-page-container {
    padding: 20px;
    max-width: 100%;
}

.page-header {
    background-color: var(--md-surface, white);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-title {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: #1a1a1a;
}

.page-subtitle {
    font-size: 16px;
    color: #666;
    margin: 0;
}

.search-card {
    background-color: var(--md-surface, white);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #1a1a1a;
}

.search-form {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 16px;
    align-items: end;
}

.search-inputs {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

.form-input, .form-select {
    padding: 12px 16px;
    border: 1.5px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.2s ease;
    background: white;
}

.form-input:focus, .form-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-actions {
    display: flex;
    gap: 8px;
}

.btn-primary {
    background-color: #667eea;
    border: none;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary:hover {
    background-color: #5a67d8;
    transform: translateY(-1px);
    color: white;
    text-decoration: none;
}

.btn-outline-secondary {
    background-color: transparent;
    border: 1.5px solid #e5e7eb;
    color: #6b7280;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-outline-secondary:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
    color: #374151;
    text-decoration: none;
}

.data-table-card {
    background-color: var(--md-surface, white);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-header {
    background-color: #f8fafc;
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.table-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #1a1a1a;
}

.table-responsive {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 800px;
}

.data-table th {
    background-color: #f8fafc;
    padding: 16px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
    font-size: 14px;
}

.data-table td {
    padding: 16px;
    border-bottom: 1px solid #f1f5f9;
    color: #1f2937;
}

.data-table tr:hover {
    background-color: #f8fafc;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.status-active {
    background-color: #d1fae5;
    color: #065f46;
}

.status-inactive {
    background-color: #f3f4f6;
    color: #6b7280;
}

.count-badge {
    background-color: #dbeafe;
    color: #1e40af;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.action-buttons {
    display: flex;
    gap: 4px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 6px;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.empty-state-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #374151;
}

.empty-state-text {
    font-size: 16px;
    margin: 0 0 20px 0;
}

@media (max-width: 768px) {
    .admin-page-container {
        padding: 16px;
    }

    .page-header {
        padding: 20px;
    }

    .page-header > div {
        flex-direction: column;
        gap: 16px;
        align-items: stretch !important;
    }

    .search-form {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .search-inputs {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .search-actions {
        justify-content: stretch;
    }

    .search-actions .btn-primary,
    .search-actions .btn-outline-secondary {
        flex: 1;
        justify-content: center;
    }

    .data-table-card {
        margin: 0 -16px;
        border-radius: 0;
    }

    .table-header {
        padding: 16px;
    }

    .data-table th,
    .data-table td {
        padding: 12px 8px;
        font-size: 14px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 4px;
    }

    .btn-sm {
        padding: 8px 12px;
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 24px;
    }

    .data-table {
        min-width: 600px;
    }

    .data-table th,
    .data-table td {
        padding: 8px 6px;
        font-size: 13px;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="admin-page-container">
    <!-- Page Header -->
    <div class="page-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1 class="page-title">Zones Management</h1>
                <p class="page-subtitle">Manage zones in the system.</p>
            </div>
            <a href="<?php echo e(route('admin.zones.create')); ?>" class="btn-primary">
                <span class="material-icons">add</span>
                Add Zone
            </a>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="search-card">
        <h2 class="search-title">
            <span class="material-icons">search</span>
            Search & Filter
        </h2>
        <form method="GET" action="<?php echo e(route('admin.zones.index')); ?>" class="search-form">
            <div class="search-inputs">
                <div class="form-group">
                    <label class="form-label">Search</label>
                    <input
                        type="text"
                        name="search"
                        class="form-input"
                        placeholder="Search by name or code..."
                        value="<?php echo e(request('search')); ?>"
                    >
                </div>
                <div class="form-group">
                    <label class="form-label">Conference</label>
                    <select name="conference_id" class="form-select">
                        <option value="">All Conferences</option>
                        <?php $__currentLoopData = $conferences; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $conference): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($conference->id); ?>" <?php echo e(request('conference_id') == $conference->id ? 'selected' : ''); ?>>
                                <?php echo e($conference->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
            </div>
            <div class="search-actions">
                <button type="submit" class="btn-primary">
                    <span class="material-icons">search</span>
                    Search
                </button>
                <a href="<?php echo e(route('admin.zones.index')); ?>" class="btn-outline-secondary">
                    <span class="material-icons">clear</span>
                    Clear
                </a>
            </div>
        </form>
    </div>

    <!-- Zones Table -->
    <div class="data-table-card">
        <div class="table-header">
            <h2 class="table-title">
                <span class="material-icons">location_on</span>
                Zones (<?php echo e($zones->total()); ?>)
            </h2>
        </div>

        <?php if($zones->count() > 0): ?>
        <div class="table-responsive">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Zone</th>
                        <th>Code</th>
                        <th>Conference</th>
                        <th>Branches</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $zones; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $zone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td>
                            <div>
                                <div style="font-weight: 600; margin-bottom: 2px;"><?php echo e($zone->name); ?></div>
                                <?php if($zone->description): ?>
                                    <div style="font-size: 12px; color: #6b7280;">
                                        <?php echo e(Str::limit($zone->description, 50)); ?>

                                    </div>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-active"><?php echo e($zone->code); ?></span>
                        </td>
                        <td><?php echo e($zone->conference->name); ?></td>
                        <td style="text-align: center;">
                            <span class="count-badge"><?php echo e($zone->branches->count()); ?></span>
                        </td>
                        <td>
                            <span class="status-badge <?php echo e($zone->is_active ? 'status-active' : 'status-inactive'); ?>">
                                <?php echo e($zone->is_active ? 'Active' : 'Inactive'); ?>

                            </span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="<?php echo e(route('admin.zones.show', $zone)); ?>"
                                   class="btn-outline-secondary btn-sm" title="View Details">
                                    <span class="material-icons" style="font-size: 16px;">visibility</span>
                                </a>
                                <a href="<?php echo e(route('admin.zones.edit', $zone)); ?>"
                                   class="btn-outline-secondary btn-sm" title="Edit">
                                    <span class="material-icons" style="font-size: 16px;">edit</span>
                                </a>
                                <?php if(!$zone->branches->count()): ?>
                                <form method="POST" action="<?php echo e(route('admin.zones.destroy', $zone)); ?>"
                                      style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this zone?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn-outline-secondary btn-sm" title="Delete"
                                            style="color: #ef4444; border-color: #ef4444;">
                                        <span class="material-icons" style="font-size: 16px;">delete</span>
                                    </button>
                                </form>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div style="padding: 20px; text-align: center; border-top: 1px solid #e5e7eb;">
            <?php echo e($zones->withQueryString()->links()); ?>

        </div>
        <?php else: ?>
        <div class="empty-state">
            <div class="empty-state-icon">
                <span class="material-icons" style="font-size: inherit;">location_on</span>
            </div>
            <h3 class="empty-state-title">No zones found</h3>
            <p class="empty-state-text">
                <?php if(request('search')): ?>
                    No zones match your search criteria.
                <?php else: ?>
                    Get started by adding your first zone.
                <?php endif; ?>
            </p>
            <?php if(!request('search')): ?>
            <a href="<?php echo e(route('admin.zones.create')); ?>" class="btn-primary">
                <span class="material-icons">add</span>
                Add Zone
            </a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\summit-management-system\resources\views/admin/zones/index.blade.php ENDPATH**/ ?>