<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class SessionTimeoutMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Auth::check()) {
            $timeout = config('session.lifetime') * 60; // Convert minutes to seconds
            $lastActivity = Session::get('last_activity', time());
            
            // Check if session has timed out (5 minutes = 300 seconds)
            if (time() - $lastActivity > 300) {
                // Store the intended URL before logout
                if (!$request->ajax() && !$request->wantsJson()) {
                    Session::put('url.intended', $request->fullUrl());
                }
                
                Auth::logout();
                Session::flush();
                
                if ($request->ajax() || $request->wantsJson()) {
                    return response()->json([
                        'message' => 'Session expired. Please login again.',
                        'redirect' => route('login'),
                        'session_expired' => true
                    ], 401);
                }
                
                return redirect()->route('login')
                    ->with('session_expired', true)
                    ->with('message', 'Your session has expired due to inactivity. Please login again.');
            }
            
            // Update last activity time
            Session::put('last_activity', time());
        }
        
        return $next($request);
    }
}
