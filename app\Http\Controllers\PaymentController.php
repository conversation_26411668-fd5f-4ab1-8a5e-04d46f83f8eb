<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\SummitRegistration;
use App\Services\SelcomPaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PaymentController extends Controller
{
    protected $selcomService;

    public function __construct(SelcomPaymentService $selcomService)
    {
        $this->middleware('auth')->except(['webhook']);
        $this->selcomService = $selcomService;
    }

    /**
     * Show payment form
     */
    public function create(SummitRegistration $registration)
    {
        $this->authorize('view', $registration);

        if ($registration->payment_complete) {
            return redirect()->route('dashboard')
                ->with('info', 'Payment for this summit is already complete.');
        }

        return view('payments.create', compact('registration'));
    }

    /**
     * Process payment
     */
    public function store(Request $request, SummitRegistration $registration)
    {
        $this->authorize('view', $registration);

        $validated = $request->validate([
            'amount' => 'required|numeric|min:1000|max:' . $registration->balance,
            'phone_number' => 'required|string|regex:/^[67]\d{8}$/',
            'payment_method' => 'required|in:selcom,mobile_money',
            'mno_provider' => 'required|in:vodacom,tigo,airtel,halopesa',
        ]);

        if ($registration->payment_complete) {
            return redirect()->route('dashboard')
                ->with('error', 'Payment for this summit is already complete.');
        }

        $result = $this->selcomService->initiatePayment(
            $registration,
            $validated['amount'],
            $validated['phone_number'],
            $validated['mno_provider']
        );

        if ($result['success']) {
            if (isset($result['checkout_url'])) {
                return redirect($result['checkout_url']);
            }

            return redirect()->route('payments.show', $result['payment'])
                ->with('success', 'Payment initiated successfully. Please complete the payment on your mobile device.');
        }

        return back()->withErrors(['payment' => $result['message']]);
    }

    /**
     * Show payment details
     */
    public function show(Payment $payment)
    {
        $this->authorize('view', $payment);

        return view('payments.show', compact('payment'));
    }

    /**
     * Handle payment webhook
     */
    public function webhook(Request $request)
    {
        $result = $this->selcomService->handleWebhook($request->all());

        if ($result['success']) {
            return response()->json(['status' => 'success']);
        }

        return response()->json(['status' => 'error', 'message' => $result['message']], 400);
    }

    /**
     * Handle payment success
     */
    public function success(Payment $payment)
    {
        $this->authorize('view', $payment);

        // Check payment status
        $this->selcomService->checkPaymentStatus($payment);
        $payment->refresh();

        return view('payments.success', compact('payment'));
    }

    /**
     * Handle payment cancellation
     */
    public function cancel(Payment $payment)
    {
        $this->authorize('view', $payment);

        $payment->update(['status' => 'cancelled']);

        return view('payments.cancel', compact('payment'));
    }

    /**
     * Check payment status (AJAX endpoint)
     */
    public function checkStatus(Payment $payment)
    {
        $this->authorize('view', $payment);

        $oldStatus = $payment->status;

        // Check status with Selcom
        $result = $this->selcomService->checkPaymentStatus($payment);

        $payment->refresh();

        return response()->json([
            'success' => $result['success'] ?? false,
            'status' => $payment->status,
            'status_changed' => $oldStatus !== $payment->status,
            'message' => $result['message'] ?? 'Status checked successfully'
        ]);
    }
}
