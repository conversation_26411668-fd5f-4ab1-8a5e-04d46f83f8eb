<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Activities - {{ $summit->title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        .activity-card {
            transition: transform 0.3s;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .activity-card:hover {
            transform: translateY(-5px);
        }
        .day-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .time-badge {
            background: #667eea;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
        }
        .activity-type {
            font-size: 0.8em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ route('landing.index') }}">
                <i class="fas fa-mountain"></i> TUCASA STU Summit
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('landing.index') }}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('landing.summit', $summit) }}">Summit Details</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ route('landing.activities', $summit) }}">Activities</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('landing.ministers', $summit) }}">Ministers</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    @guest
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('login') }}">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('register') }}">Register</a>
                    </li>
                    @else
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            {{ Auth::user()->name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ route('dashboard') }}">Dashboard</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ route('logout') }}" 
                                   onclick="event.preventDefault(); document.getElementById('logout-form').submit();">Logout</a></li>
                        </ul>
                        <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                            @csrf
                        </form>
                    </li>
                    @endguest
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="display-4 fw-bold">Summit Activities</h1>
                    <p class="lead">{{ $summit->title }} - {{ $summit->year }}</p>
                    <p class="mb-0">{{ $summit->start_date->format('F j') }} - {{ $summit->end_date->format('F j, Y') }} | {{ $summit->venue }}</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Activities Section -->
    <section class="py-5">
        <div class="container">
            @if($activities->count() > 0)
                <div class="row">
                    @foreach($activities as $activity)
                    <div class="col-lg-6 mb-4">
                        <div class="card activity-card h-100">
                            @if($activity->image)
                            <img src="{{ Storage::url($activity->image) }}" class="card-img-top" alt="{{ $activity->title }}" style="height: 200px; object-fit: cover;">
                            @else
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                <div class="text-center">
                                    <i class="fas fa-calendar-alt fa-3x text-muted mb-2"></i>
                                    <p class="text-muted mb-0 small">{{ Str::limit($activity->title, 30) }}</p>
                                </div>
                            </div>
                            @endif
                            <div class="card-body">
                                <h5 class="card-title">{{ $activity->title }}</h5>
                                <p class="card-text">{{ $activity->description }}</p>
                                <div class="activity-details">
                                    @if($activity->date)
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ $activity->date->format('F j, Y') }}
                                    </small>
                                    <br>
                                    @endif
                                    @if($activity->time)
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ $activity->time->format('g:i A') }}
                                    </small>
                                    <br>
                                    @endif
                                    @if($activity->location)
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        {{ $activity->location }}
                                    </small>
                                    @endif
                                    @if($activity->is_featured)
                                    <br>
                                    <span class="badge bg-primary mt-2">Featured</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">No Activities Scheduled</h4>
                <p class="text-muted">Activities for this summit haven't been published yet. Check back soon!</p>
                <a href="{{ route('landing.summit', $summit) }}" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Summit Details
                </a>
            </div>
            @endif
        </div>
    </section>

    @if($activities->count() > 0)
    <!-- Call to Action -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h3>Ready to Join Us?</h3>
                    <p class="lead text-muted">Don't miss out on these amazing activities!</p>
                    @auth
                        @php
                            $userRegistration = Auth::user()->summitRegistrations()
                                ->where('summit_id', $summit->id)
                                ->whereIn('status', ['pending', 'confirmed'])
                                ->first();
                        @endphp
                        @if(!$userRegistration)
                        <a href="{{ route('registrations.create', $summit) }}" class="btn btn-primary btn-lg">
                            <i class="fas fa-user-plus me-2"></i>Register for Summit
                        </a>
                        @else
                        <div class="alert alert-success d-inline-block">
                            <i class="fas fa-check-circle me-2"></i>You're already registered for this summit!
                            <a href="{{ route('registrations.show', $userRegistration) }}" class="btn btn-sm btn-outline-success ms-2">
                                View Registration
                            </a>
                        </div>
                        @endif
                    @else
                    <a href="{{ route('register') }}" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-user-plus me-2"></i>Register for Summit
                    </a>
                    <a href="{{ route('login') }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>Login
                    </a>
                    @endauth
                </div>
            </div>
        </div>
    </section>
    @endif

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>TUCASA STU Summit</h5>
                    <p class="text-muted">Empowering students through faith and fellowship.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted">&copy; {{ date('Y') }} TUCASA STU. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
