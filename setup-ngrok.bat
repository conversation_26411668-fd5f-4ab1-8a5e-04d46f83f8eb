@echo off
echo ========================================
echo   TUCASA Summit Management - ngrok Setup
echo ========================================
echo.

echo Step 1: Starting Laravel development server...
start "Laravel Server" cmd /k "php artisan serve --host=127.0.0.1 --port=8000"
timeout /t 3 /nobreak > nul

echo.
echo Step 2: Starting ngrok tunnel...
echo.
echo IMPORTANT: After ngrok starts, you'll see a URL like:
echo https://abc123.ngrok-free.app
echo.
echo Copy that URL and:
echo 1. Update your .env file: APP_URL=https://your-ngrok-url.ngrok-free.app
echo 2. Run: php artisan config:cache
echo 3. Share the ngrok URL with your testers
echo.
echo Starting ngrok now...
ngrok http 8000

pause
