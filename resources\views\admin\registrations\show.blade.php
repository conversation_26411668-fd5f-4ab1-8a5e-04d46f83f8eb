@extends('layouts.admin')

@section('title', 'Registration Details')
@section('page-title', 'Registration Details')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">Registration Details</h2>
                <p class="text-muted">{{ $registration->summit->title }} ({{ $registration->summit->year }})</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('admin.registrations.index') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Registrations
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Registration Information -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-clipboard-list me-2"></i>Registration Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Registration Number:</strong></td>
                                <td><code>{{ $registration->registration_number }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>Summit:</strong></td>
                                <td>{{ $registration->summit->title }} ({{ $registration->summit->year }})</td>
                            </tr>
                            <tr>
                                <td><strong>Registration Date:</strong></td>
                                <td>{{ $registration->registered_at->format('F j, Y \a\t g:i A') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    <span class="badge {{ $registration->payment_complete ? 'bg-success' : 'bg-warning' }}">
                                        {{ $registration->payment_complete ? 'Paid' : 'Pending Payment' }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Total Amount:</strong></td>
                                <td><strong>TZS {{ number_format($registration->total_amount, 0) }}</strong></td>
                            </tr>
                            <tr>
                                <td><strong>Paid Amount:</strong></td>
                                <td>TZS {{ number_format($registration->paid_amount, 0) }}</td>
                            </tr>
                            <tr>
                                <td><strong>Balance:</strong></td>
                                <td>
                                    @if($registration->balance > 0)
                                        <span class="text-danger">TZS {{ number_format($registration->balance, 0) }}</span>
                                    @else
                                        <span class="text-success">TZS 0</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Payment Complete:</strong></td>
                                <td>
                                    @if($registration->payment_complete)
                                        <i class="fas fa-check-circle text-success"></i> Yes
                                    @else
                                        <i class="fas fa-times-circle text-danger"></i> No
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                @if($registration->special_requirements)
                <div class="mt-3">
                    <h6>Special Requirements</h6>
                    <div class="bg-light p-3 rounded">
                        {{ $registration->special_requirements }}
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- User Information -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>User Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Full Name:</strong></td>
                                <td>{{ $registration->user->full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>{{ $registration->user->email }}</td>
                            </tr>
                            <tr>
                                <td><strong>Phone:</strong></td>
                                <td>{{ $registration->user->phone ?? 'Not provided' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Gender:</strong></td>
                                <td>{{ $registration->user->gender ? ucfirst($registration->user->gender) : 'Not specified' }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Branch:</strong></td>
                                <td>{{ $registration->user->branch->name ?? 'Not assigned' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Zone:</strong></td>
                                <td>{{ $registration->user->branch->zone->name ?? 'Not assigned' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Conference:</strong></td>
                                <td>{{ $registration->user->branch->zone->conference->name ?? 'Not assigned' }}</td>
                            </tr>
                            <tr>
                                <td><strong>University:</strong></td>
                                <td>{{ $registration->user->university->name ?? 'Not assigned' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment History -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Payment History</h5>
            </div>
            <div class="card-body">
                @if($registration->payments->count() > 0)
                    @foreach($registration->payments as $payment)
                    <div class="border rounded p-3 mb-3">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <strong>TZS {{ number_format($payment->amount, 2) }}</strong>
                            <span class="badge {{ $payment->status === 'completed' ? 'bg-success' : ($payment->status === 'pending' ? 'bg-warning' : 'bg-danger') }}">
                                {{ ucfirst($payment->status) }}
                            </span>
                        </div>
                        <div class="small text-muted">
                            <div><strong>Reference:</strong> {{ $payment->payment_reference }}</div>
                            <div><strong>Method:</strong> {{ ucfirst($payment->payment_method) }}</div>
                            <div><strong>Date:</strong> {{ $payment->created_at->format('M j, Y g:i A') }}</div>
                            @if($payment->transaction_id)
                                <div><strong>Transaction ID:</strong> {{ $payment->transaction_id }}</div>
                            @endif
                        </div>
                    </div>
                    @endforeach
                @else
                    <div class="text-center py-3">
                        <i class="fas fa-credit-card fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">No payments recorded</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.users.show', $registration->user) }}" class="btn btn-outline-primary">
                        <i class="fas fa-user me-2"></i>View User Profile
                    </a>
                    <a href="{{ route('admin.summits.show', $registration->summit) }}" class="btn btn-outline-info">
                        <i class="fas fa-mountain me-2"></i>View Summit Details
                    </a>
                    @if($registration->payment_complete)
                        <a href="{{ route('admin.id-cards.index', ['user_id' => $registration->user->id]) }}" class="btn btn-outline-success">
                            <i class="fas fa-id-card me-2"></i>View ID Cards
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
