@extends('layouts.app')

@section('title', 'Financial Reports')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">📊 Financial Reports</h2>
                    <p class="text-muted mb-0">Comprehensive financial analysis and reporting</p>
                </div>
                <div>
                    <a href="{{ route('treasurer.dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Categories -->
    <div class="row">
        <!-- Payment Reports -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div class="rounded-circle mx-auto d-flex align-items-center justify-content-center" 
                             style="width: 80px; height: 80px; background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);">
                            <i class="fas fa-credit-card fa-2x text-success"></i>
                        </div>
                    </div>
                    <h5 class="card-title">Payment Reports</h5>
                    <p class="card-text text-muted">Detailed payment transactions, status tracking, and collection analysis</p>
                    <div class="mt-auto">
                        <a href="{{ route('treasurer.payment-reports') }}" class="btn btn-success w-100">
                            <i class="fas fa-chart-line me-2"></i>View Reports
                        </a>
                        <div class="mt-2">
                            <a href="{{ route('treasurer.export.payment-report') }}" class="btn btn-sm btn-outline-success">
                                <i class="fas fa-download me-1"></i>Export PDF
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Analytics -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div class="rounded-circle mx-auto d-flex align-items-center justify-content-center" 
                             style="width: 80px; height: 80px; background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);">
                            <i class="fas fa-chart-bar fa-2x text-warning"></i>
                        </div>
                    </div>
                    <h5 class="card-title">Revenue Analytics</h5>
                    <p class="card-text text-muted">Revenue trends, branch performance, and financial insights</p>
                    <div class="mt-auto">
                        <a href="{{ route('treasurer.analytics') }}" class="btn btn-warning w-100">
                            <i class="fas fa-analytics me-2"></i>View Analytics
                        </a>
                        <div class="mt-2">
                            <a href="{{ route('treasurer.export.revenue-summary') }}" class="btn btn-sm btn-outline-warning">
                                <i class="fas fa-download me-1"></i>Export PDF
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Outstanding Payments -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div class="rounded-circle mx-auto d-flex align-items-center justify-content-center" 
                             style="width: 80px; height: 80px; background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);">
                            <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                        </div>
                    </div>
                    <h5 class="card-title">Outstanding Payments</h5>
                    <p class="card-text text-muted">Pending payments, overdue accounts, and collection follow-ups</p>
                    <div class="mt-auto">
                        <a href="{{ route('treasurer.outstanding-payments') }}" class="btn btn-danger w-100">
                            <i class="fas fa-clock me-2"></i>View Outstanding
                        </a>
                        <div class="mt-2">
                            <a href="{{ route('admin.reports.payments') }}" class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-list me-1"></i>All Payments
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Branch Performance -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div class="rounded-circle mx-auto d-flex align-items-center justify-content-center" 
                             style="width: 80px; height: 80px; background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);">
                            <i class="fas fa-sitemap fa-2x text-info"></i>
                        </div>
                    </div>
                    <h5 class="card-title">Branch Performance</h5>
                    <p class="card-text text-muted">Branch-wise revenue, registration rates, and comparative analysis</p>
                    <div class="mt-auto">
                        <a href="{{ route('admin.reports.registrations') }}" class="btn btn-info w-100">
                            <i class="fas fa-building me-2"></i>View Performance
                        </a>
                        <div class="mt-2">
                            <a href="{{ route('admin.reports.summits') }}" class="btn btn-sm btn-outline-info">
                                <i class="fas fa-calendar me-1"></i>Summit Reports
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Summary -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div class="rounded-circle mx-auto d-flex align-items-center justify-content-center" 
                             style="width: 80px; height: 80px; background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);">
                            <i class="fas fa-calculator fa-2x text-secondary"></i>
                        </div>
                    </div>
                    <h5 class="card-title">Financial Summary</h5>
                    <p class="card-text text-muted">Comprehensive financial overview and executive summaries</p>
                    <div class="mt-auto">
                        <a href="{{ route('admin.reports.analytics') }}" class="btn btn-secondary w-100">
                            <i class="fas fa-file-alt me-2"></i>View Summary
                        </a>
                        <div class="mt-2">
                            <a href="{{ route('treasurer.export.revenue-summary', ['period' => 'yearly']) }}" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-download me-1"></i>Annual Report
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Management -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div class="rounded-circle mx-auto d-flex align-items-center justify-content-center" 
                             style="width: 80px; height: 80px; background: linear-gradient(135deg, #f0e6ff 0%, #e6ccff 100%);">
                            <i class="fas fa-users fa-2x" style="color: #6f42c1;"></i>
                        </div>
                    </div>
                    <h5 class="card-title">User Reports</h5>
                    <p class="card-text text-muted">User registration trends, demographics, and participation analysis</p>
                    <div class="mt-auto">
                        <a href="{{ route('admin.reports.users') }}" class="btn w-100" style="background: #6f42c1; color: white;">
                            <i class="fas fa-user-chart me-2"></i>View Reports
                        </a>
                        <div class="mt-2">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-sm" style="border: 1px solid #6f42c1; color: #6f42c1;">
                                <i class="fas fa-users me-1"></i>Manage Users
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2 text-primary"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('treasurer.payments.index') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-credit-card me-2"></i>Manage Payments
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('treasurer.registrations.index') }}" class="btn btn-outline-success w-100">
                                <i class="fas fa-user-plus me-2"></i>View Registrations
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('treasurer.export.payment-report', ['date_from' => now()->startOfMonth()->format('Y-m-d'), 'date_to' => now()->format('Y-m-d')]) }}" class="btn btn-outline-warning w-100">
                                <i class="fas fa-download me-2"></i>Monthly Report
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('treasurer.outstanding-payments') }}" class="btn btn-outline-danger w-100">
                                <i class="fas fa-exclamation-circle me-2"></i>Follow-ups
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .card {
        transition: transform 0.2s, box-shadow 0.2s;
    }
    
    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15) !important;
    }
    
    .btn {
        transition: all 0.2s;
    }
    
    .btn:hover {
        transform: translateY(-1px);
    }
</style>
@endpush
