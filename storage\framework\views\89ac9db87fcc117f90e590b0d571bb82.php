<?php $__env->startSection('title', 'Universities Management'); ?>
<?php $__env->startSection('page-title', 'Universities Management'); ?>

<?php $__env->startPush('styles'); ?>
<style>
.page-header {
    background-color: var(--md-surface);
    border-radius: var(--md-border-radius-lg);
    padding: var(--md-spacing-xl);
    margin-bottom: var(--md-spacing-lg);
    box-shadow: var(--md-elevation-1);
}

.page-title {
    @extend .md-headline-medium;
    margin: 0 0 var(--md-spacing-xs) 0;
    color: var(--md-on-surface);
}

.page-subtitle {
    @extend .md-body-large;
    color: var(--md-on-surface-variant);
    margin: 0;
}

.search-card {
    background-color: var(--md-surface);
    border-radius: var(--md-border-radius-md);
    padding: var(--md-spacing-lg);
    margin-bottom: var(--md-spacing-lg);
    box-shadow: var(--md-elevation-1);
}

.search-title {
    @extend .md-title-medium;
    margin: 0 0 var(--md-spacing-md) 0;
    display: flex;
    align-items: center;
    gap: var(--md-spacing-sm);
    color: var(--md-on-surface);
}

.search-form {
    display: flex;
    gap: var(--md-spacing-md);
    align-items: end;
}

.search-input-container {
    flex: 1;
}

.search-actions {
    display: flex;
    gap: var(--md-spacing-sm);
}

.data-table-card {
    background-color: var(--md-surface);
    border-radius: var(--md-border-radius-md);
    box-shadow: var(--md-elevation-1);
    overflow: hidden;
}

.table-header {
    background-color: var(--md-surface-variant);
    padding: var(--md-spacing-lg);
    border-bottom: 1px solid var(--md-outline-variant);
}

.table-title {
    @extend .md-title-medium;
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--md-spacing-sm);
    color: var(--md-on-surface);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    @extend .md-label-large;
    color: var(--md-on-surface-variant);
    padding: var(--md-spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--md-outline-variant);
    background-color: var(--md-surface-variant);
}

.data-table td {
    @extend .md-body-medium;
    padding: var(--md-spacing-md);
    border-bottom: 1px solid var(--md-outline-variant);
    color: var(--md-on-surface);
}

.data-table tr:hover {
    background-color: color-mix(in srgb, var(--md-on-surface) 4%, transparent);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: var(--md-border-radius-xl);
    @extend .md-label-small;
    font-weight: 500;
}

.status-active {
    background-color: var(--md-primary-container);
    color: var(--md-on-primary-container);
}

.status-inactive {
    background-color: var(--md-outline-variant);
    color: var(--md-on-surface-variant);
}

.count-badge {
    background-color: var(--md-secondary-container);
    color: var(--md-on-secondary-container);
    padding: 2px 8px;
    border-radius: var(--md-border-radius-sm);
    @extend .md-label-small;
    font-weight: 500;
}

.action-buttons {
    display: flex;
    gap: var(--md-spacing-xs);
}

.empty-state {
    text-align: center;
    padding: var(--md-spacing-xxl);
    color: var(--md-on-surface-variant);
}

.empty-state-icon {
    font-size: 4rem;
    margin-bottom: var(--md-spacing-lg);
    opacity: 0.5;
}

.empty-state-title {
    @extend .md-title-large;
    margin: 0 0 var(--md-spacing-sm) 0;
}

.empty-state-text {
    @extend .md-body-large;
    margin: 0;
}

@media (max-width: 768px) {
    .page-header {
        padding: var(--md-spacing-lg);
    }

    .search-form {
        flex-direction: column;
        align-items: stretch;
    }

    .search-actions {
        justify-content: stretch;
    }

    .search-actions .md-button-filled,
    .search-actions .md-button-outlined {
        flex: 1;
    }

    .data-table-card {
        overflow-x: auto;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<div class="page-header">
    <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: var(--md-spacing-md);">
        <div>
            <h1 class="page-title">Universities Management</h1>
            <p class="page-subtitle">Manage universities in the system.</p>
        </div>
        <a href="<?php echo e(route('admin.universities.create')); ?>" class="md-button-filled">
            <span class="material-icons">add</span>
            Add University
        </a>
    </div>
</div>

<!-- Search and Filter -->
<div class="search-card">
    <h2 class="search-title">
        <span class="material-icons">search</span>
        Search & Filter
    </h2>
    <form method="GET" action="<?php echo e(route('admin.universities.index')); ?>" class="search-form">
        <div class="search-input-container">
            <div class="md-text-field">
                <input
                    type="text"
                    name="search"
                    class="md-text-field-input"
                    value="<?php echo e(request('search')); ?>"
                    placeholder=" "
                >
                <label class="md-text-field-label">Search by name, code, or location...</label>
            </div>
        </div>
        <div class="search-actions">
            <button type="submit" class="md-button-filled">
                <span class="material-icons">search</span>
                Search
            </button>
            <a href="<?php echo e(route('admin.universities.index')); ?>" class="md-button-outlined">
                <span class="material-icons">clear</span>
                Clear
            </a>
        </div>
    </form>
</div>

<!-- Universities Table -->
<div class="data-table-card">
    <div class="table-header">
        <h2 class="table-title">
            <span class="material-icons">table_view</span>
            Universities (<?php echo e($universities->total()); ?>)
        </h2>
    </div>

    <?php if($universities->count() > 0): ?>
    <div style="overflow-x: auto;">
        <table class="data-table">
            <thead>
                <tr>
                    <th>University</th>
                    <th>Code</th>
                    <th>Location</th>
                    <th>Branches</th>
                    <th>Users</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php $__currentLoopData = $universities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $university): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td>
                        <div>
                            <div style="font-weight: 500; margin-bottom: 2px;"><?php echo e($university->name); ?></div>
                            <?php if($university->description): ?>
                                <div style="font-size: 12px; color: var(--md-on-surface-variant);">
                                    <?php echo e(Str::limit($university->description, 50)); ?>

                                </div>
                            <?php endif; ?>
                        </div>
                    </td>
                    <td>
                        <span class="status-badge status-active"><?php echo e($university->code); ?></span>
                    </td>
                    <td><?php echo e($university->location ?? 'N/A'); ?></td>
                    <td style="text-align: center;">
                        <span class="count-badge"><?php echo e($university->branches_count); ?></span>
                    </td>
                    <td style="text-align: center;">
                        <span class="count-badge"><?php echo e($university->users_count); ?></span>
                    </td>
                    <td>
                        <span class="status-badge <?php echo e($university->is_active ? 'status-active' : 'status-inactive'); ?>">
                            <?php echo e($university->is_active ? 'Active' : 'Inactive'); ?>

                        </span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <a href="<?php echo e(route('admin.universities.show', $university)); ?>"
                               class="md-button-text"
                               title="View Details"
                               style="padding: 8px; min-height: auto;">
                                <span class="material-icons" style="font-size: 18px;">visibility</span>
                            </a>
                            <a href="<?php echo e(route('admin.universities.edit', $university)); ?>"
                               class="md-button-text"
                               title="Edit"
                               style="padding: 8px; min-height: auto;">
                                <span class="material-icons" style="font-size: 18px;">edit</span>
                            </a>
                            <?php if(!$university->users_count): ?>
                            <form method="POST" action="<?php echo e(route('admin.universities.destroy', $university)); ?>"
                                  style="display: inline;"
                                  onsubmit="return confirm('Are you sure you want to delete this university?')">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit"
                                        class="md-button-text"
                                        title="Delete"
                                        style="padding: 8px; min-height: auto; color: var(--md-error);">
                                    <span class="material-icons" style="font-size: 18px;">delete</span>
                                </button>
                            </form>
                            <?php endif; ?>
                        </div>
                    </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div style="padding: var(--md-spacing-lg); text-align: center; border-top: 1px solid var(--md-outline-variant);">
        <?php echo e($universities->withQueryString()->links()); ?>

    </div>
    <?php else: ?>
    <div class="empty-state">
        <div class="empty-state-icon">
            <span class="material-icons" style="font-size: inherit;">school</span>
        </div>
        <h3 class="empty-state-title">No universities found</h3>
        <p class="empty-state-text">
            <?php if(request('search')): ?>
                No universities match your search criteria.
            <?php else: ?>
                Get started by adding your first university.
            <?php endif; ?>
        </p>
        <?php if(!request('search')): ?>
        <div style="margin-top: var(--md-spacing-lg);">
            <a href="<?php echo e(route('admin.universities.create')); ?>" class="md-button-filled">
                <span class="material-icons">add</span>
                Add University
            </a>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\summit-management-system\resources\views/admin/universities/index.blade.php ENDPATH**/ ?>