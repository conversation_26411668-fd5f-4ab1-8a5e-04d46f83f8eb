<?php

namespace App\Http\Controllers;

use App\Services\MealTrackingService;
use App\Models\Summit;
use App\Models\SummitActivity;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MealTrackingController extends Controller
{
    protected $mealTrackingService;

    public function __construct(MealTrackingService $mealTrackingService)
    {
        $this->middleware('auth');
        $this->mealTrackingService = $mealTrackingService;
    }

    /**
     * Show QR code scanner interface
     */
    public function scanner()
    {
        $this->authorize('scan qr codes');
        
        $summit = Summit::current()->first();
        if (!$summit) {
            return redirect()->back()->with('error', 'No active summit found.');
        }

        $activities = SummitActivity::where('summit_id', $summit->id)
            ->orderBy('start_time', 'desc')
            ->get();

        return view('meal-tracking.scanner', compact('summit', 'activities'));
    }

    /**
     * Process QR code scan for meal tracking
     */
    public function scan(Request $request)
    {
        $this->authorize('track meals');

        $validated = $request->validate([
            'card_number' => 'required|string',
            'meal_type' => 'required|in:breakfast,lunch,dinner,snack',
            'activity_id' => 'nullable|exists:summit_activities,id',
            'notes' => 'nullable|string|max:500'
        ]);

        $result = $this->mealTrackingService->scanQrCodeForMeal(
            $validated['card_number'],
            $validated['meal_type'],
            $validated['activity_id'],
            Auth::user()->full_name
        );

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'data' => [
                    'user_name' => $result['user']->full_name,
                    'meal_type' => $validated['meal_type'],
                    'scanned_at' => $result['meal_tracking']->scanned_at->format('Y-m-d H:i:s'),
                    'summit_title' => $result['summit']->title
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => $result['message'],
            'error_code' => $result['error_code'] ?? 'UNKNOWN_ERROR'
        ], 400);
    }

    /**
     * Show meal tracking dashboard
     */
    public function dashboard()
    {
        $this->authorize('view meal reports');

        $summit = Summit::current()->first();
        if (!$summit) {
            return redirect()->back()->with('error', 'No active summit found.');
        }

        $stats = $this->mealTrackingService->getMealTrackingStats($summit);
        $dailyReport = $this->mealTrackingService->getDailyMealReport($summit);

        return view('meal-tracking.dashboard', compact('summit', 'stats', 'dailyReport'));
    }

    /**
     * Show meal tracking reports
     */
    public function reports(Request $request)
    {
        $this->authorize('view meal reports');

        $summit = Summit::current()->first();
        if (!$summit) {
            return redirect()->back()->with('error', 'No active summit found.');
        }

        $date = $request->input('date') ? \Carbon\Carbon::parse($request->input('date')) : today();
        $dailyReport = $this->mealTrackingService->getDailyMealReport($summit, $date);
        $stats = $this->mealTrackingService->getMealTrackingStats($summit);

        return view('meal-tracking.reports', compact('summit', 'dailyReport', 'stats', 'date'));
    }

    /**
     * API endpoint to verify card and check meal eligibility
     */
    public function checkEligibility(Request $request)
    {
        $validated = $request->validate([
            'card_number' => 'required|string',
            'meal_type' => 'required|in:breakfast,lunch,dinner,snack'
        ]);

        // First verify the card
        $idCardService = app(\App\Services\IdCardService::class);
        $idCard = $idCardService->verifyCard($validated['card_number']);

        if (!$idCard) {
            return response()->json([
                'valid' => false,
                'message' => 'Invalid or expired ID card.'
            ], 404);
        }

        // Check meal eligibility
        $eligibility = $this->mealTrackingService->canUserHaveMeal(
            $idCard->user,
            $idCard->summit,
            $validated['meal_type']
        );

        return response()->json([
            'valid' => true,
            'user' => [
                'name' => $idCard->user->full_name,
                'email' => $idCard->user->email,
                'branch' => $idCard->user->branch->name ?? 'N/A'
            ],
            'summit' => [
                'title' => $idCard->summit->title,
                'year' => $idCard->summit->year
            ],
            'meal_eligibility' => $eligibility
        ]);
    }

    /**
     * Get user's meal history
     */
    public function userHistory(Request $request)
    {
        $validated = $request->validate([
            'card_number' => 'required|string'
        ]);

        $idCardService = app(\App\Services\IdCardService::class);
        $idCard = $idCardService->verifyCard($validated['card_number']);

        if (!$idCard) {
            return response()->json([
                'valid' => false,
                'message' => 'Invalid or expired ID card.'
            ], 404);
        }

        $history = $this->mealTrackingService->getUserMealHistory($idCard->user, $idCard->summit);

        return response()->json([
            'valid' => true,
            'user' => [
                'name' => $idCard->user->full_name,
                'email' => $idCard->user->email
            ],
            'meal_history' => $history
        ]);
    }

    /**
     * Manual meal tracking (for admin use)
     */
    public function manualTrack(Request $request)
    {
        $this->authorize('track meals');

        $validated = $request->validate([
            'card_number' => 'required|string',
            'meal_type' => 'required|in:breakfast,lunch,dinner,snack',
            'activity_id' => 'nullable|exists:summit_activities,id',
            'notes' => 'nullable|string|max:500'
        ]);

        $result = $this->mealTrackingService->scanQrCodeForMeal(
            $validated['card_number'],
            $validated['meal_type'],
            $validated['activity_id'],
            Auth::user()->full_name . ' (Manual)'
        );

        if ($result['success']) {
            return redirect()->back()->with('success', $result['message']);
        }

        return redirect()->back()->with('error', $result['message']);
    }
}
