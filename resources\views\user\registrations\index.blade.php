@extends('layouts.user')

@section('title', 'My Registrations')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <h2 class="mb-0">My Summit Registrations</h2>
        <p class="text-muted">View and manage your summit registrations.</p>
    </div>
</div>

@if($registrations->count() > 0)
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-clipboard-list me-2"></i>Registration History</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Summit</th>
                                <th>Registration #</th>
                                <th>Status</th>
                                <th>Amount</th>
                                <th>Payment Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($registrations as $registration)
                            <tr class="{{ $registration->status === 'cancelled' ? 'table-secondary' : '' }}">
                                <td>
                                    <div>
                                        <div class="fw-bold {{ $registration->status === 'cancelled' ? 'text-muted' : '' }}">
                                            {{ $registration->summit->title }}
                                            @if($registration->status === 'cancelled')
                                                <i class="fas fa-times-circle text-danger ms-1" title="Cancelled"></i>
                                            @endif
                                        </div>
                                        <small class="text-muted">{{ $registration->summit->year }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary">#{{ $registration->registration_number }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $registration->status === 'confirmed' ? 'success' : ($registration->status === 'cancelled' ? 'danger' : 'warning') }}">
                                        {{ ucfirst($registration->status) }}
                                    </span>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-bold {{ $registration->status === 'cancelled' ? 'text-muted' : '' }}">
                                            TSh {{ number_format($registration->total_amount) }}
                                        </div>
                                        @if($registration->paid_amount > 0 && $registration->status !== 'cancelled')
                                            <small class="text-success">Paid: TSh {{ number_format($registration->paid_amount) }}</small>
                                        @endif
                                        @if($registration->balance > 0 && $registration->status !== 'cancelled')
                                            <small class="text-warning">Balance: TSh {{ number_format($registration->balance) }}</small>
                                        @endif
                                        @if($registration->status === 'cancelled')
                                            <small class="text-muted">Registration cancelled</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    @if($registration->status === 'cancelled')
                                        <span class="badge bg-secondary">Cancelled</span>
                                    @else
                                        <span class="badge bg-{{ $registration->payment_complete ? 'success' : 'warning' }}">
                                            {{ $registration->payment_complete ? 'Paid' : 'Pending' }}
                                        </span>
                                    @endif
                                </td>
                                <td>{{ $registration->created_at->format('M j, Y') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('registrations.show', $registration) }}"
                                           class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if($registration->balance > 0 && $registration->status !== 'cancelled')
                                        <a href="{{ route('payments.create', $registration) }}"
                                           class="btn btn-outline-success" title="Make Payment">
                                            <i class="fas fa-credit-card"></i>
                                        </a>
                                        @endif
                                        @if($registration->canBeCancelledByUser())
                                        <button type="button" class="btn btn-outline-danger"
                                                onclick="cancelRegistration({{ $registration->id }})" title="Cancel Registration">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $registrations->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
@else
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Registrations Yet</h5>
                <p class="text-muted">You haven't registered for any summits yet.</p>
                <a href="{{ route('landing.index') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Register for Summit
                </a>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Cancel Registration Modal -->
<div class="modal fade" id="cancelRegistrationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Registration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to cancel this registration?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> This action cannot be undone. You will need to register again if you change your mind.
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Note:</strong> Any pending or failed payments will also be cancelled automatically.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Keep Registration</button>
                <form id="cancelRegistrationForm" method="POST" style="display: inline;">
                    @csrf
                    @method('PATCH')
                    <button type="submit" class="btn btn-danger">Cancel Registration</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function cancelRegistration(registrationId) {
    const form = document.getElementById('cancelRegistrationForm');
    form.action = `/registrations/${registrationId}/cancel`;

    const modal = new bootstrap.Modal(document.getElementById('cancelRegistrationModal'));
    modal.show();
}
</script>
@endsection
