@extends('layouts.admin')

@section('title', 'Role Details - ' . ucwords(str_replace('_', ' ', $role->name)))

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        @switch($role->name)
                            @case('admin')
                                <i class="fas fa-crown text-warning me-2"></i>
                                @break
                            @case('treasurer')
                                <i class="fas fa-coins text-success me-2"></i>
                                @break
                            @case('conference_admin')
                                <i class="fas fa-building text-primary me-2"></i>
                                @break
                            @case('zone_admin')
                                <i class="fas fa-map-marked-alt text-info me-2"></i>
                                @break
                            @case('branch_admin')
                                <i class="fas fa-sitemap text-secondary me-2"></i>
                                @break
                            @case('moderator')
                                <i class="fas fa-user-shield text-purple me-2"></i>
                                @break
                            @default
                                <i class="fas fa-user text-muted me-2"></i>
                        @endswitch
                        {{ ucwords(str_replace('_', ' ', $role->name)) }} Role
                    </h2>
                    <p class="text-muted mb-0">Role details and user assignments</p>
                </div>
                <div>
                    <a href="{{ route('admin.superadmin.roles.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Roles
                    </a>
                    <a href="{{ route('admin.superadmin.roles.user-roles', ['role' => $role->name]) }}" class="btn btn-primary">
                        <i class="fas fa-users me-2"></i>Manage Users
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Role Information -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Role Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Role Name</label>
                        <div class="fw-bold">{{ $role->name }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">Display Name</label>
                        <div class="fw-bold">{{ ucwords(str_replace('_', ' ', $role->name)) }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">Total Users</label>
                        <div class="fw-bold">
                            <span class="badge bg-primary fs-6">{{ $role->users->count() }}</span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">Total Permissions</label>
                        <div class="fw-bold">
                            <span class="badge bg-success fs-6">{{ $role->permissions->count() }}</span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">Created</label>
                        <div class="fw-bold">{{ $role->created_at->format('M d, Y') }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permissions -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-key me-2"></i>Permissions ({{ $role->permissions->count() }})
                    </h5>
                    @if($availablePermissions->count() > 0)
                    <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#addPermissionModal">
                        <i class="fas fa-plus me-2"></i>Add Permission
                    </button>
                    @endif
                </div>
                <div class="card-body">
                    @if($role->permissions->count() > 0)
                    <div class="row">
                        @foreach($role->permissions->groupBy(function($permission) {
                            return explode(' ', $permission->name)[0];
                        }) as $group => $groupPermissions)
                        <div class="col-md-6 mb-4">
                            <h6 class="text-primary border-bottom pb-2">
                                <i class="fas fa-folder me-2"></i>{{ ucfirst($group) }}
                            </h6>
                            @foreach($groupPermissions as $permission)
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">{{ $permission->name }}</span>
                                @if(!in_array($role->name, ['admin']) || $permission->name !== 'manage system settings')
                                <form method="POST" action="{{ route('admin.superadmin.roles.remove-permission', $role) }}"
                                      class="d-inline" onsubmit="return confirm('Remove this permission?')">
                                    @csrf
                                    <input type="hidden" name="permission" value="{{ $permission->name }}">
                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </form>
                                @endif
                            </div>
                            @endforeach
                        </div>
                        @endforeach
                    </div>
                    @else
                    <div class="text-center py-4">
                        <i class="fas fa-key fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No permissions assigned to this role.</p>
                        @if($availablePermissions->count() > 0)
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPermissionModal">
                            <i class="fas fa-plus me-2"></i>Add First Permission
                        </button>
                        @endif
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Users with this Role -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>Users with {{ ucwords(str_replace('_', ' ', $role->name)) }} Role ({{ $role->users->count() }})
                    </h5>
                </div>
                <div class="card-body">
                    @if($role->users->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Email</th>
                                    <th>Branch</th>
                                    <th>Admin Level</th>
                                    <th>Status</th>
                                    <th>Assigned</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($role->users as $user)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-3">
                                                @if($user->profile_image)
                                                <img src="{{ Storage::url($user->profile_image) }}" 
                                                     class="rounded-circle" width="40" height="40" alt="Avatar">
                                                @else
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                                     style="width: 40px; height: 40px;">
                                                    <span class="text-white fw-bold">{{ substr($user->first_name, 0, 1) }}</span>
                                                </div>
                                                @endif
                                            </div>
                                            <div>
                                                <strong>{{ $user->name }}</strong>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ $user->email }}</td>
                                    <td>
                                        @if($user->branch)
                                        <small class="text-muted">
                                            {{ $user->branch->name }}<br>
                                            {{ $user->branch->zone->name }} - {{ $user->branch->zone->conference->name }}
                                        </small>
                                        @else
                                        <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($user->admin_conference_id)
                                            <small class="text-primary">
                                                <i class="fas fa-building me-1"></i>{{ $user->adminConference->name ?? 'N/A' }}
                                            </small>
                                        @elseif($user->admin_zone_id)
                                            <small class="text-info">
                                                <i class="fas fa-map-marked-alt me-1"></i>{{ $user->adminZone->name ?? 'N/A' }}
                                            </small>
                                        @elseif($user->admin_branch_id)
                                            <small class="text-secondary">
                                                <i class="fas fa-sitemap me-1"></i>{{ $user->adminBranch->name ?? 'N/A' }}
                                            </small>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($user->is_active)
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ $user->pivot->created_at ? $user->pivot->created_at->format('M d, Y') : 'N/A' }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.users.show', $user) }}" 
                                               class="btn btn-sm btn-outline-primary" title="View User">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="removeUserRole({{ $user->id }}, '{{ $role->name }}', '{{ $user->name }}')"
                                                    title="Remove Role">
                                                <i class="fas fa-user-minus"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @else
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No users assigned to this role.</p>
                        <a href="{{ route('admin.superadmin.roles.user-roles') }}" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i>Assign Users
                        </a>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Permission Modal -->
@if($availablePermissions->count() > 0)
<div class="modal fade" id="addPermissionModal" tabindex="-1" aria-labelledby="addPermissionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ route('admin.superadmin.roles.add-permission', $role) }}">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="addPermissionModalLabel">
                        <i class="fas fa-plus me-2"></i>Add Permission
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="permission" class="form-label">Permission</label>
                        <select class="form-select" id="permission" name="permission" required>
                            <option value="">Select Permission</option>
                            @foreach($availablePermissions->groupBy(function($permission) {
                                return explode(' ', $permission->name)[0];
                            }) as $group => $groupPermissions)
                            <optgroup label="{{ ucfirst($group) }}">
                                @foreach($groupPermissions as $permission)
                                <option value="{{ $permission->name }}">{{ $permission->name }}</option>
                                @endforeach
                            </optgroup>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>Add Permission
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif
@endsection

@push('styles')
<style>
.text-purple {
    color: #6f42c1 !important;
}

.avatar-sm img {
    object-fit: cover;
}
</style>
@endpush

@push('scripts')
<script>
function removeUserRole(userId, roleName, userName) {
    if (confirm(`Are you sure you want to remove the "${roleName}" role from ${userName}?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("admin.superadmin.roles.remove") }}';
        
        form.innerHTML = `
            @csrf
            <input type="hidden" name="user_id" value="${userId}">
            <input type="hidden" name="role" value="${roleName}">
        `;
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
