@extends('layouts.app')

@section('title', 'Meal Tracking Reports')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-chart-bar me-2"></i>
                    Meal Tracking Reports - {{ $summit->title }}
                </h2>
                <div>
                    <a href="{{ route('meal-tracking.dashboard') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                    <a href="{{ route('meal-tracking.scanner') }}" class="btn btn-primary">
                        <i class="fas fa-qrcode me-2"></i>QR Scanner
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('meal-tracking.reports') }}">
                        <div class="row align-items-end">
                            <div class="col-md-3">
                                <label for="date" class="form-label">Report Date</label>
                                <input type="date" class="form-control" id="date" name="date" 
                                       value="{{ $date->format('Y-m-d') }}">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>Filter
                                </button>
                            </div>
                            <div class="col-md-7 text-end">
                                <small class="text-muted">
                                    Showing data for: <strong>{{ $date->format('F j, Y') }}</strong>
                                </small>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        @foreach(['breakfast', 'lunch', 'dinner', 'snack'] as $mealType)
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: {{ $mealType === 'breakfast' ? '#e6f4ea' : ($mealType === 'lunch' ? '#fff7e6' : ($mealType === 'dinner' ? '#e6f0ff' : '#f0e6ff')) }};">
                                <i class="fas fa-{{ $mealType === 'breakfast' ? 'coffee' : ($mealType === 'lunch' ? 'hamburger' : ($mealType === 'dinner' ? 'utensils' : 'cookie-bite')) }} fa-2x" 
                                   style="color: {{ $mealType === 'breakfast' ? '#28a745' : ($mealType === 'lunch' ? '#ffc107' : ($mealType === 'dinner' ? '#007bff' : '#6f42c1')) }};"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1 text-capitalize">{{ $mealType }}</h6>
                            <h3 class="mb-0">{{ $dailyReport[$mealType]['count'] ?? 0 }}</h3>
                            <small class="text-muted">
                                <i class="fas fa-calendar-day"></i> Today's count
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Overall Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h6>Total Meals Served</h6>
                    <h3>{{ collect($dailyReport)->sum('count') }}</h3>
                    <small><i class="fas fa-utensils"></i> {{ $date->format('M j, Y') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h6>Unique Participants</h6>
                    <h3>{{ collect($dailyReport)->flatMap(function($meal) { return $meal['trackings']; })->unique('user_id')->count() }}</h3>
                    <small><i class="fas fa-users"></i> Fed today</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h6>Peak Meal</h6>
                    <h3>{{ collect($dailyReport)->sortByDesc('count')->keys()->first() ?? 'N/A' }}</h3>
                    <small><i class="fas fa-chart-line"></i> Most popular</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <h6>Average per Meal</h6>
                    <h3>{{ collect($dailyReport)->avg('count') > 0 ? number_format(collect($dailyReport)->avg('count'), 0) : 0 }}</h3>
                    <small><i class="fas fa-calculator"></i> Participants</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Reports -->
    <div class="row">
        @foreach($dailyReport as $mealType => $data)
        @if($data['count'] > 0)
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0 text-capitalize">
                        <i class="fas fa-{{ $mealType === 'breakfast' ? 'coffee' : ($mealType === 'lunch' ? 'hamburger' : ($mealType === 'dinner' ? 'utensils' : 'cookie-bite')) }} me-2"></i>
                        {{ $mealType }} Report ({{ $data['count'] }})
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>Participant</th>
                                    <th>Branch</th>
                                    <th>Activity</th>
                                    <th>Scanned By</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($data['trackings'] as $tracking)
                                <tr>
                                    <td>
                                        <small class="fw-bold">{{ $tracking->scanned_at->format('H:i') }}</small>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $tracking->user->full_name }}</strong><br>
                                            <small class="text-muted">{{ $tracking->user->email }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <small>{{ $tracking->user->branch->name ?? 'N/A' }}</small>
                                    </td>
                                    <td>
                                        <small>{{ $tracking->summitActivity->title ?? 'General' }}</small>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ $tracking->scanned_by }}</small>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        @endif
        @endforeach
    </div>

    @if(collect($dailyReport)->sum('count') == 0)
    <!-- No Data State -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-utensils fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">No Meal Data</h4>
                    <p class="text-muted">No meals were tracked for {{ $date->format('F j, Y') }}.</p>
                    <div class="mt-4">
                        <a href="{{ route('meal-tracking.scanner') }}" class="btn btn-primary me-2">
                            <i class="fas fa-qrcode me-2"></i>Start Scanning
                        </a>
                        <a href="{{ route('meal-tracking.reports', ['date' => today()->format('Y-m-d')]) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-calendar-day me-2"></i>Today's Report
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection

@push('styles')
<style>
    .card {
        transition: transform 0.2s, box-shadow 0.2s;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }
    
    .table th {
        font-weight: 600;
        color: #6c757d;
        font-size: 0.875rem;
    }
</style>
@endpush
