<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('id_cards', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('summit_id')->constrained()->onDelete('cascade');
            $table->string('card_number')->unique();
            $table->string('qr_code');
            $table->string('qr_code_path')->nullable();
            $table->boolean('is_active')->default(true);
            $table->datetime('issued_at');
            $table->datetime('expires_at')->nullable();
            $table->timestamps();

            $table->unique(['user_id', 'summit_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('id_cards');
    }
};
