<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\IdCard;
use App\Models\Summit;
use App\Models\User;
use App\Services\IdCardService;
use Illuminate\Http\Request;

class IdCardController extends Controller
{
    protected $idCardService;

    public function __construct(IdCardService $idCardService)
    {
        $this->middleware(['auth', 'role:admin']);
        $this->idCardService = $idCardService;
    }

    /**
     * Display a listing of ID cards
     */
    public function index(Request $request)
    {
        $query = IdCard::with(['user', 'summit']);

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('card_number', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('first_name', 'like', "%{$search}%")
                               ->orWhere('last_name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('summit_id')) {
            $query->where('summit_id', $request->summit_id);
        }

        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true)->where('expires_at', '>', now());
            } elseif ($request->status === 'expired') {
                $query->where('expires_at', '<', now());
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        $idCards = $query->orderBy('issued_at', 'desc')->paginate(15);
        $summits = Summit::orderBy('year', 'desc')->get();
        $statistics = $this->idCardService->getCardStatistics();

        return view('admin.id-cards.index', compact('idCards', 'summits', 'statistics'));
    }

    /**
     * Show specific ID card
     */
    public function show(IdCard $idCard)
    {
        $idCard->load(['user.branch.zone.conference', 'summit']);
        
        return view('admin.id-cards.show', compact('idCard'));
    }

    /**
     * Deactivate ID card
     */
    public function deactivate(IdCard $idCard)
    {
        $this->idCardService->deactivateCard($idCard);

        return back()->with('success', 'ID card deactivated successfully.');
    }

    /**
     * Reactivate ID card
     */
    public function reactivate(IdCard $idCard)
    {
        $idCard->update(['is_active' => true]);

        return back()->with('success', 'ID card reactivated successfully.');
    }

    /**
     * Generate ID card for user
     */
    public function generate(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'summit_id' => 'required|exists:summits,id'
        ]);

        $user = User::findOrFail($request->user_id);
        $summit = Summit::findOrFail($request->summit_id);

        if (!$this->idCardService->canGenerateCard($user, $summit)) {
            return back()->with('error', 'User must complete payment for the summit before generating an ID card.');
        }

        try {
            $idCard = $this->idCardService->generateIdCard($user, $summit);
            
            if (!$idCard) {
                return back()->with('error', 'Unable to generate ID card. Please ensure the user has completed payment.');
            }

            return redirect()->route('admin.id-cards.show', $idCard)
                ->with('success', 'ID card generated successfully for ' . $user->full_name);
                
        } catch (\Exception $e) {
            return back()->with('error', 'An error occurred while generating the ID card.');
        }
    }

    /**
     * Bulk operations
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:deactivate,reactivate,delete',
            'card_ids' => 'required|array',
            'card_ids.*' => 'exists:id_cards,id'
        ]);

        $cards = IdCard::whereIn('id', $request->card_ids)->get();
        $count = 0;

        foreach ($cards as $card) {
            switch ($request->action) {
                case 'deactivate':
                    if ($card->is_active) {
                        $this->idCardService->deactivateCard($card);
                        $count++;
                    }
                    break;
                case 'reactivate':
                    if (!$card->is_active) {
                        $card->update(['is_active' => true]);
                        $count++;
                    }
                    break;
                case 'delete':
                    $card->delete();
                    $count++;
                    break;
            }
        }

        $action = ucfirst($request->action);
        return back()->with('success', "{$action}d {$count} ID card(s) successfully.");
    }

    /**
     * Export ID cards data
     */
    public function export(Request $request)
    {
        $query = IdCard::with(['user', 'summit']);

        if ($request->filled('summit_id')) {
            $query->where('summit_id', $request->summit_id);
        }

        $idCards = $query->get();

        $csvData = "Card Number,User Name,Email,Summit,Branch,Conference,Status,Issued Date,Expires Date\n";
        
        foreach ($idCards as $card) {
            $status = $card->is_active ? ($card->expires_at > now() ? 'Active' : 'Expired') : 'Inactive';
            $csvData .= sprintf(
                '"%s","%s","%s","%s","%s","%s","%s","%s","%s"' . "\n",
                $card->card_number,
                $card->user->full_name,
                $card->user->email,
                $card->summit->title,
                $card->user->branch->name ?? 'N/A',
                $card->user->branch->zone->conference->name ?? 'N/A',
                $status,
                $card->issued_at->format('Y-m-d'),
                $card->expires_at->format('Y-m-d')
            );
        }

        return response($csvData)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', 'attachment; filename="id-cards-export.csv"');
    }

    /**
     * Show statistics
     */
    public function statistics()
    {
        $statistics = $this->idCardService->getCardStatistics();
        
        // Additional statistics
        $cardsByMonth = IdCard::selectRaw('EXTRACT(MONTH FROM issued_at) as month, COUNT(*) as count')
            ->whereRaw('EXTRACT(YEAR FROM issued_at) = ?', [date('Y')])
            ->groupBy('month')
            ->pluck('count', 'month')
            ->toArray();

        $cardsBySummit = IdCard::join('summits', 'id_cards.summit_id', '=', 'summits.id')
            ->selectRaw('summits.title, COUNT(*) as count')
            ->groupBy('summits.id', 'summits.title')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();

        return view('admin.id-cards.statistics', compact('statistics', 'cardsByMonth', 'cardsBySummit'));
    }
}
