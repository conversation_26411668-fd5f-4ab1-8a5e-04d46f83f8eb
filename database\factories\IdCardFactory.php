<?php

namespace Database\Factories;

use App\Models\IdCard;
use App\Models\User;
use App\Models\Summit;
use Illuminate\Database\Eloquent\Factories\Factory;

class IdCardFactory extends Factory
{
    protected $model = IdCard::class;

    public function definition(): array
    {
        $cardNumber = 'CARD-' . strtoupper($this->faker->bothify('??##??##'));
        
        return [
            'user_id' => User::factory(),
            'summit_id' => Summit::factory(),
            'card_number' => $cardNumber,
            'qr_code' => json_encode([
                'card_number' => $cardNumber,
                'user_id' => 1,
                'summit_id' => 1,
                'name' => $this->faker->name(),
                'email' => $this->faker->email(),
                'issued_at' => now()->toISOString(),
            ]),
            'qr_code_path' => 'qr-codes/' . $cardNumber . '.png',
            'is_active' => true,
            'issued_at' => now(),
            'expires_at' => now()->addMonths(6),
        ];
    }

    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => now()->subDays(1),
        ]);
    }
}
