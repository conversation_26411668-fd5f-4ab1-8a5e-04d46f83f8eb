@extends('layouts.admin')

@section('title', 'Users Management')
@section('page-title', 'Users Management')

@section('content')
<div class="row mb-4">
    <div class="col-md-6">
        <h3 class="mb-0">Users</h3>
        <p class="text-muted">Manage system users and their permissions</p>
    </div>
    <div class="col-md-6 text-end">
        <a href="{{ route('admin.users.create') }}" class="btn btn-gradient">
            <i class="fas fa-plus me-2"></i>Add New User
        </a>
    </div>
</div>

<!-- Enhanced Search -->
<x-enhanced-search
    type="users"
    placeholder="Search users by name, email, phone, student ID..."
    route="{{ route('admin.users.index') }}"
    :filters="[
        [
            'name' => 'search',
            'label' => 'Traditional Search',
            'type' => 'text',
            'placeholder' => 'Name, email, phone...',
            'width' => 3
        ],
        [
            'name' => 'branch_id',
            'label' => 'Branch',
            'type' => 'select',
            'placeholder' => 'All Branches',
            'width' => 3,
            'options' => $branches->pluck('name', 'id')->toArray()
        ],
        [
            'name' => 'university_id',
            'label' => 'University',
            'type' => 'select',
            'placeholder' => 'All Universities',
            'width' => 3,
            'options' => $universities->pluck('name', 'id')->toArray()
        ],
        [
            'name' => 'role',
            'label' => 'Role',
            'type' => 'select',
            'placeholder' => 'All Roles',
            'width' => 3,
            'options' => ['admin' => 'Admin', 'treasurer' => 'Treasurer', 'moderator' => 'Moderator', 'user' => 'User']
        ]
    ]"
/>

<!-- Traditional Filters (Hidden by default, shown when needed) -->
<div class="card mb-4 d-none" id="traditional-filters">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.users.index') }}">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="{{ request('search') }}" placeholder="Name, email, phone...">
                </div>
                <div class="col-md-3">
                    <label for="branch_id" class="form-label">Branch</label>
                    <select class="form-select" id="branch_id" name="branch_id">
                        <option value="">All Branches</option>
                        @foreach($branches as $branch)
                        <option value="{{ $branch->id }}" {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                            {{ $branch->name }} ({{ $branch->zone->name }})
                        </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="university_id" class="form-label">University</label>
                    <select class="form-select" id="university_id" name="university_id">
                        <option value="">All Universities</option>
                        @foreach($universities as $university)
                        <option value="{{ $university->id }}" {{ request('university_id') == $university->id ? 'selected' : '' }}>
                            {{ $university->name }}
                        </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="role" class="form-label">Role</label>
                    <select class="form-select" id="role" name="role">
                        <option value="">All Roles</option>
                        @foreach($roles as $role)
                        <option value="{{ $role->name }}" {{ request('role') == $role->name ? 'selected' : '' }}>
                            {{ ucfirst($role->name) }}
                        </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>Filter
                    </button>
                    <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>Clear
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-users me-2"></i>Users List 
            <span class="badge bg-primary">{{ $users->total() }}</span>
        </h5>
    </div>
    <div class="card-body">
        @if($users->count() > 0)
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>User</th>
                        <th>Contact</th>
                        <th>Organization</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Joined</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($users as $user)
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                @if($user->profile_image)
                                    <img src="{{ Storage::url($user->profile_image) }}" 
                                         alt="Profile" class="rounded-circle me-3" 
                                         style="width: 40px; height: 40px; object-fit: cover;">
                                @else
                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" 
                                         style="width: 40px; height: 40px;">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                @endif
                                <div>
                                    <div class="fw-bold">{{ $user->full_name }}</div>
                                    @if($user->student_id)
                                    <small class="text-muted">ID: {{ $user->student_id }}</small>
                                    @endif
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>{{ $user->email }}</div>
                            @if($user->phone)
                            <small class="text-muted">{{ $user->phone }}</small>
                            @endif
                        </td>
                        <td>
                            @if($user->branch)
                            <div>{{ $user->branch->name }}</div>
                            <small class="text-muted">{{ $user->branch->zone->name }}, {{ $user->branch->zone->conference->name }}</small>
                            @else
                            <span class="text-muted">Not assigned</span>
                            @endif
                        </td>
                        <td>
                            @if($user->roles->count() > 0)
                                @foreach($user->roles as $role)
                                <span class="badge bg-{{ $role->name === 'admin' ? 'danger' : ($role->name === 'treasurer' ? 'warning' : 'info') }}">
                                    {{ ucfirst($role->name) }}
                                </span>
                                @endforeach
                            @else
                            <span class="badge bg-secondary">User</span>
                            @endif
                        </td>
                        <td>
                            <span class="badge bg-{{ $user->is_active ? 'success' : 'danger' }}">
                                {{ $user->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </td>
                        <td>{{ $user->created_at->format('M j, Y') }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ route('admin.users.show', $user) }}"
                                   class="btn btn-outline-primary" title="View">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('admin.users.manage-roles', $user) }}"
                                   class="btn btn-outline-warning" title="Manage Roles">
                                    <i class="fas fa-user-shield"></i>
                                </a>
                                <a href="{{ route('admin.users.edit', $user) }}"
                                   class="btn btn-outline-info" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                @if($user->id !== Auth::id())
                                <button type="button" class="btn btn-outline-danger"
                                        title="Delete" onclick="deleteUser({{ $user->id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="d-flex justify-content-between align-items-center mt-4 p-3 bg-light rounded">
            <div class="text-muted">
                <i class="fas fa-info-circle me-2"></i>
                Showing <strong>{{ $users->firstItem() ?? 0 }}</strong> to <strong>{{ $users->lastItem() ?? 0 }}</strong>
                of <strong>{{ $users->total() }}</strong> results
            </div>
            <div>
                {{ $users->appends(request()->query())->links('pagination::bootstrap-4') }}
            </div>
        </div>
        @else
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No Users Found</h5>
            <p class="text-muted">No users match your current filters.</p>
            <a href="{{ route('admin.users.create') }}" class="btn btn-gradient">
                <i class="fas fa-plus me-2"></i>Add First User
            </a>
        </div>
        @endif
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this user? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function deleteUser(userId) {
    const form = document.getElementById('deleteForm');
    form.action = `/admin/users/${userId}`;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
@endpush
