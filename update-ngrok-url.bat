@echo off
echo ========================================
echo   Update ngrok URL Configuration
echo ========================================
echo.

set /p NGROK_URL="Enter your ngrok URL (e.g., https://abc123.ngrok-free.app): "

if "%NGROK_URL%"=="" (
    echo Error: No URL provided!
    pause
    exit /b 1
)

echo.
echo Updating .env file with URL: %NGROK_URL%
echo.

:: Create a temporary PowerShell script to update the .env file
echo $content = Get-Content '.env' > temp_update.ps1
echo $content = $content -replace '^APP_URL=.*', 'APP_URL=%NGROK_URL%' >> temp_update.ps1
echo $content ^| Set-Content '.env' >> temp_update.ps1

powershell -ExecutionPolicy Bypass -File temp_update.ps1
del temp_update.ps1

echo Building assets for production...
npm run build

echo Clearing Laravel configuration cache...
php artisan config:cache

echo Clearing all caches...
php artisan cache:clear
php artisan view:clear
php artisan route:clear

echo.
echo ========================================
echo Configuration updated successfully!
echo.
echo Your app is now accessible at: %NGROK_URL%
echo.
echo Share this URL with your testers:
echo %NGROK_URL%
echo.
echo Test features available:
echo - Landing page: %NGROK_URL%
echo - Registration: %NGROK_URL%/register
echo - Login: %NGROK_URL%/login
echo - Dashboard: %NGROK_URL%/dashboard
echo ========================================
pause
