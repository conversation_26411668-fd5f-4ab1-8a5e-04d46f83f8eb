// Material Design 3 Color System
:root {
  // Primary Colors
  --md-primary: #6750a4;
  --md-on-primary: #ffffff;
  --md-primary-container: #eaddff;
  --md-on-primary-container: #21005d;
  
  // Secondary Colors
  --md-secondary: #625b71;
  --md-on-secondary: #ffffff;
  --md-secondary-container: #e8def8;
  --md-on-secondary-container: #1d192b;
  
  // Tertiary Colors
  --md-tertiary: #7d5260;
  --md-on-tertiary: #ffffff;
  --md-tertiary-container: #ffd8e4;
  --md-on-tertiary-container: #31111d;
  
  // Error Colors
  --md-error: #ba1a1a;
  --md-on-error: #ffffff;
  --md-error-container: #ffdad6;
  --md-on-error-container: #410002;
  
  // Surface Colors
  --md-surface: #fffbfe;
  --md-on-surface: #1c1b1f;
  --md-surface-variant: #e7e0ec;
  --md-on-surface-variant: #49454f;
  --md-outline: #79747e;
  --md-outline-variant: #cab6cf;
  
  // Background
  --md-background: #fffbfe;
  --md-on-background: #1c1b1f;
  
  // Elevation
  --md-elevation-1: 0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.24);
  --md-elevation-2: 0px 3px 6px rgba(0, 0, 0, 0.16), 0px 3px 6px rgba(0, 0, 0, 0.23);
  --md-elevation-3: 0px 10px 20px rgba(0, 0, 0, 0.19), 0px 6px 6px rgba(0, 0, 0, 0.23);
  --md-elevation-4: 0px 14px 28px rgba(0, 0, 0, 0.25), 0px 10px 10px rgba(0, 0, 0, 0.22);
  --md-elevation-5: 0px 19px 38px rgba(0, 0, 0, 0.30), 0px 15px 12px rgba(0, 0, 0, 0.22);
  
  // Border Radius
  --md-border-radius-xs: 4px;
  --md-border-radius-sm: 8px;
  --md-border-radius-md: 12px;
  --md-border-radius-lg: 16px;
  --md-border-radius-xl: 28px;
  
  // Spacing
  --md-spacing-xs: 4px;
  --md-spacing-sm: 8px;
  --md-spacing-md: 16px;
  --md-spacing-lg: 24px;
  --md-spacing-xl: 32px;
  --md-spacing-xxl: 48px;
}

// Typography
.md-headline-large {
  font-family: 'Roboto', sans-serif;
  font-size: 32px;
  font-weight: 400;
  line-height: 40px;
  letter-spacing: 0px;
}

.md-headline-medium {
  font-family: 'Roboto', sans-serif;
  font-size: 28px;
  font-weight: 400;
  line-height: 36px;
  letter-spacing: 0px;
}

.md-headline-small {
  font-family: 'Roboto', sans-serif;
  font-size: 24px;
  font-weight: 400;
  line-height: 32px;
  letter-spacing: 0px;
}

.md-title-large {
  font-family: 'Roboto', sans-serif;
  font-size: 22px;
  font-weight: 400;
  line-height: 28px;
  letter-spacing: 0px;
}

.md-title-medium {
  font-family: 'Roboto', sans-serif;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0.15px;
}

.md-title-small {
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0.1px;
}

.md-body-large {
  font-family: 'Roboto', sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  letter-spacing: 0.5px;
}

.md-body-medium {
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0.25px;
}

.md-body-small {
  font-family: 'Roboto', sans-serif;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  letter-spacing: 0.4px;
}

.md-label-large {
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0.1px;
}

.md-label-medium {
  font-family: 'Roboto', sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
  letter-spacing: 0.5px;
}

.md-label-small {
  font-family: 'Roboto', sans-serif;
  font-size: 11px;
  font-weight: 500;
  line-height: 16px;
  letter-spacing: 0.5px;
}

// Material Design Cards
.md-card {
  background-color: var(--md-surface);
  color: var(--md-on-surface);
  border-radius: var(--md-border-radius-md);
  box-shadow: var(--md-elevation-1);
  padding: var(--md-spacing-md);
  transition: box-shadow 0.3s ease, transform 0.2s ease;
  
  &:hover {
    box-shadow: var(--md-elevation-2);
    transform: translateY(-2px);
  }
  
  &.md-card-elevated {
    box-shadow: var(--md-elevation-2);
    
    &:hover {
      box-shadow: var(--md-elevation-3);
    }
  }
  
  &.md-card-filled {
    background-color: var(--md-surface-variant);
    color: var(--md-on-surface-variant);
  }
  
  &.md-card-outlined {
    border: 1px solid var(--md-outline-variant);
    box-shadow: none;
  }
}

.md-card-header {
  margin-bottom: var(--md-spacing-md);
  padding-bottom: var(--md-spacing-sm);
  border-bottom: 1px solid var(--md-outline-variant);
}

.md-card-title {
  @extend .md-title-large;
  margin: 0;
  color: var(--md-on-surface);
}

.md-card-subtitle {
  @extend .md-body-medium;
  margin: var(--md-spacing-xs) 0 0 0;
  color: var(--md-on-surface-variant);
}

.md-card-content {
  @extend .md-body-medium;
  margin: var(--md-spacing-md) 0;
}

.md-card-actions {
  display: flex;
  gap: var(--md-spacing-sm);
  margin-top: var(--md-spacing-md);
  padding-top: var(--md-spacing-sm);
  border-top: 1px solid var(--md-outline-variant);
}

// Material Design Buttons
.md-button {
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0.1px;
  border: none;
  border-radius: var(--md-border-radius-xl);
  padding: 10px 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--md-spacing-sm);
  min-height: 40px;
  
  &:focus {
    outline: 2px solid var(--md-primary);
    outline-offset: 2px;
  }
  
  &:disabled {
    opacity: 0.38;
    cursor: not-allowed;
  }
  
  .material-icons {
    font-size: 18px;
  }
}

.md-button-filled {
  @extend .md-button;
  background-color: var(--md-primary);
  color: var(--md-on-primary);
  
  &:hover:not(:disabled) {
    box-shadow: var(--md-elevation-1);
    background-color: color-mix(in srgb, var(--md-primary) 92%, black);
  }
  
  &:active:not(:disabled) {
    box-shadow: none;
    background-color: color-mix(in srgb, var(--md-primary) 88%, black);
  }
}

.md-button-outlined {
  @extend .md-button;
  background-color: transparent;
  color: var(--md-primary);
  border: 1px solid var(--md-outline);
  
  &:hover:not(:disabled) {
    background-color: color-mix(in srgb, var(--md-primary) 8%, transparent);
    border-color: var(--md-primary);
  }
  
  &:active:not(:disabled) {
    background-color: color-mix(in srgb, var(--md-primary) 12%, transparent);
  }
}

.md-button-text {
  @extend .md-button;
  background-color: transparent;
  color: var(--md-primary);
  padding: 10px 12px;
  
  &:hover:not(:disabled) {
    background-color: color-mix(in srgb, var(--md-primary) 8%, transparent);
  }
  
  &:active:not(:disabled) {
    background-color: color-mix(in srgb, var(--md-primary) 12%, transparent);
  }
}

.md-button-tonal {
  @extend .md-button;
  background-color: var(--md-secondary-container);
  color: var(--md-on-secondary-container);
  
  &:hover:not(:disabled) {
    box-shadow: var(--md-elevation-1);
    background-color: color-mix(in srgb, var(--md-secondary-container) 92%, black);
  }
  
  &:active:not(:disabled) {
    box-shadow: none;
    background-color: color-mix(in srgb, var(--md-secondary-container) 88%, black);
  }
}

// Material Design Form Fields
.md-text-field {
  position: relative;
  margin-bottom: var(--md-spacing-md);
}

.md-text-field-input {
  width: 100%;
  padding: 16px 16px 8px 16px;
  border: 1px solid var(--md-outline);
  border-radius: var(--md-border-radius-xs);
  background-color: var(--md-surface);
  color: var(--md-on-surface);
  font-family: 'Roboto', sans-serif;
  font-size: 16px;
  line-height: 24px;
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: var(--md-primary);
    border-width: 2px;
    padding: 15px 15px 7px 15px;
  }
  
  &:focus + .md-text-field-label,
  &:not(:placeholder-shown) + .md-text-field-label {
    transform: translateY(-12px) scale(0.75);
    color: var(--md-primary);
  }
  
  &::placeholder {
    color: transparent;
  }
}

.md-text-field-label {
  position: absolute;
  left: 16px;
  top: 16px;
  color: var(--md-on-surface-variant);
  font-family: 'Roboto', sans-serif;
  font-size: 16px;
  line-height: 24px;
  pointer-events: none;
  transition: all 0.3s ease;
  transform-origin: left top;
}

.md-text-field-filled {
  .md-text-field-input {
    background-color: var(--md-surface-variant);
    border: none;
    border-bottom: 1px solid var(--md-on-surface-variant);
    border-radius: var(--md-border-radius-xs) var(--md-border-radius-xs) 0 0;
    
    &:focus {
      border-bottom: 2px solid var(--md-primary);
      padding: 16px 16px 7px 16px;
    }
  }
}

// Material Design Navigation
.md-navigation-drawer {
  background-color: var(--md-surface);
  color: var(--md-on-surface);
  border-right: 1px solid var(--md-outline-variant);
  width: 280px;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  overflow-y: auto;
  transition: transform 0.3s ease;
}

.md-navigation-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin: 4px 12px;
  border-radius: var(--md-border-radius-xl);
  color: var(--md-on-surface);
  text-decoration: none;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: color-mix(in srgb, var(--md-on-surface) 8%, transparent);
    color: var(--md-on-surface);
    text-decoration: none;
  }
  
  &.active {
    background-color: var(--md-secondary-container);
    color: var(--md-on-secondary-container);
  }
  
  .material-icons {
    margin-right: var(--md-spacing-md);
    font-size: 24px;
  }
}

// Material Design Top App Bar
.md-top-app-bar {
  background-color: var(--md-surface);
  color: var(--md-on-surface);
  box-shadow: var(--md-elevation-2);
  padding: 0 var(--md-spacing-md);
  height: 64px;
  display: flex;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1001;
}

.md-top-app-bar-title {
  @extend .md-title-large;
  margin-left: var(--md-spacing-md);
  flex: 1;
}

// Responsive Design
@media (max-width: 768px) {
  .md-navigation-drawer {
    transform: translateX(-100%);
    
    &.open {
      transform: translateX(0);
    }
  }
  
  .md-card {
    margin: var(--md-spacing-sm);
    padding: var(--md-spacing-sm);
  }
  
  .md-button {
    padding: 8px 16px;
    min-height: 36px;
  }
}

// Utility Classes
.md-elevation-0 { box-shadow: none; }
.md-elevation-1 { box-shadow: var(--md-elevation-1); }
.md-elevation-2 { box-shadow: var(--md-elevation-2); }
.md-elevation-3 { box-shadow: var(--md-elevation-3); }
.md-elevation-4 { box-shadow: var(--md-elevation-4); }
.md-elevation-5 { box-shadow: var(--md-elevation-5); }

.md-surface { background-color: var(--md-surface); color: var(--md-on-surface); }
.md-primary { background-color: var(--md-primary); color: var(--md-on-primary); }
.md-secondary { background-color: var(--md-secondary); color: var(--md-on-secondary); }
.md-error { background-color: var(--md-error); color: var(--md-on-error); }

.md-text-primary { color: var(--md-primary); }
.md-text-secondary { color: var(--md-secondary); }
.md-text-error { color: var(--md-error); }
.md-text-on-surface { color: var(--md-on-surface); }
.md-text-on-surface-variant { color: var(--md-on-surface-variant); }
