<?php $__env->startSection('title', 'Ministers Management'); ?>
<?php $__env->startSection('page-title', 'Ministers Management'); ?>
<?php $__env->startSection('page-subtitle', 'Manage ministers displayed on the landing page'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Enhanced Search -->
    <?php if (isset($component)) { $__componentOriginal7f45f03dbdf99a8f135c471b87d3c319 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7f45f03dbdf99a8f135c471b87d3c319 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.enhanced-search','data' => ['type' => 'ministers','placeholder' => 'Search ministers by name, title, position, email...','route' => ''.e(route('admin.ministers.index')).'','filters' => [
            [
                'name' => 'search',
                'label' => 'Traditional Search',
                'type' => 'text',
                'placeholder' => 'Name, title, position...',
                'width' => 6
            ],
            [
                'name' => 'status',
                'label' => 'Status',
                'type' => 'select',
                'placeholder' => 'All Status',
                'width' => 3,
                'options' => ['1' => 'Active', '0' => 'Inactive']
            ]
        ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('enhanced-search'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'ministers','placeholder' => 'Search ministers by name, title, position, email...','route' => ''.e(route('admin.ministers.index')).'','filters' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
            [
                'name' => 'search',
                'label' => 'Traditional Search',
                'type' => 'text',
                'placeholder' => 'Name, title, position...',
                'width' => 6
            ],
            [
                'name' => 'status',
                'label' => 'Status',
                'type' => 'select',
                'placeholder' => 'All Status',
                'width' => 3,
                'options' => ['1' => 'Active', '0' => 'Inactive']
            ]
        ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7f45f03dbdf99a8f135c471b87d3c319)): ?>
<?php $attributes = $__attributesOriginal7f45f03dbdf99a8f135c471b87d3c319; ?>
<?php unset($__attributesOriginal7f45f03dbdf99a8f135c471b87d3c319); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7f45f03dbdf99a8f135c471b87d3c319)): ?>
<?php $component = $__componentOriginal7f45f03dbdf99a8f135c471b87d3c319; ?>
<?php unset($__componentOriginal7f45f03dbdf99a8f135c471b87d3c319); ?>
<?php endif; ?>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>Ministers
                    </h5>
                    <a href="<?php echo e(route('admin.ministers.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Minister
                    </a>
                </div>
                <div class="card-body">
                    <?php if($ministers->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Image</th>
                                        <th>Name</th>
                                        <th>Title</th>
                                        <th>Position</th>
                                        <th>Order</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $ministers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $minister): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <?php if($minister->image): ?>
                                                <img src="<?php echo e(Storage::url($minister->image)); ?>" 
                                                     alt="<?php echo e($minister->name); ?>" 
                                                     class="rounded-circle" 
                                                     style="width: 50px; height: 50px; object-fit: cover;">
                                            <?php else: ?>
                                                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" 
                                                     style="width: 50px; height: 50px;">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($minister->name); ?></td>
                                        <td><?php echo e($minister->title); ?></td>
                                        <td><?php echo e($minister->position ?? 'N/A'); ?></td>
                                        <td><?php echo e($minister->order); ?></td>
                                        <td>
                                            <?php if($minister->is_active): ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Inactive</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.ministers.show', $minister)); ?>" 
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.ministers.edit', $minister)); ?>" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="<?php echo e(route('admin.ministers.destroy', $minister)); ?>" 
                                                      method="POST" class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this minister?')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex justify-content-center">
                            <?php echo e($ministers->links()); ?>

                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No ministers found</h5>
                            <p class="text-muted">Start by adding your first minister.</p>
                            <a href="<?php echo e(route('admin.ministers.create')); ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add Minister
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\summit-management-system\resources\views/admin/ministers/index.blade.php ENDPATH**/ ?>