<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\Zone;
use App\Models\University;
use App\Models\Conference;
use Illuminate\Http\Request;

class BranchController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Branch::with(['zone.conference', 'university', 'users']);

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%");
            });
        }

        if ($request->filled('conference_id')) {
            $query->whereHas('zone', function ($q) use ($request) {
                $q->where('conference_id', $request->conference_id);
            });
        }

        if ($request->filled('university_id')) {
            $query->where('university_id', $request->university_id);
        }

        $branches = $query->paginate(15);
        $conferences = Conference::where('is_active', true)->get();
        $universities = University::where('is_active', true)->get();

        return view('admin.branches.index', compact('branches', 'conferences', 'universities'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $zones = Zone::with('conference')->where('is_active', true)->get();
        $universities = University::where('is_active', true)->get();

        return view('admin.branches.create', compact('zones', 'universities'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:10|unique:branches,code',
            'zone_id' => 'required|exists:zones,id',
            'university_id' => 'nullable|exists:universities,id',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        $validated['is_active'] = $request->has('is_active');

        Branch::create($validated);

        return redirect()->route('admin.branches.index')
            ->with('success', 'Branch created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Branch $branch)
    {
        $branch->load(['zone.conference', 'university', 'users']);

        return view('admin.branches.show', compact('branch'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Branch $branch)
    {
        $zones = Zone::with('conference')->where('is_active', true)->get();
        $universities = University::where('is_active', true)->get();

        return view('admin.branches.edit', compact('branch', 'zones', 'universities'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Branch $branch)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:10|unique:branches,code,' . $branch->id,
            'zone_id' => 'required|exists:zones,id',
            'university_id' => 'nullable|exists:universities,id',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        $validated['is_active'] = $request->has('is_active');

        $branch->update($validated);

        return redirect()->route('admin.branches.show', $branch)
            ->with('success', 'Branch updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Branch $branch)
    {
        if ($branch->users()->count() > 0) {
            return back()->with('error', 'Cannot delete branch with existing users.');
        }

        $branch->delete();

        return redirect()->route('admin.branches.index')
            ->with('success', 'Branch deleted successfully.');
    }
}
