<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Summit;
use App\Models\SummitRegistration;
use App\Models\Payment;
use App\Models\Branch;
use App\Models\Conference;
use App\Models\Zone;
use App\Models\University;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportsController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin|treasurer']);
    }

    /**
     * Display the reports dashboard.
     */
    public function index()
    {
        // Overview statistics
        $stats = [
            'total_users' => User::count(),
            'total_summits' => Summit::count(),
            'total_registrations' => SummitRegistration::count(),
            'total_revenue' => Payment::where('status', 'completed')->sum('amount'),
            'pending_payments' => Payment::where('status', 'pending')->count(),
            'completed_payments' => Payment::where('status', 'completed')->count(),
            'active_branches' => Branch::where('is_active', true)->count(),
            'total_conferences' => Conference::count(),
        ];

        // Recent activity summary
        $recentStats = [
            'registrations_this_month' => SummitRegistration::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
            'payments_this_month' => Payment::where('status', 'completed')
                ->whereMonth('paid_at', now()->month)
                ->whereYear('paid_at', now()->year)
                ->sum('amount'),
            'new_users_this_month' => User::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
        ];

        return view('admin.reports.index', compact('stats', 'recentStats'));
    }

    /**
     * Display registration reports.
     */
    public function registrations(Request $request)
    {
        $query = SummitRegistration::with(['user.branch.zone.conference', 'summit']);

        // Apply filters
        if ($request->filled('summit_id')) {
            $query->where('summit_id', $request->summit_id);
        }

        if ($request->filled('status')) {
            if ($request->status === 'paid') {
                $query->where('payment_complete', true);
            } elseif ($request->status === 'pending') {
                $query->where('payment_complete', false);
            }
        }

        if ($request->filled('conference_id')) {
            $query->whereHas('user.branch.zone', function ($q) use ($request) {
                $q->where('conference_id', $request->conference_id);
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $registrations = $query->orderBy('created_at', 'desc')->paginate(20);

        // Statistics for the filtered data
        $filteredStats = [
            'total_registrations' => $query->count(),
            'paid_registrations' => $query->where('payment_complete', true)->count(),
            'pending_registrations' => $query->where('payment_complete', false)->count(),
            'total_amount' => $query->sum('total_amount'),
            'paid_amount' => $query->sum('paid_amount'),
        ];

        // Get filter options
        $summits = Summit::orderBy('year', 'desc')->get();
        $conferences = Conference::where('is_active', true)->get();

        return view('admin.reports.registrations', compact(
            'registrations',
            'filteredStats',
            'summits',
            'conferences'
        ));
    }

    /**
     * Display payment reports.
     */
    public function payments(Request $request)
    {
        $query = Payment::with(['user.branch.zone.conference', 'summitRegistration.summit']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        if ($request->filled('summit_id')) {
            $query->whereHas('summitRegistration', function ($q) use ($request) {
                $q->where('summit_id', $request->summit_id);
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $payments = $query->orderBy('created_at', 'desc')->paginate(20);

        // Create separate queries for statistics to avoid conflicts
        $statsQuery = Payment::with(['user.branch.zone.conference', 'summitRegistration.summit']);

        // Apply same filters to stats query
        if ($request->filled('status')) {
            $statsQuery->where('status', $request->status);
        }

        if ($request->filled('payment_method')) {
            $statsQuery->where('payment_method', $request->payment_method);
        }

        if ($request->filled('summit_id')) {
            $statsQuery->whereHas('summitRegistration', function ($q) use ($request) {
                $q->where('summit_id', $request->summit_id);
            });
        }

        if ($request->filled('date_from')) {
            $statsQuery->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $statsQuery->whereDate('created_at', '<=', $request->date_to);
        }

        // Payment statistics using separate queries
        $allFilteredPayments = $statsQuery->get();
        $paymentStats = [
            'total_payments' => $allFilteredPayments->count(),
            'completed_payments' => $allFilteredPayments->where('status', 'completed')->count(),
            'pending_payments' => $allFilteredPayments->where('status', 'pending')->count(),
            'failed_payments' => $allFilteredPayments->where('status', 'failed')->count(),
            'total_amount' => $allFilteredPayments->where('status', 'completed')->sum('amount'),
            'pending_amount' => $allFilteredPayments->where('status', 'pending')->sum('amount'),
        ];

        // Payment methods breakdown using filtered data
        $paymentMethods = $allFilteredPayments->where('status', 'completed')
            ->groupBy('payment_method')
            ->map(function ($payments, $method) {
                return (object) [
                    'payment_method' => $method,
                    'count' => $payments->count(),
                    'total' => $payments->sum('amount')
                ];
            })
            ->values();

        // Get filter options
        $summits = Summit::orderBy('year', 'desc')->get();
        $paymentMethodOptions = Payment::distinct()->pluck('payment_method')->filter();

        return view('admin.reports.payments', compact(
            'payments',
            'paymentStats',
            'paymentMethods',
            'summits',
            'paymentMethodOptions'
        ));
    }

    /**
     * Display summit reports.
     */
    public function summits(Request $request)
    {
        $query = Summit::withCount(['registrations', 'activities', 'ministers']);

        if ($request->filled('year')) {
            $query->where('year', $request->year);
        }

        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            } elseif ($request->status === 'current') {
                $query->where('is_current', true);
            }
        }

        $summits = $query->orderBy('year', 'desc')->paginate(15);

        // Summit statistics
        $summitStats = [
            'total_summits' => Summit::count(),
            'active_summits' => Summit::where('is_active', true)->count(),
            'upcoming_summits' => Summit::where('start_date', '>', now())->count(),
            'past_summits' => Summit::where('end_date', '<', now())->count(),
            'total_revenue_all_summits' => Payment::where('status', 'completed')->sum('amount'),
        ];

        // Years for filter
        $years = Summit::distinct()->orderBy('year', 'desc')->pluck('year');

        return view('admin.reports.summits', compact('summits', 'summitStats', 'years'));
    }

    /**
     * Display user reports.
     */
    public function users(Request $request)
    {
        $query = User::with(['branch.zone.conference', 'university']);

        // Apply filters
        if ($request->filled('role')) {
            $query->role($request->role);
        }

        if ($request->filled('conference_id')) {
            $query->whereHas('branch.zone', function ($q) use ($request) {
                $q->where('conference_id', $request->conference_id);
            });
        }

        if ($request->filled('university_id')) {
            $query->where('university_id', $request->university_id);
        }

        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(20);

        // User statistics
        $userStats = [
            'total_users' => User::count(),
            'active_users' => User::where('is_active', true)->count(),
            'admin_users' => User::role('admin')->count(),
            'regular_users' => User::role('user')->count(),
            'users_with_registrations' => User::whereHas('summitRegistrations')->count(),
        ];

        // Get filter options
        $conferences = Conference::where('is_active', true)->get();
        $universities = University::where('is_active', true)->get();

        return view('admin.reports.users', compact(
            'users',
            'userStats',
            'conferences',
            'universities'
        ));
    }

    /**
     * Display analytics dashboard.
     */
    public function analytics()
    {
        // Monthly registration trends (last 12 months) - PostgreSQL compatible
        $monthlyRegistrations = SummitRegistration::selectRaw('EXTRACT(MONTH FROM created_at) as month, COUNT(*) as count')
            ->whereRaw('EXTRACT(YEAR FROM created_at) = ?', [date('Y')])
            ->groupBy('month')
            ->pluck('count', 'month')
            ->toArray();

        // Monthly payment trends (last 12 months) - PostgreSQL compatible
        $monthlyPayments = Payment::selectRaw('EXTRACT(MONTH FROM paid_at) as month, SUM(amount) as total')
            ->where('status', 'completed')
            ->whereRaw('EXTRACT(YEAR FROM paid_at) = ?', [date('Y')])
            ->groupBy('month')
            ->pluck('total', 'month')
            ->toArray();

        // Registration by conference
        $registrationsByConference = SummitRegistration::join('users', 'summit_registrations.user_id', '=', 'users.id')
            ->join('branches', 'users.branch_id', '=', 'branches.id')
            ->join('zones', 'branches.zone_id', '=', 'zones.id')
            ->join('conferences', 'zones.conference_id', '=', 'conferences.id')
            ->select('conferences.name', DB::raw('COUNT(*) as total'))
            ->groupBy('conferences.id', 'conferences.name')
            ->orderBy('total', 'desc')
            ->get();

        // Payment by branch (top 10)
        $paymentsByBranch = Payment::join('summit_registrations', 'payments.summit_registration_id', '=', 'summit_registrations.id')
            ->join('users', 'summit_registrations.user_id', '=', 'users.id')
            ->join('branches', 'users.branch_id', '=', 'branches.id')
            ->where('payments.status', 'completed')
            ->select('branches.name', DB::raw('SUM(payments.amount) as total'))
            ->groupBy('branches.id', 'branches.name')
            ->orderBy('total', 'desc')
            ->limit(10)
            ->get();

        return view('admin.reports.analytics', compact(
            'monthlyRegistrations',
            'monthlyPayments',
            'registrationsByConference',
            'paymentsByBranch'
        ));
    }
}
