@extends('layouts.admin')

@section('title', 'User Details')
@section('page-title', 'User Details')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">{{ $user->full_name }}</h2>
                <p class="text-muted">{{ $user->email }}</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('admin.users.manage-roles', $user) }}" class="btn btn-warning">
                    <i class="fas fa-user-shield me-2"></i>Manage Roles
                </a>
                <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>Edit User
                </a>
                <a href="{{ route('admin.users.index') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Users
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- User Information -->
    <div class="col-lg-8">
        <!-- Basic Information -->
        <div class="payment-details-card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>Basic Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">First Name</label>
                            <p class="mb-0">{{ $user->first_name }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Last Name</label>
                            <p class="mb-0">{{ $user->last_name }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Email</label>
                            <p class="mb-0">{{ $user->email }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Phone</label>
                            <p class="mb-0">{{ $user->phone ?: 'Not provided' }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Date of Birth</label>
                            <p class="mb-0">{{ $user->date_of_birth ? $user->date_of_birth->format('M j, Y') : 'Not provided' }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Gender</label>
                            <p class="mb-0">{{ $user->gender ? ucfirst($user->gender) : 'Not provided' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Academic Information -->
        <div class="payment-details-card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Academic Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">University</label>
                            <p class="mb-0">{{ $user->university->name ?? 'Not assigned' }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Student ID</label>
                            <p class="mb-0">{{ $user->student_id ?: 'Not provided' }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Year of Study</label>
                            <p class="mb-0">{{ $user->year_of_study ?: 'Not provided' }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Course</label>
                            <p class="mb-0">{{ $user->course ?: 'Not provided' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Organization Information -->
        <div class="payment-details-card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-sitemap me-2"></i>Organization Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Conference</label>
                            <p class="mb-0">{{ $user->branch->zone->conference->name ?? 'Not assigned' }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Zone</label>
                            <p class="mb-0">{{ $user->branch->zone->name ?? 'Not assigned' }}</p>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label class="form-label text-muted">Branch</label>
                            <p class="mb-0">{{ $user->branch->name ?? 'Not assigned' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="payment-details-card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-address-card me-2"></i>Contact Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label class="form-label text-muted">Address</label>
                            <p class="mb-0">{{ $user->address ?: 'Not provided' }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Emergency Contact Name</label>
                            <p class="mb-0">{{ $user->emergency_contact_name ?: 'Not provided' }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Emergency Contact Phone</label>
                            <p class="mb-0">{{ $user->emergency_contact_phone ?: 'Not provided' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summit Registrations -->
        @if($user->summitRegistrations->count() > 0)
        <div class="payment-details-card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-mountain me-2"></i>Summit Registrations</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Summit</th>
                                <th>Registration #</th>
                                <th>Registration Status</th>
                                <th>Amount</th>
                                <th>Payment Status</th>
                                <th>Registered</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($user->summitRegistrations as $registration)
                            <tr class="{{ $registration->status === 'cancelled' ? 'table-secondary' : '' }}">
                                <td>
                                    <div>
                                        <div class="fw-bold {{ $registration->status === 'cancelled' ? 'text-muted' : '' }}">
                                            {{ $registration->summit->title }}
                                            @if($registration->status === 'cancelled')
                                                <i class="fas fa-times-circle text-danger ms-1" title="Cancelled"></i>
                                            @endif
                                        </div>
                                        <small class="text-muted">{{ $registration->summit->year }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ $registration->registration_number }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $registration->status === 'confirmed' ? 'success' : ($registration->status === 'cancelled' ? 'danger' : 'warning') }}">
                                        {{ ucfirst($registration->status) }}
                                    </span>
                                </td>
                                <td>TZS {{ number_format($registration->total_amount, 2) }}</td>
                                <td>
                                    @if($registration->payment_complete)
                                        <span class="badge bg-success">Paid</span>
                                    @elseif($registration->hasSuccessfulPayments())
                                        <span class="badge bg-warning">Partial</span>
                                        <small class="d-block text-muted">TZS {{ number_format($registration->paid_amount, 2) }}</small>
                                    @else
                                        @php
                                            $hasFailedPayments = $registration->payments()->where('status', 'failed')->exists();
                                            $hasPendingPayments = $registration->payments()->whereIn('status', ['pending', 'processing'])->exists();
                                        @endphp
                                        @if($hasFailedPayments)
                                            <span class="badge bg-danger">Failed</span>
                                        @elseif($hasPendingPayments)
                                            <span class="badge bg-info">Pending</span>
                                        @else
                                            <span class="badge bg-secondary">Unpaid</span>
                                        @endif
                                    @endif
                                </td>
                                <td>{{ $registration->registered_at->format('M j, Y') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('admin.registrations.show', $registration) }}" 
                                           class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if($registration->canBeCancelledByAdmin())
                                        <button type="button" class="btn btn-outline-danger"
                                                onclick="cancelRegistration({{ $registration->id }})" title="Cancel Registration">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Profile Image & Status -->
        <div class="payment-details-card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-circle me-2"></i>Profile</h5>
            </div>
            <div class="card-body text-center">
                @if($user->profile_image)
                    <img src="{{ Storage::url($user->profile_image) }}" alt="Profile Image" 
                         class="rounded-circle mb-3" style="width: 120px; height: 120px; object-fit: cover;">
                @else
                    <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 120px; height: 120px;">
                        <i class="fas fa-user fa-3x text-muted"></i>
                    </div>
                @endif
                
                <h5 class="mb-1">{{ $user->full_name }}</h5>
                <p class="text-muted mb-3">{{ $user->email }}</p>
                
                <div class="mb-3">
                    <span class="badge bg-{{ $user->is_active ? 'success' : 'danger' }} fs-6">
                        {{ $user->is_active ? 'Active' : 'Inactive' }}
                    </span>
                </div>
                
                @if($user->roles->count() > 0)
                <div class="mb-3">
                    <label class="form-label text-muted small">Roles</label>
                    <div>
                        @foreach($user->roles as $role)
                        <span class="badge bg-{{ $role->name === 'admin' ? 'danger' : ($role->name === 'treasurer' ? 'warning' : 'info') }} me-1">
                            {{ ucfirst($role->name) }}
                        </span>
                        @endforeach
                    </div>
                </div>
                @endif
                
                <div class="text-muted small">
                    <p class="mb-1">Member since: {{ $user->created_at->format('M j, Y') }}</p>
                    <p class="mb-0">Last updated: {{ $user->updated_at->format('M j, Y') }}</p>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="payment-details-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Quick Stats</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary mb-1">{{ $user->summitRegistrations->count() }}</h4>
                            <small class="text-muted">Registrations</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success mb-1">{{ $user->payments->count() }}</h4>
                        <small class="text-muted">Payments</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cancel Registration Modal -->
<div class="modal fade" id="cancelRegistrationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Registration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to cancel this registration? This action cannot be undone.</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Note:</strong> If successful payments have been made, they will need to be refunded manually.
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Note:</strong> Any pending or failed payments will also be cancelled automatically.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="cancelRegistrationForm" method="POST" style="display: inline;">
                    @csrf
                    @method('PATCH')
                    <button type="submit" class="btn btn-danger">Cancel Registration</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function cancelRegistration(registrationId) {
    const form = document.getElementById('cancelRegistrationForm');
    form.action = `/admin/registrations/${registrationId}/cancel`;
    
    const modal = new bootstrap.Modal(document.getElementById('cancelRegistrationModal'));
    modal.show();
}
</script>
@endsection
