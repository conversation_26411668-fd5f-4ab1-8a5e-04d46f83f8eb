<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\SummitRegistration;
use App\Models\Summit;
use App\Models\Branch;
use App\Models\Conference;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;

class TreasurerController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:treasurer|admin']);
    }

    /**
     * Treasurer Dashboard
     */
    public function dashboard()
    {
        try {
            $stats = $this->getFinancialStats();
            $recentPayments = $this->getRecentPayments();
            $paymentsByBranch = $this->getPaymentsByBranch();
            $monthlyTrends = $this->getMonthlyPaymentTrends();

            // Add some sample data if no real data exists
            if ($monthlyTrends->isEmpty()) {
                $monthlyTrends = collect([
                    (object)['month' => 1, 'total' => 50000],
                    (object)['month' => 2, 'total' => 75000],
                    (object)['month' => 3, 'total' => 60000],
                    (object)['month' => 4, 'total' => 90000],
                    (object)['month' => 5, 'total' => 80000],
                    (object)['month' => 6, 'total' => 95000],
                    (object)['month' => 7, 'total' => 85000],
                ]);
            }

            return view('treasurer.dashboard', compact(
                'stats',
                'recentPayments',
                'paymentsByBranch',
                'monthlyTrends'
            ));
        } catch (\Exception $e) {
            // Fallback with empty data if there's an error
            $stats = [
                'total_revenue' => 0,
                'monthly_revenue' => 0,
                'completed_payments' => 0,
                'pending_payments' => 0,
                'failed_payments' => 0,
                'outstanding_amount' => 0,
            ];
            $recentPayments = collect();
            $paymentsByBranch = collect();
            $monthlyTrends = collect();

            return view('treasurer.dashboard', compact(
                'stats',
                'recentPayments',
                'paymentsByBranch',
                'monthlyTrends'
            ));
        }
    }

    /**
     * Financial Reports Index
     */
    public function reports()
    {
        return view('treasurer.reports.index');
    }

    /**
     * Payment Reports
     */
    public function paymentReports(Request $request)
    {
        $dateFrom = $request->input('date_from', now()->startOfMonth()->format('Y-m-d'));
        $dateTo = $request->input('date_to', now()->format('Y-m-d'));
        $status = $request->input('status', 'all');
        $branch = $request->input('branch', 'all');

        // Base query for filtering
        $baseQuery = Payment::with(['user.branch', 'summitRegistration.summit'])
            ->whereBetween('created_at', [$dateFrom, $dateTo]);

        if ($branch !== 'all') {
            $baseQuery->whereHas('user', function($q) use ($branch) {
                $q->where('branch_id', $branch);
            });
        }

        // Clone the base query for payments display
        $paymentsQuery = clone $baseQuery;
        if ($status !== 'all') {
            $paymentsQuery->where('status', $status);
        }

        $payments = $paymentsQuery->orderBy('created_at', 'desc')->paginate(50);

        // Calculate summary statistics using separate queries to avoid conflicts
        $summaryQuery = clone $baseQuery;
        $allPayments = $summaryQuery->get();

        $summary = [
            'total_amount' => $allPayments->where('status', 'completed')->sum('amount'),
            'total_count' => $allPayments->count(),
            'completed_count' => $allPayments->where('status', 'completed')->count(),
            'pending_count' => $allPayments->where('status', 'pending')->count(),
            'failed_count' => $allPayments->where('status', 'failed')->count(),
        ];

        $branches = Branch::orderBy('name')->get();

        return view('treasurer.reports.payments', compact(
            'payments',
            'summary',
            'branches',
            'dateFrom',
            'dateTo',
            'status',
            'branch'
        ));
    }

    /**
     * Revenue Analytics
     */
    public function analytics()
    {
        $monthlyRevenue = $this->getMonthlyRevenue();
        $revenueByBranch = $this->getRevenueByBranch();
        $revenueByConference = $this->getRevenueByConference();
        $paymentMethodStats = $this->getPaymentMethodStats();
        $registrationTrends = $this->getRegistrationTrends();

        return view('treasurer.analytics', compact(
            'monthlyRevenue',
            'revenueByBranch',
            'revenueByConference',
            'paymentMethodStats',
            'registrationTrends'
        ));
    }

    /**
     * Export Payment Report to PDF
     */
    public function exportPaymentReport(Request $request)
    {
        $dateFrom = $request->input('date_from', now()->startOfMonth()->format('Y-m-d'));
        $dateTo = $request->input('date_to', now()->format('Y-m-d'));
        $status = $request->input('status', 'all');

        $query = Payment::with(['user.branch.zone.conference', 'summitRegistration.summit'])
            ->whereBetween('created_at', [$dateFrom, $dateTo]);

        if ($status !== 'all') {
            $query->where('status', $status);
        }

        $payments = $query->orderBy('created_at', 'desc')->get();
        
        $summary = [
            'total_amount' => $payments->where('status', 'completed')->sum('amount'),
            'total_count' => $payments->count(),
            'completed_count' => $payments->where('status', 'completed')->count(),
            'pending_count' => $payments->where('status', 'pending')->count(),
            'failed_count' => $payments->where('status', 'failed')->count(),
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'generated_by' => Auth::user()->full_name,
            'generated_at' => now()->format('Y-m-d H:i:s')
        ];

        $pdf = PDF::loadView('treasurer.reports.payment-pdf', compact('payments', 'summary'));
        
        $filename = 'payment-report-' . $dateFrom . '-to-' . $dateTo . '.pdf';
        return $pdf->download($filename);
    }

    /**
     * Export Revenue Summary to PDF
     */
    public function exportRevenueSummary(Request $request)
    {
        $period = $request->input('period', 'monthly'); // monthly, quarterly, yearly
        
        $data = [
            'monthly_revenue' => $this->getMonthlyRevenue(),
            'revenue_by_branch' => $this->getRevenueByBranch(),
            'revenue_by_conference' => $this->getRevenueByConference(),
            'payment_method_stats' => $this->getPaymentMethodStats(),
            'summary' => $this->getFinancialStats(),
            'generated_by' => Auth::user()->full_name,
            'generated_at' => now()->format('Y-m-d H:i:s'),
            'period' => $period
        ];

        $pdf = PDF::loadView('treasurer.reports.revenue-summary-pdf', $data);
        
        $filename = 'revenue-summary-' . $period . '-' . now()->format('Y-m') . '.pdf';
        return $pdf->download($filename);
    }

    /**
     * Outstanding Payments Report
     */
    public function outstandingPayments()
    {
        $outstandingRegistrations = SummitRegistration::with(['user.branch.zone.conference', 'summit'])
            ->where('payment_complete', false)
            ->where('status', '!=', 'cancelled')
            ->orderBy('registered_at', 'desc')
            ->get();

        $summary = [
            'total_outstanding' => $outstandingRegistrations->sum('balance'),
            'count' => $outstandingRegistrations->count(),
            'by_summit' => $outstandingRegistrations->groupBy('summit.title')->map(function($group) {
                return [
                    'count' => $group->count(),
                    'amount' => $group->sum('balance')
                ];
            })
        ];

        return view('treasurer.reports.outstanding', compact('outstandingRegistrations', 'summary'));
    }

    // Private helper methods
    private function getFinancialStats()
    {
        // Get all payments and registrations (not just current summit for treasurer overview)
        $allPayments = Payment::query();
        $allRegistrations = SummitRegistration::query();

        // Calculate basic statistics
        $totalRevenue = $allPayments->where('status', 'completed')->sum('amount');
        $completedPayments = $allPayments->where('status', 'completed')->count();
        $pendingPayments = $allPayments->where('status', 'pending')->count();
        $failedPayments = $allPayments->where('status', 'failed')->count();
        $totalRegistrations = $allRegistrations->count();

        // Calculate paid registrations
        $paidRegistrations = $allRegistrations->whereHas('payments', function($query) {
            $query->where('status', 'completed');
        })->count();

        // Calculate outstanding amount from unpaid registrations
        $outstandingAmount = $allRegistrations->where('payment_complete', false)->sum('balance');

        // Monthly revenue
        $monthlyRevenue = $allPayments->where('status', 'completed')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('amount');

        // Payments today
        $paymentsToday = $allPayments->where('status', 'completed')
            ->whereDate('created_at', today())
            ->count();

        // Average payment amount
        $averagePaymentAmount = $allPayments->where('status', 'completed')->avg('amount') ?? 0;

        // Payment success rate
        $totalPaymentAttempts = $allPayments->count();
        $paymentSuccessRate = $totalPaymentAttempts > 0
            ? round(($completedPayments / $totalPaymentAttempts) * 100, 2)
            : 0;

        return [
            'total_revenue' => $totalRevenue,
            'monthly_revenue' => $monthlyRevenue,
            'completed_payments' => $completedPayments,
            'pending_payments' => $pendingPayments,
            'failed_payments' => $failedPayments,
            'total_registrations' => $totalRegistrations,
            'paid_registrations' => $paidRegistrations,
            'outstanding_amount' => $outstandingAmount,
            'payments_today' => $paymentsToday,
            'average_payment_amount' => $averagePaymentAmount,
            'payment_success_rate' => $paymentSuccessRate,
        ];
    }

    private function getRecentPayments()
    {
        return Payment::with(['user.branch', 'summitRegistration.summit'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();
    }

    private function getPaymentsByBranch()
    {
        return Payment::join('users', 'payments.user_id', '=', 'users.id')
            ->join('branches', 'users.branch_id', '=', 'branches.id')
            ->where('payments.status', 'completed')
            ->select('branches.name', DB::raw('SUM(payments.amount) as total'), DB::raw('COUNT(*) as count'))
            ->groupBy('branches.id', 'branches.name')
            ->orderBy('total', 'desc')
            ->limit(10)
            ->get();
    }

    private function getMonthlyPaymentTrends()
    {
        return Payment::selectRaw('EXTRACT(MONTH FROM created_at) as month, SUM(amount) as total')
            ->where('status', 'completed')
            ->whereRaw('EXTRACT(YEAR FROM created_at) = ?', [date('Y')])
            ->groupBy('month')
            ->orderBy('month')
            ->get();
    }

    private function getMonthlyRevenue()
    {
        return Payment::selectRaw('EXTRACT(MONTH FROM created_at) as month, EXTRACT(YEAR FROM created_at) as year, SUM(amount) as total')
            ->where('status', 'completed')
            ->whereRaw('created_at >= ?', [now()->subMonths(12)])
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();
    }

    private function getRevenueByBranch()
    {
        return Payment::join('users', 'payments.user_id', '=', 'users.id')
            ->join('branches', 'users.branch_id', '=', 'branches.id')
            ->join('zones', 'branches.zone_id', '=', 'zones.id')
            ->join('conferences', 'zones.conference_id', '=', 'conferences.id')
            ->where('payments.status', 'completed')
            ->select(
                'branches.name as branch_name',
                'conferences.name as conference_name',
                DB::raw('SUM(payments.amount) as total'),
                DB::raw('COUNT(*) as count')
            )
            ->groupBy('branches.id', 'branches.name', 'conferences.name')
            ->orderBy('total', 'desc')
            ->get();
    }

    private function getRevenueByConference()
    {
        return Payment::join('users', 'payments.user_id', '=', 'users.id')
            ->join('branches', 'users.branch_id', '=', 'branches.id')
            ->join('zones', 'branches.zone_id', '=', 'zones.id')
            ->join('conferences', 'zones.conference_id', '=', 'conferences.id')
            ->where('payments.status', 'completed')
            ->select('conferences.name', DB::raw('SUM(payments.amount) as total'), DB::raw('COUNT(*) as count'))
            ->groupBy('conferences.id', 'conferences.name')
            ->orderBy('total', 'desc')
            ->get();
    }

    private function getPaymentMethodStats()
    {
        return Payment::where('status', 'completed')
            ->selectRaw('
                CASE
                    WHEN payment_method = \'selcom\' THEN \'Selcom Mobile Money\'
                    WHEN payment_method = \'azampay_mobile\' THEN \'AzamPay Mobile Money\'
                    WHEN payment_method = \'azampay_bank\' THEN \'AzamPay Bank Transfer\'
                    WHEN payment_method = \'bank_transfer\' THEN \'Manual Bank Transfer\'
                    WHEN payment_method = \'cash\' THEN \'Cash Payment\'
                    WHEN payment_method = \'mobile_money_direct\' THEN \'Direct Mobile Money\'
                    ELSE payment_method
                END as method_name,
                payment_method,
                COALESCE(gateway_provider, \'unknown\') as gateway_provider,
                COUNT(*) as count,
                SUM(amount) as total
            ')
            ->groupBy('payment_method', 'gateway_provider')
            ->orderBy('total', 'desc')
            ->get();
    }

    private function getRegistrationTrends()
    {
        return SummitRegistration::selectRaw('DATE(registered_at) as date, COUNT(*) as count')
            ->whereRaw('registered_at >= ?', [now()->subDays(30)])
            ->groupBy('date')
            ->orderBy('date', 'desc')
            ->get();
    }
}
