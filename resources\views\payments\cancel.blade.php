@extends('layouts.user')

@section('title', 'Payment Cancelled')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Cancellation Message -->
        <div class="text-center mb-4">
            <div class="cancel-icon mb-3">
                <i class="fas fa-times-circle fa-5x text-warning"></i>
            </div>
            <h1 class="text-warning">Payment Cancelled</h1>
            <p class="lead text-muted">Your payment has been cancelled and no charges were made.</p>
        </div>

        <!-- Payment Details Card -->
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0"><i class="fas fa-ban me-2"></i>Cancelled Payment Details</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Payment Reference:</strong></td>
                                <td><code>{{ $payment->payment_reference }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>Amount:</strong></td>
                                <td><strong>TZS {{ number_format($payment->amount, 2) }}</strong></td>
                            </tr>
                            <tr>
                                <td><strong>Payment Method:</strong></td>
                                <td>{{ ucfirst(str_replace('_', ' ', $payment->payment_method)) }}</td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td><span class="badge bg-warning">Cancelled</span></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Created:</strong></td>
                                <td>{{ $payment->created_at->format('M j, Y g:i A') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Cancelled:</strong></td>
                                <td>{{ $payment->updated_at->format('M j, Y g:i A') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Summit:</strong></td>
                                <td>{{ $payment->summitRegistration->summit->title }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="alert alert-info mt-3">
                    <h6><i class="fas fa-info-circle me-2"></i>What happened?</h6>
                    <p class="mb-0">
                        Your payment was cancelled either by you or due to a timeout. No money has been deducted from your account.
                        You can try making the payment again using the button below.
                    </p>
                </div>
            </div>
        </div>

        <!-- Summit Registration Summary -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-mountain me-2"></i>Summit Registration Status</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Summit:</strong></td>
                                <td>{{ $payment->summitRegistration->summit->title }}</td>
                            </tr>
                            <tr>
                                <td><strong>Year:</strong></td>
                                <td>{{ $payment->summitRegistration->summit->year }}</td>
                            </tr>
                            <tr>
                                <td><strong>Registration Number:</strong></td>
                                <td><code>{{ $payment->summitRegistration->registration_number }}</code></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Total Registration Fee:</strong></td>
                                <td>TZS {{ number_format($payment->summitRegistration->total_amount, 2) }}</td>
                            </tr>
                            <tr>
                                <td><strong>Amount Paid:</strong></td>
                                <td class="text-success">TZS {{ number_format($payment->summitRegistration->paid_amount, 2) }}</td>
                            </tr>
                            <tr>
                                <td><strong>Outstanding Balance:</strong></td>
                                <td>
                                    @if($payment->summitRegistration->balance > 0)
                                        <span class="text-danger"><strong>TZS {{ number_format($payment->summitRegistration->balance, 2) }}</strong></span>
                                    @else
                                        <span class="text-success">TZS 0.00 (Fully Paid)</span>
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                @if($payment->summitRegistration->balance > 0)
                <div class="alert alert-warning mt-3">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Payment Still Required</h6>
                    <p class="mb-0">
                        You still need to pay <strong>TZS {{ number_format($payment->summitRegistration->balance, 2) }}</strong> 
                        to complete your summit registration.
                    </p>
                </div>
                @endif
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-arrow-right me-2"></i>What would you like to do?</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    @if($payment->summitRegistration->balance > 0)
                    <div class="col-md-6 mb-3">
                        <div class="d-grid">
                            <a href="{{ route('payments.create', $payment->summitRegistration) }}" class="btn btn-success btn-lg">
                                <i class="fas fa-credit-card me-2"></i>Try Payment Again
                            </a>
                        </div>
                        <small class="text-muted">Complete your summit registration payment</small>
                    </div>
                    @endif
                    
                    <div class="col-md-6 mb-3">
                        <div class="d-grid">
                            <a href="{{ route('registrations.show', $payment->summitRegistration) }}" class="btn btn-primary btn-lg">
                                <i class="fas fa-eye me-2"></i>View Registration
                            </a>
                        </div>
                        <small class="text-muted">See your registration details</small>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6 mb-3">
                        <div class="d-grid">
                            <a href="{{ route('payments.index') }}" class="btn btn-outline-info">
                                <i class="fas fa-list me-2"></i>Payment History
                            </a>
                        </div>
                        <small class="text-muted">View all your payments</small>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="d-grid">
                            <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </div>
                        <small class="text-muted">Return to your dashboard</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Help Section -->
        <div class="card mt-4">
            <div class="card-body">
                <h6><i class="fas fa-question-circle text-info me-2"></i>Need Help?</h6>
                <p class="mb-2">
                    If you're having trouble with payments or need assistance, here are some options:
                </p>
                <ul class="mb-3">
                    <li>Check that your mobile money account has sufficient funds</li>
                    <li>Ensure your phone number is correct and active</li>
                    <li>Try using a different mobile money provider</li>
                    <li>Contact our support team if the problem persists</li>
                </ul>
                <a href="{{ route('help') }}" class="btn btn-outline-info">
                    <i class="fas fa-headset me-2"></i>Get Support
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.cancel-icon {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}
</style>
@endsection
