@extends('layouts.user')

@section('title', 'Registration Details')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">
                    Registration Details
                    @if($registration->status === 'cancelled')
                        <span class="badge bg-danger ms-2">CANCELLED</span>
                    @elseif($registration->status === 'confirmed')
                        <span class="badge bg-success ms-2">CONFIRMED</span>
                    @else
                        <span class="badge bg-warning ms-2">PENDING</span>
                    @endif
                </h2>
                <p class="text-muted">{{ $registration->summit->title }} ({{ $registration->summit->year }})</p>
            </div>
            <div class="d-flex gap-2">
                @if($registration->balance > 0 && $registration->status !== 'cancelled')
                <a href="{{ route('payments.create', $registration) }}" class="btn btn-success">
                    <i class="fas fa-credit-card me-2"></i>Make Payment
                </a>
                @endif
                @if($registration->canBeCancelledByUser())
                <button type="button" class="btn btn-outline-danger" onclick="cancelRegistration()">
                    <i class="fas fa-times me-2"></i>Cancel Registration
                </button>
                @endif
                <a href="{{ route('registrations.index') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Registrations
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Registration Information -->
@if($registration->status === 'cancelled')
<div class="alert alert-danger">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <strong>Registration Cancelled:</strong> This registration has been cancelled and is no longer active.
</div>
@endif

<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card {{ $registration->status === 'cancelled' ? 'border-danger' : '' }}">
            <div class="card-header {{ $registration->status === 'cancelled' ? 'bg-light' : '' }}">
                <h5 class="mb-0 {{ $registration->status === 'cancelled' ? 'text-muted' : '' }}">
                    <i class="fas fa-clipboard-list me-2"></i>Registration Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Registration #:</strong></td>
                                <td><span class="badge bg-primary">{{ $registration->registration_number }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    <span class="badge bg-{{ $registration->status === 'confirmed' ? 'success' : ($registration->status === 'cancelled' ? 'danger' : 'warning') }}">
                                        {{ ucfirst($registration->status) }}
                                    </span>
                                    @if($registration->status === 'cancelled')
                                        <small class="text-muted d-block">This registration has been cancelled</small>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Summit:</strong></td>
                                <td>{{ $registration->summit->title }}</td>
                            </tr>
                            <tr>
                                <td><strong>Year:</strong></td>
                                <td>{{ $registration->summit->year }}</td>
                            </tr>
                            <tr>
                                <td><strong>Venue:</strong></td>
                                <td>{{ $registration->summit->venue }}</td>
                            </tr>
                            <tr>
                                <td><strong>Dates:</strong></td>
                                <td>{{ $registration->summit->start_date->format('M j') }} - {{ $registration->summit->end_date->format('M j, Y') }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Registration Date:</strong></td>
                                <td>{{ $registration->created_at->format('F j, Y') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Total Amount:</strong></td>
                                <td><span class="fw-bold text-primary">TSh {{ number_format($registration->total_amount) }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Paid Amount:</strong></td>
                                <td><span class="fw-bold text-success">TSh {{ number_format($registration->paid_amount) }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Balance:</strong></td>
                                <td>
                                    @if($registration->balance > 0)
                                        <span class="fw-bold text-warning">TSh {{ number_format($registration->balance) }}</span>
                                    @else
                                        <span class="fw-bold text-success">TSh 0 (Paid)</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Payment Status:</strong></td>
                                <td>
                                    <span class="badge bg-{{ $registration->payment_complete ? 'success' : 'warning' }}">
                                        {{ $registration->payment_complete ? 'Complete' : 'Pending' }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    @if($registration->balance > 0)
                    <a href="{{ route('payments.create', $registration) }}" class="btn btn-success">
                        <i class="fas fa-credit-card me-2"></i>Make Payment
                    </a>
                    @endif
                    
                    @if($registration->payment_complete)
                    <a href="{{ route('id-cards.index') }}" class="btn btn-primary">
                        <i class="fas fa-id-card me-2"></i>Get ID Card
                    </a>
                    @endif
                    
                    <a href="{{ route('payments.index') }}" class="btn btn-outline-info">
                        <i class="fas fa-history me-2"></i>Payment History
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Summit Information -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-mountain me-2"></i>Summit Details</h5>
            </div>
            <div class="card-body">
                <h6>{{ $registration->summit->title }}</h6>
                <p class="text-muted mb-2">{{ $registration->summit->theme }}</p>
                
                <div class="mb-2">
                    <small class="text-muted d-block">Venue</small>
                    <strong>{{ $registration->summit->venue }}</strong>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted d-block">Duration</small>
                    <strong>{{ $registration->summit->start_date->format('M j') }} - {{ $registration->summit->end_date->format('M j, Y') }}</strong>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted d-block">Registration Fee</small>
                    <strong>TSh {{ number_format($registration->summit->registration_fee) }}</strong>
                </div>
                
                @if($registration->summit->description)
                <div class="mt-3">
                    <small class="text-muted d-block">Description</small>
                    <p class="small">{{ $registration->summit->description }}</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Payment History -->
@if($registration->payments->count() > 0)
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Payment History</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Reference</th>
                                <th>Amount</th>
                                <th>Method</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($registration->payments as $payment)
                            <tr>
                                <td>
                                    <div>
                                        <div class="fw-bold">#{{ $payment->payment_reference }}</div>
                                        @if($payment->transaction_id)
                                            <small class="text-muted">{{ $payment->transaction_id }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-bold text-success">TSh {{ number_format($payment->amount) }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ ucfirst($payment->payment_method) }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $payment->status === 'completed' ? 'success' : ($payment->status === 'pending' ? 'warning' : 'danger') }}">
                                        {{ ucfirst($payment->status) }}
                                    </span>
                                </td>
                                <td>
                                    <div>
                                        <div>{{ $payment->created_at->format('M j, Y') }}</div>
                                        @if($payment->paid_at)
                                            <small class="text-muted">Paid: {{ $payment->paid_at->format('M j, Y') }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <a href="{{ route('payments.show', $payment) }}" 
                                       class="btn btn-outline-primary btn-sm" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Cancel Registration Modal -->
<div class="modal fade" id="cancelRegistrationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Registration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to cancel your registration for <strong>{{ $registration->summit->title }}</strong>?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> This action cannot be undone. You will need to register again if you change your mind.
                </div>
                @if($registration->hasSuccessfulPayments())
                <div class="alert alert-danger">
                    <i class="fas fa-ban me-2"></i>
                    <strong>Note:</strong> You have made successful payments for this registration. Please contact the admin for refund processing.
                </div>
                @else
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Note:</strong> Any pending or failed payments will also be cancelled automatically.
                </div>
                @endif
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Keep Registration</button>
                <form method="POST" action="{{ route('registrations.cancel', $registration) }}" style="display: inline;">
                    @csrf
                    @method('PATCH')
                    <button type="submit" class="btn btn-danger">Cancel Registration</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function cancelRegistration() {
    const modal = new bootstrap.Modal(document.getElementById('cancelRegistrationModal'));
    modal.show();
}
</script>
@endsection
