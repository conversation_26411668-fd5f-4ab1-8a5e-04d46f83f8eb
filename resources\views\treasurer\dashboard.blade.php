@extends('layouts.user')

@section('title', 'Treasurer Dashboard')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">💰 Financial Management Dashboard</h2>
                    <p class="text-muted mb-0">Comprehensive financial oversight and reporting</p>
                </div>
                <div>
                    <a href="{{ route('treasurer.reports') }}" class="btn btn-primary me-2">
                        <i class="fas fa-chart-line me-2"></i>Financial Reports
                    </a>
                    <a href="{{ route('treasurer.analytics') }}" class="btn btn-success me-2">
                        <i class="fas fa-analytics me-2"></i>Analytics
                    </a>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-2"></i>Export
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ route('treasurer.export.payment-report') }}">
                                <i class="fas fa-file-pdf me-2"></i>Payment Report
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('treasurer.export.revenue-summary') }}">
                                <i class="fas fa-chart-bar me-2"></i>Revenue Summary
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Financial Metrics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: rgba(40, 167, 69, 0.2);">
                                <i class="fas fa-money-bill-wave fa-2x text-success"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Total Revenue</h6>
                            <h3 class="mb-0 text-success">{{ number_format($stats['total_revenue'] ?? 0, 0) }} TZS</h3>
                            <small class="text-success">
                                <i class="fas fa-arrow-up"></i> All time collections
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: rgba(255, 193, 7, 0.2);">
                                <i class="fas fa-calendar-alt fa-2x text-warning"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">This Month</h6>
                            <h3 class="mb-0 text-warning">{{ number_format($stats['monthly_revenue'] ?? 0, 0) }} TZS</h3>
                            <small class="text-warning">
                                <i class="fas fa-calendar"></i> {{ now()->format('F Y') }}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: rgba(23, 162, 184, 0.2);">
                                <i class="fas fa-check-circle fa-2x text-info"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Completed Payments</h6>
                            <h3 class="mb-0 text-info">{{ $stats['completed_payments'] ?? 0 }}</h3>
                            <small class="text-info">
                                <i class="fas fa-credit-card"></i> Successful transactions
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: rgba(220, 53, 69, 0.2);">
                                <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Outstanding Amount</h6>
                            <h3 class="mb-0 text-danger">{{ number_format($stats['outstanding_amount'] ?? 0, 0) }} TZS</h3>
                            <small class="text-danger">
                                <i class="fas fa-clock"></i> Pending collections
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Statistics Row -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: rgba(108, 117, 125, 0.2);">
                                <i class="fas fa-users fa-2x text-secondary"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Total Registrations</h6>
                            <h3 class="mb-0 text-secondary">{{ $stats['total_registrations'] ?? 0 }}</h3>
                            <small class="text-secondary">
                                <i class="fas fa-user-plus"></i> All registrations
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: rgba(40, 167, 69, 0.2);">
                                <i class="fas fa-user-check fa-2x text-success"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Paid Registrations</h6>
                            <h3 class="mb-0 text-success">{{ $stats['paid_registrations'] ?? 0 }}</h3>
                            <small class="text-success">
                                <i class="fas fa-check"></i> Payment completed
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: rgba(255, 193, 7, 0.2);">
                                <i class="fas fa-calendar-day fa-2x text-warning"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Payments Today</h6>
                            <h3 class="mb-0 text-warning">{{ $stats['payments_today'] ?? 0 }}</h3>
                            <small class="text-warning">
                                <i class="fas fa-clock"></i> {{ now()->format('M d, Y') }}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: rgba(23, 162, 184, 0.2);">
                                <i class="fas fa-percentage fa-2x text-info"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Success Rate</h6>
                            <h3 class="mb-0 text-info">{{ number_format($stats['payment_success_rate'] ?? 0, 1) }}%</h3>
                            <small class="text-info">
                                <i class="fas fa-chart-line"></i> Payment success
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Status Overview -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2 text-primary"></i>Monthly Revenue Trend
                        </h5>
                        <div class="text-end">
                            <small class="text-muted">Last 12 months</small>
                            @if($monthlyTrends->isEmpty())
                                <br><small class="text-warning"><i class="fas fa-info-circle"></i> Sample data shown</small>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="revenueChart" height="100"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2 text-success"></i>Payment Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="p-3">
                                <h4 class="text-success mb-1">{{ $stats['completed_payments'] ?? 0 }}</h4>
                                <small class="text-muted">Completed</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="p-3">
                                <h4 class="text-warning mb-1">{{ $stats['pending_payments'] ?? 0 }}</h4>
                                <small class="text-muted">Pending</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="p-3">
                                <h4 class="text-danger mb-1">{{ $stats['failed_payments'] ?? 0 }}</h4>
                                <small class="text-muted">Failed</small>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="text-center">
                        <h6 class="text-muted mb-2">Collection Rate</h6>
                        <h3 class="text-primary">
                            {{ $stats['completed_payments'] > 0 ? round(($stats['completed_payments'] / ($stats['completed_payments'] + $stats['pending_payments'] + $stats['failed_payments'])) * 100, 1) : 0 }}%
                        </h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Transactions and Top Branches -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2 text-info"></i>Recent Transactions
                        </h5>
                        <a href="{{ route('treasurer.payment-reports') }}" class="btn btn-sm btn-outline-primary">
                            View All
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    @if($recentPayments && $recentPayments->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="bg-light">
                                    <tr>
                                        <th class="border-0">Participant</th>
                                        <th class="border-0">Amount</th>
                                        <th class="border-0">Status</th>
                                        <th class="border-0">Date</th>
                                        <th class="border-0">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentPayments as $payment)
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ $payment->user->full_name }}</strong><br>
                                                <small class="text-muted">{{ $payment->user->branch->name ?? 'N/A' }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <strong class="text-success">{{ number_format($payment->amount, 0) }} TZS</strong>
                                        </td>
                                        <td>
                                            @if($payment->status === 'completed')
                                                <span class="badge bg-success">Completed</span>
                                            @elseif($payment->status === 'pending')
                                                <span class="badge bg-warning">Pending</span>
                                            @else
                                                <span class="badge bg-danger">Failed</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small>{{ $payment->created_at->format('M j, H:i') }}</small>
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.payments.show', $payment) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No recent transactions found.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-trophy me-2 text-warning"></i>Top Performing Branches
                    </h5>
                </div>
                <div class="card-body">
                    @if($paymentsByBranch && $paymentsByBranch->count() > 0)
                        @foreach($paymentsByBranch->take(5) as $index => $branch)
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0">
                                <div class="rounded-circle d-flex align-items-center justify-content-center" 
                                     style="width: 40px; height: 40px; background: {{ $index === 0 ? '#ffd700' : ($index === 1 ? '#c0c0c0' : ($index === 2 ? '#cd7f32' : '#e9ecef')) }};">
                                    <strong style="color: {{ $index < 3 ? 'white' : '#6c757d' }};">{{ $index + 1 }}</strong>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">{{ Str::limit($branch->name, 25) }}</h6>
                                <small class="text-muted">{{ $branch->count }} payments</small>
                            </div>
                            <div class="text-end">
                                <strong class="text-success">{{ number_format($branch->total, 0) }} TZS</strong>
                            </div>
                        </div>
                        @if(!$loop->last)
                            <hr class="my-2">
                        @endif
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No payment data available.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .card {
        transition: transform 0.2s, box-shadow 0.2s;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }
    
    .table th {
        font-weight: 600;
        color: #6c757d;
        font-size: 0.875rem;
    }
    
    .badge {
        font-size: 0.75rem;
        padding: 0.5em 0.75em;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const ctx = document.getElementById('revenueChart');
    if (!ctx) {
        console.error('Revenue chart canvas not found');
        return;
    }

    // Prepare chart data
    const monthlyData = @json($monthlyTrends ?? collect());
    console.log('Monthly data:', monthlyData);

    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    // Use sample data if no real data exists
    let labels, data;
    if (monthlyData.length === 0) {
        // Sample data for demonstration
        labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'];
        data = [50000, 75000, 60000, 90000, 80000, 95000, 85000];
        console.log('Using sample data');
    } else {
        labels = monthlyData.map(item => monthNames[parseInt(item.month) - 1] || 'Unknown');
        data = monthlyData.map(item => parseFloat(item.total) || 0);
        console.log('Using real data - Labels:', labels, 'Data:', data);
    }

    try {
        const revenueChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Revenue (TZS)',
                    data: data,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return new Intl.NumberFormat().format(value) + ' TZS';
                            }
                        }
                    }
                }
            }
        });
        console.log('Chart created successfully');
    } catch (error) {
        console.error('Error creating chart:', error);
    }
});
</script>
@endpush
