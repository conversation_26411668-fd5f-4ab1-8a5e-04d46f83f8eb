@extends('layouts.admin')

@section('title', 'Branches Management')
@section('page-title', 'Branches Management')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">Branches Management</h2>
                <p class="text-muted">Manage branches in the system.</p>
            </div>
            <a href="{{ route('admin.branches.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add Branch
            </a>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-search me-2"></i>Search & Filter</h5>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.branches.index') }}">
                    <div class="row">
                        <div class="col-md-4">
                            <input type="text" name="search" class="form-control" 
                                   placeholder="Search by name or code..." 
                                   value="{{ request('search') }}">
                        </div>
                        <div class="col-md-3">
                            <select name="conference_id" class="form-select">
                                <option value="">All Conferences</option>
                                @foreach($conferences as $conference)
                                    <option value="{{ $conference->id }}" {{ request('conference_id') == $conference->id ? 'selected' : '' }}>
                                        {{ $conference->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select name="university_id" class="form-select">
                                <option value="">All Universities</option>
                                @foreach($universities as $university)
                                    <option value="{{ $university->id }}" {{ request('university_id') == $university->id ? 'selected' : '' }}>
                                        {{ $university->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Search
                                </button>
                                <a href="{{ route('admin.branches.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>Clear
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Branches Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>Branches ({{ $branches->total() }})</h5>
            </div>
            <div class="card-body">
                @if($branches->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Branch</th>
                                <th>Code</th>
                                <th>Zone/Conference</th>
                                <th>University</th>
                                <th>Users</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($branches as $branch)
                            <tr>
                                <td>
                                    <div>
                                        <div class="fw-bold">{{ $branch->name }}</div>
                                        @if($branch->description)
                                            <small class="text-muted">{{ Str::limit($branch->description, 50) }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ $branch->code }}</span>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-bold">{{ $branch->zone->name ?? 'N/A' }}</div>
                                        <small class="text-muted">{{ $branch->zone->conference->name ?? 'N/A' }}</small>
                                    </div>
                                </td>
                                <td>{{ $branch->university->name ?? 'N/A' }}</td>
                                <td>
                                    <div class="text-center">
                                        <span class="badge bg-info">{{ $branch->users->count() }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $branch->is_active ? 'success' : 'secondary' }}">
                                        {{ $branch->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('admin.branches.show', $branch) }}" 
                                           class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.branches.edit', $branch) }}" 
                                           class="btn btn-outline-secondary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @if(!$branch->users->count())
                                        <form method="POST" action="{{ route('admin.branches.destroy', $branch) }}" 
                                              class="d-inline" onsubmit="return confirm('Are you sure you want to delete this branch?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-outline-danger" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $branches->withQueryString()->links() }}
                </div>
                @else
                <div class="text-center py-4">
                    <i class="fas fa-code-branch fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No branches found</h5>
                    <p class="text-muted">
                        @if(request('search'))
                            No branches match your search criteria.
                        @else
                            Get started by adding your first branch.
                        @endif
                    </p>
                    @if(!request('search'))
                    <a href="{{ route('admin.branches.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Branch
                    </a>
                    @endif
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
