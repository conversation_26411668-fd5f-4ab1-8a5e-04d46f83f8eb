<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class GateEntry extends Model
{
    protected $fillable = [
        'user_id',
        'summit_id',
        'gate_location',
        'entry_type',
        'scanned_at',
        'scanned_by',
        'notes'
    ];

    protected $casts = [
        'scanned_at' => 'datetime'
    ];

    /**
     * Get the user that owns the gate entry.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the summit that owns the gate entry.
     */
    public function summit(): BelongsTo
    {
        return $this->belongsTo(Summit::class);
    }
}
