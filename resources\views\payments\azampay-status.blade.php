@extends('layouts.app')

@section('title', 'Payment Status')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-{{ $payment->status === 'completed' ? 'success' : ($payment->status === 'failed' ? 'danger' : 'primary') }} text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-{{ $payment->status === 'completed' ? 'check-circle' : ($payment->status === 'failed' ? 'times-circle' : 'clock') }} me-2"></i>
                        Payment Status
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Payment Status -->
                    <div class="text-center mb-4">
                        @if($payment->status === 'completed')
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                <h4>Payment Successful!</h4>
                                <p class="mb-0">Your payment has been completed successfully.</p>
                            </div>
                        @elseif($payment->status === 'failed')
                            <div class="alert alert-danger">
                                <i class="fas fa-times-circle fa-3x text-danger mb-3"></i>
                                <h4>Payment Failed</h4>
                                <p class="mb-0">Your payment could not be processed. Please try again.</p>
                            </div>
                        @elseif($payment->status === 'pending_verification')
                            <div class="alert alert-warning">
                                <i class="fas fa-clock fa-3x text-warning mb-3"></i>
                                <h4>Payment Under Verification</h4>
                                <p class="mb-0">Your payment has been submitted and is being verified by our admin team.</p>
                            </div>
                        @elseif($payment->status === 'processing')
                            <div class="alert alert-info">
                                <i class="fas fa-spinner fa-spin fa-3x text-info mb-3"></i>
                                <h4>Payment Processing</h4>
                                <p class="mb-0">Please complete the payment on your device.</p>
                            </div>
                        @else
                            <div class="alert alert-secondary">
                                <i class="fas fa-clock fa-3x text-secondary mb-3"></i>
                                <h4>Payment Pending</h4>
                                <p class="mb-0">Your payment is being initialized.</p>
                            </div>
                        @endif
                    </div>

                    <!-- Payment Details -->
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Payment Information</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Reference:</strong></td>
                                    <td>{{ $payment->payment_reference }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Amount:</strong></td>
                                    <td><strong class="text-success">TZS {{ number_format($payment->amount, 0) }}</strong></td>
                                </tr>
                                <tr>
                                    <td><strong>Method:</strong></td>
                                    <td>
                                        @switch($payment->payment_method)
                                            @case('azampay_mobile')
                                                <i class="fas fa-mobile-alt text-primary"></i> Mobile Money (AzamPay)
                                                @break
                                            @case('azampay_bank')
                                                <i class="fas fa-university text-success"></i> Bank Transfer (AzamPay)
                                                @break
                                            @case('cash')
                                                <i class="fas fa-money-bill-wave text-warning"></i> Cash Payment
                                                @break
                                            @case('mobile_money_direct')
                                                <i class="fas fa-phone text-info"></i> Direct Mobile Money
                                                @break
                                            @default
                                                {{ ucfirst(str_replace('_', ' ', $payment->payment_method)) }}
                                        @endswitch
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge bg-{{ $payment->status === 'completed' ? 'success' : ($payment->status === 'failed' ? 'danger' : 'warning') }}">
                                            {{ ucfirst(str_replace('_', ' ', $payment->status)) }}
                                        </span>
                                    </td>
                                </tr>
                                @if($payment->paid_at)
                                <tr>
                                    <td><strong>Paid At:</strong></td>
                                    <td>{{ $payment->paid_at->format('M d, Y H:i') }}</td>
                                </tr>
                                @endif
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>Registration Details</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Summit:</strong></td>
                                    <td>{{ $payment->summitRegistration->summit->title }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Registration:</strong></td>
                                    <td>{{ $payment->summitRegistration->registration_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Total Amount:</strong></td>
                                    <td>TZS {{ number_format($payment->summitRegistration->total_amount, 0) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Amount Paid:</strong></td>
                                    <td class="text-success">TZS {{ number_format($payment->summitRegistration->paid_amount, 0) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Balance:</strong></td>
                                    <td class="{{ $payment->summitRegistration->balance > 0 ? 'text-danger' : 'text-success' }}">
                                        TZS {{ number_format($payment->summitRegistration->balance, 0) }}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Payment Method Specific Information -->
                    @if($payment->gateway_data)
                        <div class="mt-4">
                            <h6>Payment Details</h6>
                            <div class="card bg-light">
                                <div class="card-body">
                                    @if($payment->payment_method === 'azampay_mobile' && isset($payment->gateway_data['provider']))
                                        <p><strong>Provider:</strong> {{ $payment->gateway_data['provider'] }}</p>
                                        @if(isset($payment->gateway_data['phone_number']))
                                            <p><strong>Phone:</strong> {{ $payment->gateway_data['phone_number'] }}</p>
                                        @endif
                                    @elseif($payment->payment_method === 'azampay_bank' && isset($payment->gateway_data['bank_code']))
                                        <p><strong>Bank:</strong> {{ $payment->gateway_data['bank_code'] }}</p>
                                    @elseif($payment->payment_method === 'cash')
                                        @if(isset($payment->gateway_data['received_by']))
                                            <p><strong>Received By:</strong> {{ $payment->gateway_data['received_by'] }}</p>
                                        @endif
                                        @if(isset($payment->gateway_data['receipt_number']))
                                            <p><strong>Receipt Number:</strong> {{ $payment->gateway_data['receipt_number'] }}</p>
                                        @endif
                                    @elseif($payment->payment_method === 'mobile_money_direct')
                                        @if(isset($payment->gateway_data['mno_provider']))
                                            <p><strong>Provider:</strong> {{ $payment->gateway_data['mno_provider'] }}</p>
                                        @endif
                                        @if(isset($payment->gateway_data['transaction_id']))
                                            <p><strong>Transaction ID:</strong> {{ $payment->gateway_data['transaction_id'] }}</p>
                                        @endif
                                    @endif
                                    
                                    @if($payment->transaction_id)
                                        <p><strong>Transaction ID:</strong> {{ $payment->transaction_id }}</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Action Buttons -->
                    <div class="mt-4 d-flex gap-2 justify-content-center">
                        @if($payment->status === 'completed')
                            <a href="{{ route('dashboard') }}" class="btn btn-success">
                                <i class="fas fa-home me-2"></i>Go to Dashboard
                            </a>
                            @if($payment->summitRegistration->payment_complete)
                                <a href="{{ route('id-cards.index') }}" class="btn btn-primary">
                                    <i class="fas fa-id-card me-2"></i>View ID Card
                                </a>
                            @endif
                        @elseif($payment->status === 'failed')
                            <a href="{{ route('payments.create', $payment->summitRegistration) }}" class="btn btn-primary">
                                <i class="fas fa-redo me-2"></i>Try Again
                            </a>
                            <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-home me-2"></i>Go to Dashboard
                            </a>
                        @elseif(in_array($payment->status, ['pending', 'processing']))
                            <button type="button" class="btn btn-primary" onclick="checkPaymentStatus()">
                                <i class="fas fa-sync me-2"></i>Check Status
                            </button>
                            <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-home me-2"></i>Go to Dashboard
                            </a>
                        @else
                            <a href="{{ route('dashboard') }}" class="btn btn-primary">
                                <i class="fas fa-home me-2"></i>Go to Dashboard
                            </a>
                        @endif
                    </div>

                    <!-- Auto-refresh for processing payments -->
                    @if(in_array($payment->status, ['pending', 'processing']))
                        <div class="mt-3 text-center">
                            <small class="text-muted">
                                <i class="fas fa-sync fa-spin me-1"></i>
                                This page will automatically refresh every 10 seconds
                            </small>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Help Section -->
            <div class="card mt-3">
                <div class="card-body">
                    <h6><i class="fas fa-question-circle text-info me-2"></i>Need Help?</h6>
                    <p class="small text-muted mb-2">
                        If you're experiencing issues with your payment, please contact our support team:
                    </p>
                    <ul class="small text-muted mb-0">
                        <li><strong>Email:</strong> <EMAIL></li>
                        <li><strong>Phone:</strong> +255 123 456 789</li>
                        <li><strong>WhatsApp:</strong> +255 123 456 789</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function checkPaymentStatus() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Checking...';
    
    fetch('{{ route("payments.azampay.check", $payment) }}')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'completed' || data.status === 'failed') {
                location.reload();
            } else {
                btn.disabled = false;
                btn.innerHTML = originalText;
                
                // Show status message
                const alert = document.createElement('div');
                alert.className = 'alert alert-info mt-3';
                alert.innerHTML = '<i class="fas fa-info-circle me-2"></i>' + data.message;
                btn.parentNode.appendChild(alert);
                
                setTimeout(() => alert.remove(), 5000);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            btn.disabled = false;
            btn.innerHTML = originalText;
        });
}

// Auto-refresh for processing payments
@if(in_array($payment->status, ['pending', 'processing']))
    setTimeout(() => {
        location.reload();
    }, 10000); // Refresh every 10 seconds
@endif
</script>
@endsection
