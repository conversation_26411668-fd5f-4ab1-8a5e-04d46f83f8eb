@extends('layouts.admin')

@section('title', 'Summit Details')
@section('page-title', 'Summit Details')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">{{ $summit->title }}</h2>
                <p class="text-muted">{{ $summit->theme }}</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('admin.summits.edit', $summit) }}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>Edit Summit
                </a>
                <a href="{{ route('admin.summits.index') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Summits
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Summit Information -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-mountain me-2"></i>Summit Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Title:</strong></td>
                                <td>{{ $summit->title }}</td>
                            </tr>
                            <tr>
                                <td><strong>Year:</strong></td>
                                <td><span class="badge bg-primary">{{ $summit->year }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Theme:</strong></td>
                                <td>{{ $summit->theme }}</td>
                            </tr>
                            <tr>
                                <td><strong>Venue:</strong></td>
                                <td>{{ $summit->venue }}</td>
                            </tr>
                            <tr>
                                <td><strong>Address:</strong></td>
                                <td>{{ $summit->venue_address }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Start Date:</strong></td>
                                <td>{{ $summit->start_date->format('F j, Y') }}</td>
                            </tr>
                            <tr>
                                <td><strong>End Date:</strong></td>
                                <td>{{ $summit->end_date->format('F j, Y') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Duration:</strong></td>
                                <td>{{ $summit->start_date->diffInDays($summit->end_date) + 1 }} days</td>
                            </tr>
                            <tr>
                                <td><strong>Registration Fee:</strong></td>
                                <td><span class="fw-bold text-success">TSh {{ number_format($summit->registration_fee) }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Registration Deadline:</strong></td>
                                <td>{{ $summit->registration_deadline->format('F j, Y') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                @if($summit->description)
                <div class="mt-3">
                    <h6>Description</h6>
                    <p class="text-muted">{{ $summit->description }}</p>
                </div>
                @endif
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Summit Statistics</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Total Registrations</span>
                    <span class="badge bg-info">{{ $summit->registrations->count() }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Confirmed Registrations</span>
                    <span class="badge bg-success">{{ $summit->registrations->where('status', 'confirmed')->count() }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Pending Registrations</span>
                    <span class="badge bg-warning">{{ $summit->registrations->where('status', 'pending')->count() }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Total Revenue</span>
                    <span class="badge bg-primary">TSh {{ number_format($summit->registrations->where('payment_complete', true)->sum('paid_amount')) }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>ID Cards Issued</span>
                    <span class="badge bg-secondary">{{ $summit->idCards->count() }}</span>
                </div>
            </div>
        </div>
        
        <!-- Status -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-toggle-on me-2"></i>Status</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Active</span>
                    <span class="badge bg-{{ $summit->is_active ? 'success' : 'secondary' }}">
                        {{ $summit->is_active ? 'Yes' : 'No' }}
                    </span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Current Summit</span>
                    <span class="badge bg-{{ $summit->is_current ? 'success' : 'secondary' }}">
                        {{ $summit->is_current ? 'Yes' : 'No' }}
                    </span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Created</span>
                    <span class="text-muted">{{ $summit->created_at->format('M j, Y') }}</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Registrations -->
@if($summit->registrations->count() > 0)
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-users me-2"></i>Recent Registrations ({{ $summit->registrations->count() }})</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Registration #</th>
                                <th>User</th>
                                <th>Branch</th>
                                <th>Amount</th>
                                <th>Payment Status</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($summit->registrations->take(10) as $registration)
                            <tr>
                                <td>
                                    <span class="badge bg-primary">{{ $registration->registration_number }}</span>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-bold">{{ $registration->user->full_name }}</div>
                                        <small class="text-muted">{{ $registration->user->email }}</small>
                                    </div>
                                </td>
                                <td>{{ $registration->user->branch->name ?? 'N/A' }}</td>
                                <td>
                                    <div>
                                        <div class="fw-bold">TSh {{ number_format($registration->total_amount) }}</div>
                                        @if($registration->balance > 0)
                                            <small class="text-warning">Balance: TSh {{ number_format($registration->balance) }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $registration->payment_complete ? 'success' : 'warning' }}">
                                        {{ $registration->payment_complete ? 'Paid' : 'Pending' }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $registration->status === 'confirmed' ? 'success' : ($registration->status === 'pending' ? 'warning' : 'danger') }}">
                                        {{ ucfirst($registration->status) }}
                                    </span>
                                </td>
                                <td>{{ $registration->registered_at->format('M j, Y') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('admin.registrations.show', $registration) }}" 
                                           class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                @if($summit->registrations->count() > 10)
                <div class="text-center mt-3">
                    <a href="{{ route('admin.registrations.index', ['summit_id' => $summit->id]) }}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>View All Registrations ({{ $summit->registrations->count() }})
                    </a>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endif
@endsection
