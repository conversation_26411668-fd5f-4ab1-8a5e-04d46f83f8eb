@extends('layouts.user')

@section('title', 'ID Card Preview')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">ID Card Print Preview</h2>
                <p class="text-muted">Preview your ID card before printing</p>
            </div>
            <div class="d-flex gap-2">
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="fas fa-print me-2"></i>Print Card
                </button>
                <a href="{{ route('id-cards.download', $idCard) }}" class="btn btn-success">
                    <i class="fas fa-download me-2"></i>Download PDF
                </a>
                <a href="{{ route('id-cards.show', $idCard) }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Print Instructions -->
<div class="row mb-4 no-print">
    <div class="col-12">
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>Printing Instructions</h6>
            <ul class="mb-0">
                <li>Use standard A4 paper (210 × 297 mm)</li>
                <li>Set printer to "Actual Size" or "100%" scale</li>
                <li>Use high-quality print settings for best results</li>
                <li>Cut along the dotted lines after printing</li>
                <li>Consider laminating for durability</li>
            </ul>
        </div>
    </div>
</div>

<!-- Printable ID Card -->
<div class="row justify-content-center">
    <div class="col-12">
        <div class="card-container">
            <div class="id-card">
                <!-- Header Section -->
                <div class="header-section">
                    <div class="header-title">{{ $idCard->summit->title }}</div>
                    <div class="header-year">{{ $idCard->summit->year }}</div>
                </div>

                <!-- Card Body -->
                <div class="card-body">
                    <!-- Profile Image -->
                    @if($idCard->user->profile_image)
                        <img src="{{ Storage::url($idCard->user->profile_image) }}" class="profile-image" alt="Profile Image">
                    @else
                        <div class="profile-placeholder">
                            <i class="fas fa-user"></i>
                        </div>
                    @endif

                    <!-- User Name -->
                    <div class="user-name">{{ $idCard->user->full_name }}</div>

                    <!-- User Email -->
                    <div class="user-email">{{ $idCard->user->email }}</div>

                    <!-- User Phone -->
                    @if($idCard->user->phone)
                    <div class="user-phone">{{ $idCard->user->phone }}</div>
                    @endif

                    <!-- Card Number -->
                    <div class="card-number">{{ $idCard->card_number }}</div>
                    <!-- Institution Info -->
                    <div class="institution-info">
                        <div class="institution-name">{{ $idCard->user->university->name ?? 'N/A' }}</div>
                        <div class="conference-name">{{ $idCard->user->branch->zone->conference->name ?? 'N/A' }}</div>
                    </div>

                    <!-- QR Code -->
                    @if($idCard->qr_code_path && Storage::disk('public')->exists($idCard->qr_code_path))
                        <img src="{{ Storage::url($idCard->qr_code_path) }}" class="qr-code" alt="QR Code">
                    @endif

                    <!-- Status Badge -->
                    <div class="status-badge">Active</div>

                    <!-- Expiry Info -->
                    <div class="expiry-info">
                        Expires: {{ $idCard->expires_at->format('M j, Y') }}
                    </div>
                </div>
            </div>
            
            <!-- Cutting Guidelines -->
            <div class="cutting-guide no-print">
                <p class="text-center text-muted mt-3">
                    <i class="fas fa-scissors me-2"></i>
                    Cut along the dotted border to create your ID card
                </p>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
        margin: 0;
        padding: 0;
    }
    
    .container-fluid {
        padding: 0 !important;
    }
    
    .print-container {
        page-break-inside: avoid;
    }
}

/* ID Card Styles */
.card-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.id-card {
    width: 300px;
    height: 450px;
    background: #f8f9fa;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    position: relative;
    border: 1px solid #e0e0e0;
}

.header-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 20px 15px;
    border-radius: 20px 20px 0 0;
}

.header-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
    letter-spacing: 0.5px;
    color: white;
}

.header-year {
    font-size: 14px;
    font-weight: normal;
    color: white;
}

.card-body {
    padding: 25px 20px;
    text-align: center;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.profile-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 3px solid #667eea;
    object-fit: cover;
    margin-bottom: 15px;
}

.profile-placeholder {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 3px solid #667eea;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    color: #999;
    margin-bottom: 15px;
}

.user-name {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.user-email {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.user-phone {
    font-size: 12px;
    color: #666;
    margin-bottom: 15px;
}

.card-number {
    background: #333;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 15px;
    letter-spacing: 1px;
}

.institution-info {
    text-align: center;
    margin-bottom: 20px;
}

.institution-name {
    font-size: 13px;
    color: #666;
    margin-bottom: 3px;
    font-weight: 500;
}

.conference-name {
    font-size: 13px;
    color: #666;
    font-weight: 500;
}

.qr-code {
    width: 80px;
    height: 80px;
    margin: 15px auto;
    background: white;
    padding: 5px;
    border-radius: 8px;
    border: 1px solid #ddd;
}

.status-badge {
    background: #28a745;
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 11px;
    font-weight: bold;
    margin-bottom: 15px;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.status-badge::before {
    content: "●";
    font-size: 8px;
}

.expiry-info {
    font-size: 12px;
    color: #333;
    font-weight: bold;
    margin-top: auto;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}



/* Screen-only styles */
@media screen {
    .cutting-guide {
        margin-top: 20px;
    }
    
    .id-card {
        transform: scale(1.2);
        margin: 40px auto;
    }

    .card-container {
        padding: 80px 20px;
    }
}
</style>
@endpush
