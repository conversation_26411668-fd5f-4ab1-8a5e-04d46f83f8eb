@extends('layouts.admin')

@section('title', 'ID Cards Management')
@section('page-title', 'ID Cards Management')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">ID Cards Management</h2>
                <p class="text-muted">Manage and monitor summit ID cards.</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('admin.id-cards.export') }}" class="btn btn-success">
                    <i class="fas fa-download me-2"></i>Export CSV
                </a>
                <a href="{{ route('admin.id-cards.statistics') }}" class="btn btn-info">
                    <i class="fas fa-chart-bar me-2"></i>Statistics
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="modern-stats-card blue h-100">
            <div class="stat-icon">
                <i class="fas fa-id-card"></i>
            </div>
            <h3 class="stat-number">{{ number_format($statistics['total_cards']) }}</h3>
            <p class="stat-label">Total Cards</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="modern-stats-card green h-100">
            <div class="stat-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h3 class="stat-number">{{ number_format($statistics['active_cards']) }}</h3>
            <p class="stat-label">Active Cards</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="modern-stats-card orange h-100">
            <div class="stat-icon">
                <i class="fas fa-clock"></i>
            </div>
            <h3 class="stat-number">{{ number_format($statistics['expired_cards']) }}</h3>
            <p class="stat-label">Expired Cards</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="modern-stats-card purple h-100">
            <div class="stat-icon">
                <i class="fas fa-calendar"></i>
            </div>
            <h3 class="stat-number">{{ number_format($statistics['cards_this_month']) }}</h3>
            <p class="stat-label">This Month</p>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="payment-filter-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.id-cards.index') }}">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" name="search" id="search" class="form-control" 
                                       value="{{ request('search') }}" placeholder="Card number, name, email...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="summit_id" class="form-label">Summit</label>
                                <select name="summit_id" id="summit_id" class="form-select">
                                    <option value="">All Summits</option>
                                    @foreach($summits as $summit)
                                        <option value="{{ $summit->id }}" {{ request('summit_id') == $summit->id ? 'selected' : '' }}>
                                            {{ $summit->title }} ({{ $summit->year }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select name="status" id="status" class="form-select">
                                    <option value="">All Status</option>
                                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                    <option value="expired" {{ request('status') == 'expired' ? 'selected' : '' }}>Expired</option>
                                    <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>Filter
                                    </button>
                                    <a href="{{ route('admin.id-cards.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>Clear
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- ID Cards Table -->
<div class="row">
    <div class="col-12">
        <div class="payment-table-card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-table me-2"></i>ID Cards ({{ $idCards->total() }})</h5>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="bulkAction('deactivate')" disabled id="bulkActionBtn">
                            <i class="fas fa-ban me-2"></i>Bulk Deactivate
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                @if($idCards->count() > 0)
                <form id="bulkForm" method="POST" action="{{ route('admin.id-cards.bulk-action') }}">
                    @csrf
                    <input type="hidden" name="action" id="bulkAction">
                    
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th>Card Number</th>
                                    <th>User</th>
                                    <th>Summit</th>
                                    <th>Branch</th>
                                    <th>Status</th>
                                    <th>Issued</th>
                                    <th>Expires</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($idCards as $card)
                                <tr>
                                    <td>
                                        <input type="checkbox" name="card_ids[]" value="{{ $card->id }}" class="form-check-input card-checkbox">
                                    </td>
                                    <td>
                                        <span class="badge bg-dark">{{ $card->card_number }}</span>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-bold">{{ $card->user->full_name }}</div>
                                            <small class="text-muted">{{ $card->user->email }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-bold">{{ $card->summit->title }}</div>
                                            <small class="text-muted">{{ $card->summit->year }}</small>
                                        </div>
                                    </td>
                                    <td>{{ $card->user->branch->name ?? 'N/A' }}</td>
                                    <td>
                                        @if($card->is_active && $card->expires_at > now())
                                            <span class="badge bg-success">Active</span>
                                        @elseif($card->expires_at < now())
                                            <span class="badge bg-warning">Expired</span>
                                        @else
                                            <span class="badge bg-secondary">Inactive</span>
                                        @endif
                                    </td>
                                    <td>{{ $card->issued_at->format('M j, Y') }}</td>
                                    <td>{{ $card->expires_at->format('M j, Y') }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('admin.id-cards.show', $card) }}"
                                               class="btn btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if($card->is_active)
                                                <form method="POST" action="{{ route('admin.id-cards.deactivate', $card) }}" class="d-inline">
                                                    @csrf
                                                    @method('PATCH')
                                                    <button type="submit" class="btn btn-outline-warning" title="Deactivate"
                                                            onclick="return confirm('Deactivate this ID card?')">
                                                        <i class="fas fa-ban"></i>
                                                    </button>
                                                </form>
                                            @else
                                                <form method="POST" action="{{ route('admin.id-cards.reactivate', $card) }}" class="d-inline">
                                                    @csrf
                                                    @method('PATCH')
                                                    <button type="submit" class="btn btn-outline-success" title="Reactivate">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </form>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $idCards->appends(request()->query())->links() }}
                </div>
                @else
                <div class="text-center py-5">
                    <i class="fas fa-id-card fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No ID Cards Found</h5>
                    <p class="text-muted">No ID cards match your current filters.</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.card-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateBulkActionButton();
});

// Update bulk action button state
document.querySelectorAll('.card-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateBulkActionButton);
});

function updateBulkActionButton() {
    const checkedBoxes = document.querySelectorAll('.card-checkbox:checked');
    const bulkBtn = document.getElementById('bulkActionBtn');
    
    if (checkedBoxes.length > 0) {
        bulkBtn.disabled = false;
        bulkBtn.textContent = `Bulk Action (${checkedBoxes.length})`;
    } else {
        bulkBtn.disabled = true;
        bulkBtn.textContent = 'Bulk Action';
    }
}

function bulkAction(action) {
    const checkedBoxes = document.querySelectorAll('.card-checkbox:checked');
    if (checkedBoxes.length === 0) {
        alert('Please select at least one ID card.');
        return;
    }
    
    if (confirm(`Are you sure you want to ${action} ${checkedBoxes.length} ID card(s)?`)) {
        document.getElementById('bulkAction').value = action;
        document.getElementById('bulkForm').submit();
    }
}
</script>
@endpush
