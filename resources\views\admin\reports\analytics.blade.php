@extends(auth()->user()->hasRole('admin') ? 'layouts.admin' : 'layouts.user')

@section('title', 'Analytics Dashboard')
@if(auth()->user()->hasRole('admin'))
@section('page-title', 'Analytics Dashboard')
@endif

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">Analytics Dashboard</h2>
                <p class="text-muted">Interactive charts and comprehensive data visualization.</p>
            </div>
            <a href="{{ route('admin.reports.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Reports
            </a>
        </div>
    </div>
</div>

<!-- Charts Row 1 -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Monthly Registration Trends ({{ date('Y') }})</h5>
            </div>
            <div class="card-body">
                <canvas id="registrationsChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Registrations by Conference</h5>
            </div>
            <div class="card-body">
                @if($registrationsByConference->count() > 0)
                <canvas id="conferenceChart" height="200"></canvas>
                @else
                <div class="text-center py-4">
                    <i class="fas fa-chart-pie fa-2x text-muted mb-2"></i>
                    <p class="text-muted">No data available</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Charts Row 2 -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-area me-2"></i>Monthly Payment Trends ({{ date('Y') }})</h5>
            </div>
            <div class="card-body">
                <canvas id="paymentsChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Top Performing Branches</h5>
            </div>
            <div class="card-body">
                @if($paymentsByBranch->count() > 0)
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Branch</th>
                                <th class="text-end">Revenue</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($paymentsByBranch as $branch)
                            <tr>
                                <td>{{ Str::limit($branch->name, 20) }}</td>
                                <td class="text-end fw-bold text-success">TSh {{ number_format($branch->total) }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="text-center py-4">
                    <i class="fas fa-chart-bar fa-2x text-muted mb-2"></i>
                    <p class="text-muted">No data available</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Conference Performance Table -->
@if($registrationsByConference->count() > 0)
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>Conference Performance Summary</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Conference</th>
                                <th class="text-center">Registrations</th>
                                <th class="text-center">Percentage</th>
                                <th class="text-center">Performance</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php
                                $totalRegistrations = $registrationsByConference->sum('total');
                            @endphp
                            @foreach($registrationsByConference as $conference)
                            @php
                                $percentage = $totalRegistrations > 0 ? ($conference->total / $totalRegistrations) * 100 : 0;
                            @endphp
                            <tr>
                                <td>
                                    <div class="fw-bold">{{ $conference->name }}</div>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-primary">{{ number_format($conference->total) }}</span>
                                </td>
                                <td class="text-center">
                                    <span class="fw-bold">{{ number_format($percentage, 1) }}%</span>
                                </td>
                                <td class="text-center">
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-{{ $percentage > 30 ? 'success' : ($percentage > 15 ? 'warning' : 'danger') }}" 
                                             role="progressbar" 
                                             style="width: {{ $percentage }}%"
                                             aria-valuenow="{{ $percentage }}" 
                                             aria-valuemin="0" 
                                             aria-valuemax="100">
                                            {{ number_format($percentage, 1) }}%
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endif
@endsection

@push('scripts')
<script>
// Monthly Registrations Chart
const registrationsCtx = document.getElementById('registrationsChart').getContext('2d');
const monthlyRegistrationsData = @json($monthlyRegistrations);

// Prepare data for all 12 months
const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
const registrationsData = [];
for (let i = 1; i <= 12; i++) {
    registrationsData.push(monthlyRegistrationsData[i] || 0);
}

new Chart(registrationsCtx, {
    type: 'line',
    data: {
        labels: months,
        datasets: [{
            label: 'Registrations',
            data: registrationsData,
            borderColor: 'rgb(102, 126, 234)',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// Monthly Payments Chart
const paymentsCtx = document.getElementById('paymentsChart').getContext('2d');
const monthlyPaymentsData = @json($monthlyPayments);

// Prepare payments data for all 12 months
const paymentsData = [];
for (let i = 1; i <= 12; i++) {
    paymentsData.push(monthlyPaymentsData[i] || 0);
}

new Chart(paymentsCtx, {
    type: 'bar',
    data: {
        labels: months,
        datasets: [{
            label: 'Revenue (TSh)',
            data: paymentsData,
            backgroundColor: 'rgba(40, 167, 69, 0.8)',
            borderColor: 'rgb(40, 167, 69)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return 'TSh ' + value.toLocaleString();
                    }
                }
            }
        },
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return 'Revenue: TSh ' + context.parsed.y.toLocaleString();
                    }
                }
            }
        }
    }
});

// Conference Pie Chart
@if($registrationsByConference->count() > 0)
const conferenceCtx = document.getElementById('conferenceChart').getContext('2d');
const conferenceData = @json($registrationsByConference);

new Chart(conferenceCtx, {
    type: 'doughnut',
    data: {
        labels: conferenceData.map(item => item.name),
        datasets: [{
            data: conferenceData.map(item => item.total),
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF',
                '#FF9F40'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 20,
                    usePointStyle: true
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                        return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                    }
                }
            }
        }
    }
});
@endif
</script>
@endpush
