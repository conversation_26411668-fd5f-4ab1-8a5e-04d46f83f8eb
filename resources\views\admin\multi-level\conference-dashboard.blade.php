@extends(auth()->user()->hasRole('admin') ? 'layouts.admin' : 'layouts.user')

@section('title', 'Conference Dashboard')
@if(auth()->user()->hasRole('admin'))
@section('page-title', 'Conference Dashboard')
@endif

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">
                    <i class="fas fa-building me-2"></i>
                    {{ $conference->name }} Dashboard
                </h2>
                <p class="text-muted">Conference-level administration and statistics</p>
            </div>
            <div>
                @if(auth()->user()->hasRole('admin'))
                <a href="{{ route('admin.conferences.index') }}" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-left me-2"></i>Back to Conferences
                </a>
                @endif
                <a href="{{ route('admin.reports.analytics') }}" class="btn btn-primary">
                    <i class="fas fa-chart-bar me-2"></i>Analytics
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="rounded-circle p-3" style="background: rgba(40, 167, 69, 0.2);">
                            <i class="fas fa-map-marked-alt fa-2x text-success"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">Total Zones</h6>
                        <h3 class="mb-0 text-success">{{ $stats['total_zones'] ?? 0 }}</h3>
                        <small class="text-success">
                            <i class="fas fa-layer-group"></i> Administrative zones
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="rounded-circle p-3" style="background: rgba(255, 193, 7, 0.2);">
                            <i class="fas fa-code-branch fa-2x text-warning"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">Total Branches</h6>
                        <h3 class="mb-0 text-warning">{{ $stats['total_branches'] ?? 0 }}</h3>
                        <small class="text-warning">
                            <i class="fas fa-sitemap"></i> Local branches
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="rounded-circle p-3" style="background: rgba(23, 162, 184, 0.2);">
                            <i class="fas fa-users fa-2x text-info"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">Total Users</h6>
                        <h3 class="mb-0 text-info">{{ $stats['total_users'] ?? 0 }}</h3>
                        <small class="text-info">
                            <i class="fas fa-user-friends"></i> Registered members
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="rounded-circle p-3" style="background: rgba(220, 53, 69, 0.2);">
                            <i class="fas fa-money-bill-wave fa-2x text-danger"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">Total Revenue</h6>
                        <h3 class="mb-0 text-danger">{{ number_format($stats['total_revenue'] ?? 0, 0) }} TZS</h3>
                        <small class="text-danger">
                            <i class="fas fa-chart-line"></i> Conference revenue
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Zones Overview -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-map-marked-alt me-2"></i>
                    Zones in {{ $conference->name }}
                </h5>
            </div>
            <div class="card-body">
                @if($zones->count() > 0)
                    <div class="row">
                        @foreach($zones as $zone)
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card border">
                                <div class="card-body">
                                    <h6 class="card-title">{{ $zone->name }}</h6>
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <h4 class="text-primary mb-1">{{ $zone->branches_count }}</h4>
                                            <small class="text-muted">Branches</small>
                                        </div>
                                        <div class="col-6">
                                            <h4 class="text-success mb-1">{{ $zone->users_count }}</h4>
                                            <small class="text-muted">Users</small>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <a href="{{ route('admin.multi-level.zone.dashboard', $zone) }}" class="btn btn-sm btn-outline-primary w-100">
                                            <i class="fas fa-eye me-2"></i>View Zone
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-map-marked-alt fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No zones found in this conference.</p>
                        @can('manage zones')
                        <a href="{{ route('admin.zones.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Create Zone
                        </a>
                        @endcan
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Recent Registrations -->
@if(isset($recentRegistrations) && $recentRegistrations->count() > 0)
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    Recent Registrations
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Branch</th>
                                <th>Zone</th>
                                <th>Summit</th>
                                <th>Date</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($recentRegistrations as $registration)
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ $registration->user->full_name }}</strong><br>
                                        <small class="text-muted">{{ $registration->user->email }}</small>
                                    </div>
                                </td>
                                <td>{{ $registration->user->branch->name ?? 'N/A' }}</td>
                                <td>{{ $registration->user->branch->zone->name ?? 'N/A' }}</td>
                                <td>{{ $registration->summit->title }}</td>
                                <td>{{ $registration->created_at->format('M j, Y') }}</td>
                                <td>
                                    <span class="badge bg-{{ $registration->payment_complete ? 'success' : 'warning' }}">
                                        {{ $registration->payment_complete ? 'Paid' : 'Pending' }}
                                    </span>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endif
@endsection

@push('styles')
<style>
    .card {
        transition: transform 0.2s, box-shadow 0.2s;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }
</style>
@endpush
