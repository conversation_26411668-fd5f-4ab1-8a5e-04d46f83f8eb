<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Summit;
use App\Models\Branch;
use App\Models\Zone;
use App\Models\Conference;
use App\Models\University;
use App\Models\Minister;
use App\Models\Activity;
use App\Models\SummitRegistration;
use App\Models\Payment;
use Illuminate\Http\Request;

class SearchController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin|treasurer|moderator']);
    }

    /**
     * Universal search endpoint
     */
    public function search(Request $request)
    {
        $query = $request->get('q', '');
        $type = $request->get('type', 'users');
        $limit = $request->get('limit', 10);

        if (strlen($query) < 2) {
            return response()->json([
                'results' => [],
                'total' => 0
            ]);
        }

        $results = [];
        $total = 0;

        switch ($type) {
            case 'users':
                $results = $this->searchUsers($query, $limit);
                break;
            case 'summits':
                $results = $this->searchSummits($query, $limit);
                break;
            case 'branches':
                $results = $this->searchBranches($query, $limit);
                break;
            case 'zones':
                $results = $this->searchZones($query, $limit);
                break;
            case 'conferences':
                $results = $this->searchConferences($query, $limit);
                break;
            case 'universities':
                $results = $this->searchUniversities($query, $limit);
                break;
            case 'ministers':
                $results = $this->searchMinisters($query, $limit);
                break;
            case 'activities':
                $results = $this->searchActivities($query, $limit);
                break;
            case 'registrations':
                $results = $this->searchRegistrations($query, $limit);
                break;
            case 'payments':
                $results = $this->searchPayments($query, $limit);
                break;
        }

        $total = count($results);

        return response()->json([
            'results' => $results,
            'total' => $total,
            'query' => $query
        ]);
    }

    private function searchUsers($query, $limit)
    {
        return User::with(['branch.zone.conference', 'university'])
            ->where(function ($q) use ($query) {
                $q->where('first_name', 'like', "%{$query}%")
                  ->orWhere('last_name', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%")
                  ->orWhere('phone', 'like', "%{$query}%")
                  ->orWhere('student_id', 'like', "%{$query}%")
                  ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%{$query}%"]);
            })
            ->limit($limit)
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'text' => $user->first_name . ' ' . $user->last_name,
                    'subtitle' => $user->email,
                    'meta' => $user->branch ? $user->branch->name : 'No Branch',
                    'url' => route('admin.users.show', $user)
                ];
            });
    }

    private function searchSummits($query, $limit)
    {
        return Summit::where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('theme', 'like', "%{$query}%")
                  ->orWhere('venue', 'like', "%{$query}%")
                  ->orWhere('year', 'like', "%{$query}%");
            })
            ->limit($limit)
            ->get()
            ->map(function ($summit) {
                return [
                    'id' => $summit->id,
                    'text' => $summit->title,
                    'subtitle' => $summit->theme,
                    'meta' => $summit->year . ' - ' . $summit->venue,
                    'url' => route('admin.summits.show', $summit)
                ];
            });
    }

    private function searchBranches($query, $limit)
    {
        return Branch::with(['zone.conference', 'university'])
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('code', 'like', "%{$query}%");
            })
            ->limit($limit)
            ->get()
            ->map(function ($branch) {
                return [
                    'id' => $branch->id,
                    'text' => $branch->name,
                    'subtitle' => $branch->code,
                    'meta' => $branch->zone->name . ' - ' . $branch->zone->conference->name,
                    'url' => route('admin.branches.show', $branch)
                ];
            });
    }

    private function searchZones($query, $limit)
    {
        return Zone::with('conference')
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('code', 'like', "%{$query}%");
            })
            ->limit($limit)
            ->get()
            ->map(function ($zone) {
                return [
                    'id' => $zone->id,
                    'text' => $zone->name,
                    'subtitle' => $zone->code,
                    'meta' => $zone->conference->name,
                    'url' => route('admin.zones.show', $zone)
                ];
            });
    }

    private function searchConferences($query, $limit)
    {
        return Conference::where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('code', 'like', "%{$query}%")
                  ->orWhere('region', 'like', "%{$query}%");
            })
            ->limit($limit)
            ->get()
            ->map(function ($conference) {
                return [
                    'id' => $conference->id,
                    'text' => $conference->name,
                    'subtitle' => $conference->code,
                    'meta' => $conference->region,
                    'url' => route('admin.conferences.show', $conference)
                ];
            });
    }

    private function searchUniversities($query, $limit)
    {
        return University::where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('code', 'like', "%{$query}%")
                  ->orWhere('location', 'like', "%{$query}%");
            })
            ->limit($limit)
            ->get()
            ->map(function ($university) {
                return [
                    'id' => $university->id,
                    'text' => $university->name,
                    'subtitle' => $university->code,
                    'meta' => $university->location,
                    'url' => route('admin.universities.show', $university)
                ];
            });
    }

    private function searchMinisters($query, $limit)
    {
        return Minister::where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('title', 'like', "%{$query}%")
                  ->orWhere('position', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%");
            })
            ->limit($limit)
            ->get()
            ->map(function ($minister) {
                return [
                    'id' => $minister->id,
                    'text' => $minister->name,
                    'subtitle' => $minister->title,
                    'meta' => $minister->position,
                    'url' => route('admin.ministers.show', $minister)
                ];
            });
    }

    private function searchActivities($query, $limit)
    {
        return Activity::where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('location', 'like', "%{$query}%");
            })
            ->limit($limit)
            ->get()
            ->map(function ($activity) {
                return [
                    'id' => $activity->id,
                    'text' => $activity->title,
                    'subtitle' => substr($activity->description, 0, 50) . '...',
                    'meta' => $activity->location . ($activity->date ? ' - ' . $activity->date->format('M d, Y') : ''),
                    'url' => route('admin.activities.show', $activity)
                ];
            });
    }

    private function searchRegistrations($query, $limit)
    {
        return SummitRegistration::with(['user', 'summit'])
            ->where(function ($q) use ($query) {
                $q->where('registration_number', 'like', "%{$query}%")
                  ->orWhereHas('user', function ($userQuery) use ($query) {
                      $userQuery->where('first_name', 'like', "%{$query}%")
                               ->orWhere('last_name', 'like', "%{$query}%")
                               ->orWhere('email', 'like', "%{$query}%")
                               ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%{$query}%"]);
                  });
            })
            ->limit($limit)
            ->get()
            ->map(function ($registration) {
                return [
                    'id' => $registration->id,
                    'text' => $registration->user->first_name . ' ' . $registration->user->last_name,
                    'subtitle' => $registration->registration_number,
                    'meta' => $registration->summit->title . ' (' . $registration->summit->year . ')',
                    'url' => route('admin.registrations.show', $registration)
                ];
            });
    }

    private function searchPayments($query, $limit)
    {
        return Payment::with(['user', 'summitRegistration.summit'])
            ->where(function ($q) use ($query) {
                $q->where('payment_reference', 'like', "%{$query}%")
                  ->orWhereHas('user', function ($userQuery) use ($query) {
                      $userQuery->where('first_name', 'like', "%{$query}%")
                               ->orWhere('last_name', 'like', "%{$query}%")
                               ->orWhere('email', 'like', "%{$query}%")
                               ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%{$query}%"]);
                  });
            })
            ->limit($limit)
            ->get()
            ->map(function ($payment) {
                return [
                    'id' => $payment->id,
                    'text' => $payment->user->first_name . ' ' . $payment->user->last_name,
                    'subtitle' => $payment->payment_reference,
                    'meta' => 'TZS ' . number_format($payment->amount) . ' - ' . ucfirst($payment->status),
                    'url' => route('admin.payments.show', $payment)
                ];
            });
    }
}
