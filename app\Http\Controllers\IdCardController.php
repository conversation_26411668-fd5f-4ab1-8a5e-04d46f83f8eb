<?php

namespace App\Http\Controllers;

use App\Models\IdCard;
use App\Models\Summit;
use App\Services\IdCardService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;

class IdCardController extends Controller
{
    protected $idCardService;

    public function __construct(IdCardService $idCardService)
    {
        $this->middleware('auth')->except(['verify', 'verifyCard']);
        $this->idCardService = $idCardService;
    }

    /**
     * Display user's ID cards
     */
    public function index()
    {
        $user = Auth::user();
        $activeCards = $this->idCardService->getUserActiveCards($user);
        $currentSummit = Summit::current()->first();
        
        $canGenerateForCurrent = false;
        if ($currentSummit) {
            $canGenerateForCurrent = $this->idCardService->canGenerateCard($user, $currentSummit);
        }

        return view('user.id-cards.index', compact('activeCards', 'currentSummit', 'canGenerateForCurrent'));
    }

    /**
     * Show specific ID card
     */
    public function show(IdCard $idCard)
    {
        // Ensure user can only view their own ID cards
        if ($idCard->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to ID card.');
        }

        if (!$idCard->is_active || $idCard->expires_at < now()) {
            abort(404, 'ID card is no longer valid.');
        }

        return view('user.id-cards.show', compact('idCard'));
    }

    /**
     * Generate ID card for current summit
     */
    public function generate(Request $request)
    {
        $user = Auth::user();
        $summit = Summit::current()->first();

        if (!$summit) {
            return back()->with('error', 'No active summit found.');
        }

        if (!$this->idCardService->canGenerateCard($user, $summit)) {
            return back()->with('error', 'You must complete payment for the summit before generating an ID card.');
        }

        try {
            $idCard = $this->idCardService->generateIdCard($user, $summit);
            
            if (!$idCard) {
                return back()->with('error', 'Unable to generate ID card. Please ensure your payment is complete.');
            }

            return redirect()->route('id-cards.show', $idCard)
                ->with('success', 'ID card generated successfully!');
                
        } catch (\Exception $e) {
            return back()->with('error', 'An error occurred while generating your ID card. Please try again.');
        }
    }

    /**
     * Download ID card as PDF
     */
    public function download(IdCard $idCard)
    {
        // Ensure user can only download their own ID cards
        if ($idCard->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to ID card.');
        }

        if (!$idCard->is_active || $idCard->expires_at < now()) {
            abort(404, 'ID card is no longer valid.');
        }

        try {
            // Generate PDF using the service
            $pdfPath = $this->idCardService->generatePdfCard($idCard);

            // Check if file exists
            if (!Storage::disk('public')->exists($pdfPath)) {
                return back()->with('error', 'Unable to generate PDF. Please try again.');
            }

            // Get file content
            $fileContent = Storage::disk('public')->get($pdfPath);
            $fileName = $idCard->card_number . '.pdf';

            // Determine content type
            $contentType = str_ends_with($pdfPath, '.pdf') ? 'application/pdf' : 'text/html';

            return response($fileContent)
                ->header('Content-Type', $contentType)
                ->header('Content-Disposition', 'attachment; filename="' . $fileName . '"');

        } catch (\Exception $e) {
            return back()->with('error', 'Unable to download ID card. Please try again.');
        }
    }

    /**
     * Verify ID card by card number (public endpoint)
     */
    public function verify(Request $request, $card = null)
    {
        $cardNumber = $card ?? $request->input('card_number');
        
        if (!$cardNumber) {
            return view('public.id-card-verify', ['error' => 'Please provide a card number.']);
        }

        $idCard = $this->idCardService->verifyCard($cardNumber);
        
        if (!$idCard) {
            return view('public.id-card-verify', [
                'error' => 'Invalid or expired ID card.',
                'cardNumber' => $cardNumber
            ]);
        }

        return view('public.id-card-verify', [
            'idCard' => $idCard,
            'cardNumber' => $cardNumber,
            'verified' => true
        ]);
    }

    /**
     * API endpoint to verify card (for mobile apps, etc.)
     */
    public function verifyCard(Request $request)
    {
        $request->validate([
            'card_number' => 'required|string'
        ]);

        $idCard = $this->idCardService->verifyCard($request->card_number);
        
        if (!$idCard) {
            return response()->json([
                'valid' => false,
                'message' => 'Invalid or expired ID card.'
            ], 404);
        }

        return response()->json([
            'valid' => true,
            'data' => [
                'card_number' => $idCard->card_number,
                'user_name' => $idCard->user->full_name,
                'summit' => $idCard->summit->title,
                'branch' => $idCard->user->branch->name ?? 'N/A',
                'conference' => $idCard->user->branch->zone->conference->name ?? 'N/A',
                'issued_at' => $idCard->issued_at->format('Y-m-d H:i:s'),
                'expires_at' => $idCard->expires_at->format('Y-m-d H:i:s'),
            ]
        ]);
    }

    /**
     * Preview ID card design
     */
    public function preview(IdCard $idCard)
    {
        // Ensure user can only preview their own ID cards
        if ($idCard->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to ID card.');
        }

        return view('user.id-cards.preview', compact('idCard'));
    }

    /**
     * Regenerate QR code for ID card
     */
    public function regenerateQr(IdCard $idCard)
    {
        // Ensure user can only regenerate their own ID cards
        if ($idCard->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to ID card.');
        }

        try {
            // Delete old QR code file
            if ($idCard->qr_code_path && Storage::disk('public')->exists($idCard->qr_code_path)) {
                Storage::disk('public')->delete($idCard->qr_code_path);
            }

            // Generate new QR code
            $qrData = json_encode([
                'card_number' => $idCard->card_number,
                'user_id' => $idCard->user_id,
                'summit_id' => $idCard->summit_id,
                'name' => $idCard->user->full_name,
                'email' => $idCard->user->email,
                'branch' => $idCard->user->branch->name ?? 'N/A',
                'conference' => $idCard->user->branch->zone->conference->name ?? 'N/A',
                'issued_at' => $idCard->issued_at->toISOString(),
                'verification_url' => route('id-card.verify', ['card' => $idCard->card_number])
            ]);

            // Use the service to generate new QR code
            $qrCodePath = $this->idCardService->generateQrCodeFromData($qrData, $idCard->card_number);

            $idCard->update([
                'qr_code' => $qrData,
                'qr_code_path' => $qrCodePath
            ]);

            return back()->with('success', 'QR code regenerated successfully.');
            
        } catch (\Exception $e) {
            return back()->with('error', 'Unable to regenerate QR code. Please try again.');
        }
    }

    /**
     * Helper method to generate QR code (extracted from service)
     */
    private function generateQrCode(string $data, string $cardNumber): string
    {
        // This is a simplified version - the full implementation is in the service
        $filename = "qr-codes/{$cardNumber}.png";
        
        // For now, create a placeholder file
        Storage::disk('public')->put($filename, 'QR Code placeholder');
        
        return $filename;
    }
}
