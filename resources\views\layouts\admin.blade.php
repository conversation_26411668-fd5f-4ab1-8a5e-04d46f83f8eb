<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="user-authenticated" content="{{ Auth::check() ? 'true' : 'false' }}">
    <meta name="login-url" content="{{ route('login') }}">
    <title>@yield('title', 'Admin Dashboard') - Summit Management</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.css" rel="stylesheet">

    <!-- Vite Assets -->
    @vite(['resources/sass/app.scss', 'resources/js/app.js'])
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-light: #3b82f6;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
            --light-bg: #f1f5f9;
            --card-bg: #ffffff;
            --border-color: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --sidebar-bg: #1e293b;
            --sidebar-text: #cbd5e1;
            --sidebar-active: #3b82f6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: var(--light-bg);
            color: var(--text-primary);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            font-size: 14px;
            line-height: 1.5;
        }

        /* Material Icons */
        .material-icons {
            font-family: 'Material Icons';
            font-weight: normal;
            font-style: normal;
            font-size: 24px;
            line-height: 1;
            letter-spacing: normal;
            text-transform: none;
            display: inline-block;
            white-space: nowrap;
            word-wrap: normal;
            direction: ltr;
            -webkit-font-feature-settings: 'liga';
            -webkit-font-smoothing: antialiased;
        }

        .main-wrapper {
            display: flex;
            height: 100vh;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            height: 100vh;
            background: var(--sidebar-bg);
            color: var(--sidebar-text);
            transition: all 0.3s ease;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .sidebar-header {
            padding: 1.5rem 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            flex-shrink: 0;
        }

        .sidebar-brand {
            color: white;
            font-size: 1.25rem;
            font-weight: 700;
            text-decoration: none;
        }

        .sidebar-nav {
            padding: 1rem 0;
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
        }

        /* Hide scrollbar for webkit browsers */
        .sidebar-nav::-webkit-scrollbar {
            width: 0px;
            background: transparent;
        }

        /* Hide scrollbar for Firefox */
        .sidebar-nav {
            scrollbar-width: none;
        }

        .sidebar .nav-link {
            color: var(--sidebar-text);
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.2s ease;
            border: none;
            border-radius: 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .sidebar .nav-link:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar .nav-link.active {
            color: white;
            background: var(--sidebar-active);
            position: relative;
        }

        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            margin-left: 250px;
            background: var(--light-bg);
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .main-content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100%;
            overflow: hidden;
        }

        /* Top Navigation */
        .navbar {
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 999;
            flex-shrink: 0;
        }

        .navbar-brand {
            font-weight: 600;
        }

        /* Content Area */
        .content-area {
            flex: 1;
            padding: 1.5rem;
            background: var(--light-bg);
            overflow-y: auto;
        }

        /* Fix Bootstrap grid spacing issues */
        .content-area .container-fluid {
            padding-left: 0;
            padding-right: 0;
        }

        .content-area .row {
            margin-left: 0;
            margin-right: 0;
        }

        .content-area .row > [class*="col-"] {
            padding-left: 0.75rem;
            padding-right: 0.75rem;
        }

        /* Ensure cards and content have proper spacing */
        .content-area .card {
            margin-bottom: 1.5rem;
        }

        /* Fix form and button spacing in admin views */
        .content-area .d-flex {
            margin-left: 0;
            margin-right: 0;
        }

        .content-area .btn-group {
            margin-left: 0;
        }

        /* Ensure table responsiveness without extra spacing */
        .content-area .table-responsive {
            margin-left: 0;
            margin-right: 0;
        }

        /* Fix nested row spacing within cards */
        .content-area .card-body .row {
            margin-left: -0.75rem;
            margin-right: -0.75rem;
        }

        .content-area .card-body .row > [class*="col-"] {
            padding-left: 0.75rem;
            padding-right: 0.75rem;
        }

        /* Ensure proper spacing for first-level content */
        .content-area > .container-fluid:first-child {
            margin-top: 0;
        }

        /* Enhanced Payment Management Card Styles - Match Admin Dashboard */
        .payment-stats-card {
            background: white;
            border-radius: 16px;
            padding: 1rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: none;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .payment-stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .payment-stats-card .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
        }

        .payment-stats-card .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin: 0;
            color: #1e293b;
        }

        .payment-stats-card .stat-label {
            color: #64748b;
            font-size: 0.875rem;
            font-weight: 500;
            margin: 0;
        }

        .payment-stats-card .stat-change {
            font-size: 0.75rem;
            font-weight: 600;
            margin-top: 0.5rem;
        }

        /* Payment Card Color Variants */
        .payment-stats-card.blue .stat-icon {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
        }

        .payment-stats-card.green .stat-icon {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        .payment-stats-card.orange .stat-icon {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
        }

        .payment-stats-card.purple .stat-icon {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
        }

        .payment-stats-card.red .stat-icon {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

        /* Modern Stats Cards for Reports and ID Cards */
        .modern-stats-card {
            background: white;
            border-radius: 16px;
            padding: 1rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: none;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .modern-stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .modern-stats-card .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
        }

        .modern-stats-card .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin: 0;
            color: #1e293b;
        }

        .modern-stats-card .stat-label {
            color: #64748b;
            font-size: 0.875rem;
            font-weight: 500;
            margin: 0;
        }

        .modern-stats-card .stat-change {
            font-size: 0.75rem;
            font-weight: 600;
            margin-top: 0.5rem;
        }

        /* Color variants for modern stats cards */
        .modern-stats-card.blue .stat-icon {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
        }

        .modern-stats-card.green .stat-icon {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        .modern-stats-card.orange .stat-icon {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
        }

        .modern-stats-card.purple .stat-icon {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
        }

        .modern-stats-card.red .stat-icon {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

        .modern-stats-card.indigo .stat-icon {
            background: linear-gradient(135deg, #6366f1, #4f46e5);
            color: white;
        }

        /* Enhanced Dashboard Styles */
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 2rem;
            color: white;
            margin-bottom: 2rem;
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .dashboard-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .dashboard-actions .btn {
            border-radius: 10px;
            font-weight: 500;
        }

        /* Chart Controls */
        .chart-controls .btn {
            border-radius: 6px;
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }

        .chart-controls .btn.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        /* Payment Legend */
        .payment-legend {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.5rem 0;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .legend-label {
            flex: 1;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .legend-value {
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Performance Metrics */
        .metric-item {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 12px;
            border-left: 4px solid var(--primary-color);
        }

        .metric-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .metric-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .metric-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .metric-progress {
            height: 6px;
            border-radius: 3px;
            margin-bottom: 0.5rem;
        }

        .metric-change {
            font-size: 0.75rem;
            font-weight: 500;
        }

        /* Activity Feed */
        .activity-feed {
            max-height: 400px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            align-items: flex-start;
            padding: 1rem 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .activity-icon.success {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        .activity-icon.info {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
        }

        .activity-icon.warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }

        .activity-description {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
        }

        .activity-time {
            font-size: 0.75rem;
            color: var(--text-muted);
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 2rem 1rem;
        }

        .empty-icon {
            font-size: 3rem;
            color: var(--text-muted);
            margin-bottom: 1rem;
        }

        .empty-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
        }

        .empty-description {
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        /* Quick Actions Grid */
        .quick-actions-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .quick-action-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1.5rem 1rem;
            background: #f8fafc;
            border-radius: 12px;
            text-decoration: none;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .quick-action-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            text-decoration: none;
            border-color: var(--primary-color);
        }

        .quick-action-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.75rem;
            color: white;
            font-size: 1.25rem;
        }

        .quick-action-icon.bg-purple {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        }

        .quick-action-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-primary);
            text-align: center;
        }

        /* System Health */
        .system-health-grid {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .health-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 10px;
            border-left: 4px solid #e2e8f0;
        }

        .health-indicator {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .health-icon {
            width: 35px;
            height: 35px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.75rem;
            color: white;
            font-size: 0.875rem;
        }

        .health-icon.operational {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .health-icon.warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .health-details {
            flex: 1;
        }

        .health-title {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.875rem;
        }

        .health-value {
            font-size: 0.75rem;
            color: var(--text-secondary);
        }

        .health-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .health-status.operational {
            background: #dcfce7;
            color: #166534;
        }

        .health-status.warning {
            background: #fef3c7;
            color: #92400e;
        }

        .system-status-summary {
            margin-top: 1rem;
            padding: 1rem;
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            border-radius: 10px;
            text-align: center;
        }

        .status-indicator.success {
            color: #059669;
            font-weight: 600;
        }

        .status-indicator.warning {
            color: #d97706;
            font-weight: 600;
        }

        /* Chart Statistics */
        .chart-stats {
            border-bottom: 1px solid #f1f5f9;
            padding-bottom: 1rem;
        }

        .stat-mini {
            padding: 0.75rem;
            background: #f8fafc;
            border-radius: 8px;
            text-align: center;
        }

        .stat-mini-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }

        .stat-mini-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 500;
        }

        /* Enhanced Payment Legend */
        .payment-legend .legend-item {
            background: #f8fafc;
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
        }

        .payment-legend .legend-item:last-child {
            margin-bottom: 0;
        }

        .payment-legend .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .quick-actions-grid {
                grid-template-columns: 1fr;
            }

            .chart-controls {
                margin-top: 1rem;
            }

            .dashboard-header {
                text-align: center;
            }

            .dashboard-actions {
                margin-top: 1rem;
            }
        }

        /* Payment Filter Card */
        .payment-filter-card {
            border: none;
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            background: var(--card-bg);
            margin-bottom: 2rem;
        }

        .payment-filter-card .card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid var(--border-color);
            padding: 1.25rem 1.5rem;
            border-radius: 16px 16px 0 0;
        }

        .payment-filter-card .card-body {
            padding: 1.5rem;
        }

        .payment-filter-card .form-label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .payment-filter-card .form-control,
        .payment-filter-card .form-select {
            border-radius: 8px;
            border: 1px solid var(--border-color);
            padding: 0.75rem 1rem;
            transition: all 0.2s ease;
        }

        .payment-filter-card .form-control:focus,
        .payment-filter-card .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.1);
        }

        /* Payment Table Card */
        .payment-table-card {
            border: none;
            border-radius: 16px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            background: var(--card-bg);
            overflow: hidden;
        }

        .payment-table-card .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            color: white;
            padding: 1.25rem 1.5rem;
            border: none;
        }

        .payment-table-card .card-header h5 {
            margin: 0;
            font-weight: 600;
        }

        .payment-table-card .card-body {
            padding: 0;
        }

        .payment-table-card .table {
            margin: 0;
        }

        .payment-table-card .table th {
            background: #f8fafc;
            border-top: none;
            border-bottom: 2px solid var(--border-color);
            padding: 1rem 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .payment-table-card .table td {
            padding: 1.25rem 1.5rem;
            border-top: 1px solid #f1f5f9;
            vertical-align: middle;
        }

        .payment-table-card .table tbody tr:hover {
            background: #f8fafc;
        }

        /* Payment Details Card */
        .payment-details-card {
            border: none;
            border-radius: 16px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            background: var(--card-bg);
            margin-bottom: 1.5rem;
            overflow: hidden;
        }

        .payment-details-card .card-header {
            padding: 1.5rem;
            border: none;
            font-weight: 600;
        }

        .payment-details-card .card-body {
            padding: 1.5rem;
        }

        .payment-details-card .table-borderless td {
            padding: 0.75rem 0;
            border: none;
        }

        .payment-details-card .table-borderless td:first-child {
            width: 40%;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .payment-details-card .table-borderless td:last-child {
            color: var(--text-primary);
            font-weight: 600;
        }

        /* Payment Status Badges */
        .payment-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Enhanced Button Spacing */
        .payment-actions .btn {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .payment-actions .btn:last-child {
            margin-right: 0;
        }

        /* Card Styles */
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
            background: var(--card-bg);
            margin-bottom: 1rem;
        }

        .card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            background: transparent;
            border-bottom: 1px solid var(--border-color);
            padding: 0.75rem 1rem;
            font-weight: 600;
        }

        .card-body {
            padding: 0.75rem 1rem;
        }
        /* Stat Cards */
        .stat-card {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .stat-card-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%);
        }

        .stat-card-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #fbbf24 100%);
        }

        .stat-card-info {
            background: linear-gradient(135deg, var(--info-color) 0%, #22d3ee 100%);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin: 0;
        }

        .stat-label {
            font-size: 0.875rem;
            opacity: 0.9;
            margin: 0;
        }

        .stat-icon {
            position: absolute;
            top: 1rem;
            right: 1rem;
            font-size: 2rem;
            opacity: 0.3;
        }

        /* Buttons */
        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
            border-radius: 8px;
            font-weight: 500;
            padding: 0.5rem 1rem;
        }

        .btn-primary:hover {
            background: var(--primary-light);
            border-color: var(--primary-light);
        }

        .btn-outline-primary {
            border-color: var(--primary-color);
            color: var(--primary-color);
            border-radius: 8px;
            font-weight: 500;
        }

        /* Tables */
        .table {
            margin-bottom: 0;
        }

        .table th {
            border-top: none;
            border-bottom: 2px solid var(--border-color);
            font-weight: 600;
            color: var(--text-primary);
            padding: 1rem 0.75rem;
        }

        .table td {
            border-top: 1px solid var(--border-color);
            padding: 1rem 0.75rem;
            vertical-align: middle;
        }

        /* Pagination Styling */
        .pagination {
            margin-bottom: 0;
            justify-content: center;
        }

        .pagination .page-link {
            color: var(--primary-color);
            border: 1px solid #dee2e6;
            padding: 0.5rem 0.75rem;
            margin: 0 2px;
            border-radius: 6px;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .pagination .page-link:hover {
            color: white;
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .pagination .page-item.active .page-link {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .pagination .page-item.disabled .page-link {
            color: #6c757d;
            background-color: #f8f9fa;
            border-color: #dee2e6;
            cursor: not-allowed;
        }

        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link {
            border-radius: 6px;
        }

        /* Pagination Info */
        .pagination-info {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-top: 1rem;
            padding: 0.75rem 0;
            border-top: 1px solid #f1f5f9;
            font-size: 0.875rem;
            color: #64748b;
        }

        .pagination-wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1.5rem;
            padding: 1rem 0;
            border-top: 1px solid #f1f5f9;
        }

        .pagination-results {
            font-size: 0.875rem;
            color: #64748b;
        }

        .pagination-nav {
            flex: 1;
            display: flex;
            justify-content: center;
        }

        /* Mobile Overlay */
        .sidebar-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                left: -250px;
                width: 250px;
                z-index: 1001;
            }

            .sidebar.show {
                left: 0;
            }

            .sidebar.show + .sidebar-overlay {
                display: block;
            }

            .main-content {
                margin-left: 0;
            }

            .content-area {
                padding: 1rem;
            }

            .content-area .row > [class*="col-"] {
                padding-left: 0.5rem;
                padding-right: 0.5rem;
            }
        }

        /* Dropdown styles */
        .dropdown-menu {
            display: none;
            position: absolute;
            top: 100%;
            right: 0;
            z-index: 1000;
            min-width: 160px;
            background-color: white;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .dropdown-menu.show {
            display: block;
        }

        .dropdown {
            position: relative;
        }
    </style>
    @stack('styles')
</head>
<body>
    <div class="main-wrapper">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <a href="{{ route('admin.dashboard') }}" class="sidebar-brand">
                    <i class="fas fa-mountain me-2"></i>Summit Admin
                </a>
            </div>

            <div class="sidebar-nav">
                <nav class="nav flex-column">
                        <!-- Dashboard Link - Different for each admin level -->
                        @if(auth()->user()->hasRole('conference_admin') && auth()->user()->admin_conference_id)
                            <a class="nav-link {{ request()->routeIs('admin.multi-level.conference.dashboard') ? 'active' : '' }}"
                               href="{{ route('admin.multi-level.conference.dashboard', auth()->user()->admin_conference_id) }}">
                                <i class="fas fa-tachometer-alt me-2"></i> Conference Dashboard
                            </a>
                        @elseif(auth()->user()->hasRole('zone_admin') && auth()->user()->admin_zone_id)
                            <a class="nav-link {{ request()->routeIs('admin.multi-level.zone.dashboard') ? 'active' : '' }}"
                               href="{{ route('admin.multi-level.zone.dashboard', auth()->user()->admin_zone_id) }}">
                                <i class="fas fa-tachometer-alt me-2"></i> Zone Dashboard
                            </a>
                        @elseif(auth()->user()->hasRole('branch_admin') && auth()->user()->admin_branch_id)
                            <a class="nav-link {{ request()->routeIs('admin.multi-level.branch.dashboard') ? 'active' : '' }}"
                               href="{{ route('admin.multi-level.branch.dashboard', auth()->user()->admin_branch_id) }}">
                                <i class="fas fa-tachometer-alt me-2"></i> Branch Dashboard
                            </a>
                        @else
                            <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}"
                               href="{{ route('admin.dashboard') }}">
                                <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                            </a>
                        @endif

                        <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">

                        <!-- User Management - Available to all admin levels -->
                        <h6 class="text-uppercase text-white-50 mb-2">User Management</h6>
                        <a class="nav-link {{ request()->routeIs('admin.users.*') ? 'active' : '' }}"
                           href="{{ route('admin.users.index') }}">
                            <i class="fas fa-users me-2"></i> Users
                        </a>

                        <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">

                        <!-- Organization - Show based on admin level -->
                        <h6 class="text-uppercase text-white-50 mb-2">Organization</h6>
                        @role('admin')
                            <a class="nav-link {{ request()->routeIs('admin.universities.*') ? 'active' : '' }}"
                               href="{{ route('admin.universities.index') }}">
                                <i class="fas fa-university me-2"></i> Universities
                            </a>
                            <a class="nav-link {{ request()->routeIs('admin.conferences.*') ? 'active' : '' }}"
                               href="{{ route('admin.conferences.index') }}">
                                <i class="fas fa-globe me-2"></i> Conferences
                            </a>
                        @endrole

                        @if(auth()->user()->hasRole(['admin', 'conference_admin']))
                            <a class="nav-link {{ request()->routeIs('admin.zones.*') ? 'active' : '' }}"
                               href="{{ route('admin.zones.index') }}">
                                <i class="fas fa-map me-2"></i> Zones
                            </a>
                        @endif

                        @if(auth()->user()->hasRole(['admin', 'conference_admin', 'zone_admin']))
                            <a class="nav-link {{ request()->routeIs('admin.branches.*') ? 'active' : '' }}"
                               href="{{ route('admin.branches.index') }}">
                                <i class="fas fa-code-branch me-2"></i> Branches
                            </a>
                        @endif
                        
                        <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">

                        <!-- Content Management - Available to admin and conference admin -->
                        @if(auth()->user()->hasRole(['admin', 'conference_admin']))
                        <h6 class="text-uppercase text-white-50 mb-2">Content Management</h6>
                        <a class="nav-link {{ request()->routeIs('admin.ministers.*') ? 'active' : '' }}"
                           href="{{ route('admin.ministers.index') }}">
                            <i class="fas fa-users me-2"></i> Ministers
                        </a>
                        <a class="nav-link {{ request()->routeIs('admin.activities.*') ? 'active' : '' }}"
                           href="{{ route('admin.activities.index') }}">
                            <i class="fas fa-calendar-alt me-2"></i> Activities
                        </a>

                        <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">
                        @endif

                        <!-- Event Management - Available to all admin levels -->
                        <h6 class="text-uppercase text-white-50 mb-2">Event Management</h6>
                        <a class="nav-link {{ request()->routeIs('meal-tracking.*') ? 'active' : '' }}"
                           href="{{ route('meal-tracking.dashboard') }}">
                            <i class="fas fa-utensils me-2"></i> Meal Tracking
                        </a>
                        <a class="nav-link {{ request()->routeIs('meal-tracking.scanner') ? 'active' : '' }}"
                           href="{{ route('meal-tracking.scanner') }}">
                            <i class="fas fa-qrcode me-2"></i> QR Scanner
                        </a>

                        <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">

                        <!-- Summit Management - Available to admin and conference admin -->
                        @if(auth()->user()->hasRole(['admin', 'conference_admin']))
                        <h6 class="text-uppercase text-white-50 mb-2">Summit Management</h6>
                        <a class="nav-link {{ request()->routeIs('admin.summits.*') ? 'active' : '' }}"
                           href="{{ route('admin.summits.index') }}">
                            <i class="fas fa-mountain me-2"></i> Summits
                        </a>
                        @endif

                        <!-- Registrations - Available to all admin levels -->
                        <a class="nav-link {{ request()->routeIs('admin.registrations.*') ? 'active' : '' }}"
                           href="{{ route('admin.registrations.index') }}">
                            <i class="fas fa-clipboard-list me-2"></i> Registrations
                        </a>

                        <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">

                        <!-- Reports & Analytics - Available to all admin levels -->
                        <h6 class="text-uppercase text-white-50 mb-2">Reports & Analytics</h6>
                        <a class="nav-link {{ request()->routeIs('admin.reports.*') ? 'active' : '' }}"
                           href="{{ route('admin.reports.index') }}">
                            <i class="fas fa-chart-bar me-2"></i> Reports
                        </a>

                        <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">

                        <!-- Financial - Available to all admin levels -->
                        <h6 class="text-uppercase text-white-50 mb-2">Financial</h6>
                        <a class="nav-link {{ request()->routeIs('admin.payments.*') ? 'active' : '' }}"
                           href="{{ route('admin.payments.index') }}">
                            <i class="fas fa-credit-card me-2"></i> Payments
                        </a>
                        <a class="nav-link {{ request()->routeIs('admin.id-cards.*') ? 'active' : '' }}"
                           href="{{ route('admin.id-cards.index') }}">
                            <i class="fas fa-id-card me-2"></i> ID Cards
                        </a>

                        @role('admin')
                        <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">

                        <h6 class="text-uppercase text-white-50 mb-2">System Administration</h6>
                        <a class="nav-link {{ request()->routeIs('admin.superadmin.*') ? 'active' : '' }}"
                           href="{{ route('admin.superadmin.roles.index') }}">
                            <i class="fas fa-user-shield me-2"></i> Role Management
                        </a>
                        @endrole

                        <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">

                        <a class="nav-link" href="{{ route('landing.index') }}" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i> View Site
                        </a>
                        <a class="nav-link" href="{{ route('profile.show') }}">
                            <i class="fas fa-user me-2"></i> Profile
                        </a>
                        <a class="nav-link" href="{{ route('logout') }}"
                           onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                            <i class="fas fa-sign-out-alt me-2"></i> Logout
                        </a>
                        <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                            @csrf
                        </form>
                </nav>
            </div>
        </div>

        <!-- Mobile Overlay -->
        <div class="sidebar-overlay" id="sidebarOverlay"></div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="main-content-wrapper">
                <!-- Top Navigation -->
                <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom">
                    <div class="container-fluid">
                        <h5 class="mb-0">@yield('page-title', 'Dashboard')</h5>
                            
                            <div class="navbar-nav ms-auto">
                                <div class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        @if(Auth::user()->profile_image)
                                            <img src="{{ Storage::url(Auth::user()->profile_image) }}"
                                                 alt="Profile" class="rounded-circle me-2"
                                                 style="width: 32px; height: 32px; object-fit: cover;">
                                        @else
                                            <i class="fas fa-user me-2"></i>
                                        @endif
                                        {{ Auth::user()->first_name }}
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><a class="dropdown-item" href="{{ route('profile.show') }}">
                                            <i class="fas fa-user me-2"></i>Profile
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('landing.index') }}" target="_blank">
                                            <i class="fas fa-external-link-alt me-2"></i>View Site
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="{{ route('logout') }}"
                                               onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                                        </a></li>
                                    </ul>
                                </div>
                                <button class="btn btn-outline-primary btn-sm d-md-none ms-2" id="sidebarToggle">
                                    <i class="fas fa-bars"></i>
                                </button>
                            </div>
                    </div>
                </nav>

                <!-- Page Content -->
                <div class="content-area">
                    @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    @endif

                    @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    @endif

                    @if(session('info'))
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <i class="fas fa-info-circle me-2"></i>{{ session('info') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    @endif

                    @yield('content')
                </div>
            </div>
        </div>
    </div>


    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.js"></script>
    <script src="{{ asset('js/enhanced-search.js') }}"></script>
    <script src="{{ asset('js/session-timeout.js') }}"></script>

    <script>
        // Sidebar toggle for mobile
        document.getElementById('sidebarToggle')?.addEventListener('click', function() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            sidebar.classList.toggle('show');

            if (sidebar.classList.contains('show')) {
                overlay.style.display = 'block';
            } else {
                overlay.style.display = 'none';
            }
        });

        // Close sidebar when clicking overlay
        document.getElementById('sidebarOverlay')?.addEventListener('click', function() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.remove('show');
            this.style.display = 'none';
        });

        // Close sidebar on window resize if mobile
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                const sidebar = document.querySelector('.sidebar');
                const overlay = document.getElementById('sidebarOverlay');
                sidebar.classList.remove('show');
                overlay.style.display = 'none';
            }
        });

        // Enhanced dropdown functionality with Bootstrap compatibility
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Bootstrap dropdowns if available
            if (typeof bootstrap !== 'undefined' && bootstrap.Dropdown) {
                // Let Bootstrap handle dropdowns with data-bs-toggle
                console.log('Bootstrap dropdowns initialized');
            } else {
                // Fallback dropdown functionality
                console.log('Using fallback dropdown functionality');

                document.querySelectorAll('.dropdown-toggle').forEach(function(toggle) {
                    toggle.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        // Find the dropdown menu
                        var dropdownMenu = this.nextElementSibling;
                        if (!dropdownMenu) {
                            dropdownMenu = this.parentElement.querySelector('.dropdown-menu');
                        }

                        if (dropdownMenu && dropdownMenu.classList.contains('dropdown-menu')) {
                            // Close other dropdowns
                            document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                                if (menu !== dropdownMenu) {
                                    menu.classList.remove('show');
                                }
                            });

                            // Toggle current dropdown
                            dropdownMenu.classList.toggle('show');
                        }
                    });
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!e.target.closest('.dropdown')) {
                        document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                            menu.classList.remove('show');
                        });
                    }
                });
            }
        });
    </script>

    @stack('scripts')
</body>
</html>
