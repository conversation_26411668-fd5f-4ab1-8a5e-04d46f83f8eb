<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class CreateAdminUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:create {--email=<EMAIL>} {--password=admin123}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create an admin user with known credentials';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->option('email');
        $password = $this->option('password');

        // Check if user already exists
        $existingUser = User::where('email', $email)->first();

        if ($existingUser) {
            $this->info("User with email {$email} already exists. Updating password...");
            $existingUser->update([
                'password' => Hash::make($password)
            ]);
            $existingUser->assignRole('admin');
        } else {
            $this->info("Creating new admin user...");
            $user = User::create([
                'name' => 'System Administrator',
                'first_name' => 'System',
                'last_name' => 'Administrator',
                'email' => $email,
                'password' => Hash::make($password),
                'is_active' => true,
                'email_verified_at' => now(),
            ]);
            $user->assignRole('admin');
        }

        $this->info("Admin user created/updated successfully!");
        $this->info("Email: {$email}");
        $this->info("Password: {$password}");
        $this->warn("Please change the password after first login for security.");
    }
}
