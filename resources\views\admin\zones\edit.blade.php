@extends('layouts.admin')

@section('title', 'Edit Zone')
@section('page-title', 'Edit Zone')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">Edit Zone</h2>
                <p class="text-muted">Update zone information.</p>
            </div>
            <a href="{{ route('admin.zones.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Zones
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-map me-2"></i>Zone Information</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.zones.update', $zone) }}">
                    @csrf
                    @method('PUT')
                    
                    <div class="mb-3">
                        <label for="conference_id" class="form-label">Conference <span class="text-danger">*</span></label>
                        <select name="conference_id" id="conference_id" class="form-select @error('conference_id') is-invalid @enderror" required>
                            <option value="">Select Conference</option>
                            @foreach($conferences as $conference)
                                <option value="{{ $conference->id }}" {{ old('conference_id', $zone->conference_id) == $conference->id ? 'selected' : '' }}>
                                    {{ $conference->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('conference_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="name" class="form-label">Zone Name <span class="text-danger">*</span></label>
                                <input type="text" name="name" id="name" class="form-control @error('name') is-invalid @enderror" 
                                       value="{{ old('name', $zone->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="code" class="form-label">Zone Code <span class="text-danger">*</span></label>
                                <input type="text" name="code" id="code" class="form-control @error('code') is-invalid @enderror" 
                                       value="{{ old('code', $zone->code) }}" placeholder="e.g., DSM" required>
                                @error('code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea name="description" id="description" class="form-control @error('description') is-invalid @enderror" 
                                  rows="4" placeholder="Brief description of the zone...">{{ old('description', $zone->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" name="is_active" id="is_active" class="form-check-input" 
                                   value="1" {{ old('is_active', $zone->is_active) ? 'checked' : '' }}>
                            <label for="is_active" class="form-check-label">
                                Active Zone
                            </label>
                        </div>
                        <small class="text-muted">Inactive zones won't be available for selection.</small>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Zone
                        </button>
                        <a href="{{ route('admin.zones.show', $zone) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Zone Stats</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Total Branches</span>
                    <span class="badge bg-info">{{ $zone->branches()->count() }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Created</span>
                    <span class="text-muted">{{ $zone->created_at->format('M j, Y') }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Last Updated</span>
                    <span class="text-muted">{{ $zone->updated_at->format('M j, Y') }}</span>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
