@extends('layouts.app')

@section('title', 'Make Payment - AzamPay')

@section('content')
    <!-- AzamPay Payment System Indicator -->
    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle me-2"></i>
        <strong>AzamPay Payment System</strong> - You are using the new AzamPay payment gateway with mobile money and bank
        transfer options.
    </div>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-credit-card me-2"></i>
                            Make Payment - {{ $registration->summit->title }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Registration Summary -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Payment Summary</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Registration:</strong> {{ $registration->registration_number }}<br>
                                    <strong>Summit:</strong> {{ $registration->summit->title }}<br>
                                    <strong>Total Amount:</strong> TZS {{ number_format($registration->total_amount, 0) }}
                                </div>
                                <div class="col-md-6">
                                    <strong>Amount Paid:</strong> <span class="text-success">TZS
                                        {{ number_format($registration->paid_amount, 0) }}</span><br>
                                    <strong>Outstanding Balance:</strong> <span class="text-danger">TZS
                                        {{ number_format($registration->balance, 0) }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Method Selection -->
                        <div class="mb-4">
                            <label class="form-label">Choose Payment Method</label>
                            <div class="row">
                                <!-- AzamPay Mobile Money -->
                                <div class="col-lg-6 mb-3">
                                    <input type="radio" class="btn-check" name="payment_method" id="azampay_mobile"
                                        value="azampay_mobile" checked>
                                    <label class="btn btn-outline-primary w-100 h-100" for="azampay_mobile">
                                        <div class="text-center p-3">
                                            <i class="fas fa-mobile-alt fa-2x mb-2"></i>
                                            <h6>Mobile Money (Instant)</h6>
                                            <small class="text-muted">M-Pesa, Tigo Pesa, Airtel Money, Halopesa,
                                                AzamPesa</small>
                                            <div class="badge bg-success mt-2">Recommended</div>
                                        </div>
                                    </label>
                                </div>

                                <!-- AzamPay Bank Transfer -->
                                <div class="col-lg-6 mb-3">
                                    <input type="radio" class="btn-check" name="payment_method" id="azampay_bank"
                                        value="azampay_bank">
                                    <label class="btn btn-outline-success w-100 h-100" for="azampay_bank">
                                        <div class="text-center p-3">
                                            <i class="fas fa-university fa-2x mb-2"></i>
                                            <h6>Bank Transfer (Instant)</h6>
                                            <small class="text-muted">Pay directly from your bank account</small>
                                            <div class="badge bg-info mt-2">Secure</div>
                                        </div>
                                    </label>
                                </div>

                                <!-- Cash Payment -->
                                <div class="col-lg-6 mb-3">
                                    <input type="radio" class="btn-check" name="payment_method" id="cash" value="cash">
                                    <label class="btn btn-outline-warning w-100 h-100" for="cash">
                                        <div class="text-center p-3">
                                            <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                                            <h6>Cash Payment</h6>
                                            <small class="text-muted">Pay at our office</small>
                                            <div class="badge bg-warning mt-2">In-person</div>
                                        </div>
                                    </label>
                                </div>

                                <!-- Direct Mobile Money -->
                                <div class="col-lg-6 mb-3">
                                    <input type="radio" class="btn-check" name="payment_method" id="mobile_money_direct"
                                        value="mobile_money_direct">
                                    <label class="btn btn-outline-info w-100 h-100" for="mobile_money_direct">
                                        <div class="text-center p-3">
                                            <i class="fas fa-phone fa-2x mb-2"></i>
                                            <h6>Direct Mobile Money</h6>
                                            <small class="text-muted">Send directly to our accounts</small>
                                            <div class="badge bg-secondary mt-2">Manual verify</div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- AzamPay Mobile Money Form -->
                        <div id="azampay-mobile-form">
                            <form action="{{ route('payments.mobile', $registration) }}" method="POST"
                                id="mobilePaymentForm">
                                @csrf
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Payment Amount (TZS) <span
                                                class="text-danger">*</span></label>
                                        <input type="number" class="form-control @error('amount') is-invalid @enderror"
                                            name="amount" value="{{ old('amount', $registration->balance) }}" min="1000"
                                            max="{{ $registration->balance }}" required>
                                        @error('amount')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">Select Mobile Money Provider <span
                                                class="text-danger">*</span></label>
                                        <div class="row">
                                            <!-- M-Pesa (Vodacom) -->
                                            <div class="col-md-4 col-6 mb-3">
                                                <input type="radio" class="btn-check" name="provider" id="mpesa" value="Mpesa" required {{ old('provider') == 'Mpesa' ? 'checked' : '' }}>
                                                <label class="btn btn-outline-success w-100 provider-card" for="mpesa">
                                                    <div class="text-center p-2">
                                                        <div class="provider-icon mb-1">
                                                            <img src="{{ asset('images/mno/mpesa.png') }}?v={{ time() }}" alt="M-Pesa" style="height: 25px; width: auto;"
                                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                                            <i class="fas fa-mobile-alt text-success" style="display: none;"></i>
                                                        </div>
                                                        <small class="fw-bold">M-Pesa</small>
                                                    </div>
                                                </label>
                                            </div>

                                            <!-- Tigo Pesa -->
                                            <div class="col-md-4 col-6 mb-3">
                                                <input type="radio" class="btn-check" name="provider" id="tigo" value="Tigo" required {{ old('provider') == 'Tigo' ? 'checked' : '' }}>
                                                <label class="btn btn-outline-primary w-100 provider-card" for="tigo">
                                                    <div class="text-center p-2">
                                                        <div class="provider-icon mb-1">
                                                            <img src="{{ asset('images/mno/tigo.png') }}?v={{ time() }}" alt="Tigo Pesa" style="height: 25px; width: auto;"
                                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                                            <i class="fas fa-mobile-alt text-primary" style="display: none;"></i>
                                                        </div>
                                                        <small class="fw-bold">Tigo Pesa</small>
                                                    </div>
                                                </label>
                                            </div>

                                            <!-- Airtel Money -->
                                            <div class="col-md-4 col-6 mb-3">
                                                <input type="radio" class="btn-check" name="provider" id="airtel" value="Airtel" required {{ old('provider') == 'Airtel' ? 'checked' : '' }}>
                                                <label class="btn btn-outline-danger w-100 provider-card" for="airtel">
                                                    <div class="text-center p-2">
                                                        <div class="provider-icon mb-1">
                                                            <img src="{{ asset('images/mno/airtel.png') }}?v={{ time() }}" alt="Airtel Money" style="height: 25px; width: auto;"
                                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                                            <i class="fas fa-mobile-alt text-danger" style="display: none;"></i>
                                                        </div>
                                                        <small class="fw-bold">Airtel Money</small>
                                                    </div>
                                                </label>
                                            </div>

                                            <!-- Halopesa -->
                                            <div class="col-md-4 col-6 mb-3">
                                                <input type="radio" class="btn-check" name="provider" id="halopesa" value="Halopesa" required {{ old('provider') == 'Halopesa' ? 'checked' : '' }}>
                                                <label class="btn btn-outline-warning w-100 provider-card" for="halopesa">
                                                    <div class="text-center p-2">
                                                        <div class="provider-icon mb-1">
                                                            <img src="{{ asset('images/mno/halopesa.png') }}?v={{ time() }}" alt="HaloPesa" style="height: 25px; width: auto;"
                                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                                            <i class="fas fa-mobile-alt text-warning" style="display: none;"></i>
                                                        </div>
                                                        <small class="fw-bold">HaloPesa</small>
                                                    </div>
                                                </label>
                                            </div>

                                            <!-- Azam Pesa -->
                                            <div class="col-md-4 col-6 mb-3">
                                                <input type="radio" class="btn-check" name="provider" id="azampesa" value="Azampesa" required {{ old('provider') == 'Azampesa' ? 'checked' : '' }}>
                                                <label class="btn btn-outline-info w-100 provider-card" for="azampesa">
                                                    <div class="text-center p-2">
                                                        <div class="provider-icon mb-1">
                                                            <img src="{{ asset('images/mno/azampesa.png') }}?v={{ time() }}" alt="Azam Pesa" style="height: 25px; width: auto;"
                                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                                            <i class="fas fa-mobile-alt text-info" style="display: none;"></i>
                                                        </div>
                                                        <small class="fw-bold">Azam Pesa</small>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>

                                        @error('provider')
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">Mobile Phone Number <span
                                                class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text">+255</span>
                                            <input type="text"
                                                class="form-control @error('phone_number') is-invalid @enderror"
                                                name="phone_number" value="{{ old('phone_number') }}"
                                                placeholder="754123456" maxlength="9" required>
                                        </div>
                                        @error('phone_number')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>Mobile Money Payment Instructions</h6>
                                    <ol class="mb-0">
                                        <li>Select your mobile money provider above</li>
                                        <li>Enter your mobile phone number</li>
                                        <li>Click "Pay with Mobile Money" below</li>
                                        <li>You will receive a USSD prompt on your phone</li>
                                        <li>Enter your mobile money PIN to complete the payment</li>
                                        <li>You will receive a confirmation SMS upon successful payment</li>
                                    </ol>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg" id="mobileSubmitBtn">
                                        <i class="fas fa-mobile-alt me-2"></i>
                                        Pay with Mobile Money
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- AzamPay Bank Transfer Form -->
                        <div id="azampay-bank-form" style="display: none;">
                            <form action="{{ route('payments.bank', $registration) }}" method="POST" id="bankPaymentForm">
                                @csrf
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Payment Amount (TZS) <span
                                                class="text-danger">*</span></label>
                                        <input type="number" class="form-control" name="amount"
                                            value="{{ old('amount', $registration->balance) }}" min="1000"
                                            max="{{ $registration->balance }}" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Select Bank <span class="text-danger">*</span></label>
                                        <select class="form-select" name="bank_code" required>
                                            <option value="">Select Bank</option>
                                            @foreach($banks as $code => $name)
                                                <option value="{{ $code }}">{{ $name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>

                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>Bank Transfer Instructions</h6>
                                    <ol class="mb-0">
                                        <li>Select your bank above</li>
                                        <li>Click "Pay with Bank Transfer" below</li>
                                        <li>You will be redirected to your bank's secure payment page</li>
                                        <li>Login with your online banking credentials</li>
                                        <li>Authorize the payment</li>
                                        <li>You will be redirected back with payment confirmation</li>
                                    </ol>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-success btn-lg" id="bankSubmitBtn">
                                        <i class="fas fa-university me-2"></i>
                                        Pay with Bank Transfer
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Manual Payment Forms -->
                        <div id="manual-payment-forms" style="display: none;">
                            <form action="{{ route('payments.manual', $registration) }}" method="POST"
                                id="manualPaymentForm">
                                @csrf
                                <input type="hidden" name="payment_method" id="manual_payment_method" value="">

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Payment Amount (TZS) <span
                                                class="text-danger">*</span></label>
                                        <input type="number" class="form-control" name="amount"
                                            value="{{ old('amount', $registration->balance) }}" min="1000"
                                            max="{{ $registration->balance }}" required>
                                    </div>
                                </div>

                                <!-- Cash Payment Fields -->
                                <div id="cash-fields" style="display: none;">
                                    <div class="alert alert-warning">
                                        <h6><i class="fas fa-info-circle me-2"></i>Cash Payment Instructions</h6>
                                        <p class="mb-2">Visit our office to make cash payment:</p>
                                        <ul class="mb-2">
                                            <li><strong>Office:</strong> TUCASA STU Headquarters</li>
                                            <li><strong>Address:</strong> [Your Office Address]</li>
                                            <li><strong>Hours:</strong> Monday - Friday, 8:00 AM - 5:00 PM</li>
                                            <li><strong>Contact:</strong> +255 123 456 789</li>
                                        </ul>
                                        <small class="text-muted">Fill the form below after making payment.</small>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Received By <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" name="received_by"
                                                placeholder="Name of person who received payment">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Receipt Number <span
                                                    class="text-danger">*</span></label>
                                            <input type="text" class="form-control" name="receipt_number"
                                                placeholder="Official receipt number">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Payment Location</label>
                                            <input type="text" class="form-control" name="payment_location"
                                                placeholder="Office location" value="TUCASA STU Headquarters">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Payment Date <span
                                                    class="text-danger">*</span></label>
                                            <input type="date" class="form-control" name="payment_date"
                                                value="{{ date('Y-m-d') }}">
                                        </div>
                                    </div>
                                </div>

                                <!-- Direct Mobile Money Fields -->
                                <div id="mobile-direct-fields" style="display: none;">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-info-circle me-2"></i>Direct Mobile Money Instructions</h6>
                                        <p class="mb-2">Send money directly to our mobile money accounts:</p>
                                        <ul class="mb-2">
                                            <li><strong>M-Pesa (Vodacom):</strong> 0754-123456 (TUCASA STU)</li>
                                            <li><strong>Tigo Pesa:</strong> 0654-123456 (TUCASA STU)</li>
                                            <li><strong>Airtel Money:</strong> 0684-123456 (TUCASA STU)</li>
                                            <li><strong>Reference:</strong> {{ $registration->registration_number }}</li>
                                        </ul>
                                        <small class="text-muted">After sending, enter transaction details below.</small>
                                    </div>

                                    <div class="row">
                                        <div class="col-12 mb-3">
                                            <label class="form-label">Select Mobile Money Provider <span
                                                    class="text-danger">*</span></label>
                                            <div class="row">
                                                <!-- M-Pesa (Vodacom) -->
                                                <div class="col-md-4 col-6 mb-3">
                                                    <input type="radio" class="btn-check" name="provider_direct" id="mpesa_direct" value="Mpesa" required>
                                                    <label class="btn btn-outline-success w-100 provider-card" for="mpesa_direct">
                                                        <div class="text-center p-2">
                                                            <div class="provider-icon mb-1">
                                                                <img src="{{ asset('images/mno/mpesa.png') }}?v={{ time() }}" alt="M-Pesa" style="height: 25px; width: auto;"
                                                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                                                <i class="fas fa-mobile-alt text-success" style="display: none;"></i>
                                                            </div>
                                                            <small class="fw-bold">M-Pesa</small>
                                                        </div>
                                                    </label>
                                                </div>

                                                <!-- Tigo Pesa -->
                                                <div class="col-md-4 col-6 mb-3">
                                                    <input type="radio" class="btn-check" name="provider_direct" id="tigo_direct" value="Tigo" required>
                                                    <label class="btn btn-outline-primary w-100 provider-card" for="tigo_direct">
                                                        <div class="text-center p-2">
                                                            <div class="provider-icon mb-1">
                                                                <img src="{{ asset('images/mno/tigo.png') }}?v={{ time() }}" alt="Tigo Pesa" style="height: 25px; width: auto;"
                                                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                                                <i class="fas fa-mobile-alt text-primary" style="display: none;"></i>
                                                            </div>
                                                            <small class="fw-bold">Tigo Pesa</small>
                                                        </div>
                                                    </label>
                                                </div>

                                                <!-- Airtel Money -->
                                                <div class="col-md-4 col-6 mb-3">
                                                    <input type="radio" class="btn-check" name="provider_direct" id="airtel_direct" value="Airtel" required>
                                                    <label class="btn btn-outline-danger w-100 provider-card" for="airtel_direct">
                                                        <div class="text-center p-2">
                                                            <div class="provider-icon mb-1">
                                                                <img src="{{ asset('images/mno/airtel.png') }}?v={{ time() }}" alt="Airtel Money" style="height: 25px; width: auto;"
                                                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                                                <i class="fas fa-mobile-alt text-danger" style="display: none;"></i>
                                                            </div>
                                                            <small class="fw-bold">Airtel Money</small>
                                                        </div>
                                                    </label>
                                                </div>

                                                <!-- Halopesa -->
                                                <div class="col-md-4 col-6 mb-3">
                                                    <input type="radio" class="btn-check" name="provider_direct" id="halopesa_direct" value="Halopesa" required>
                                                    <label class="btn btn-outline-warning w-100 provider-card" for="halopesa_direct">
                                                        <div class="text-center p-2">
                                                            <div class="provider-icon mb-1">
                                                                <img src="{{ asset('images/mno/halopesa.png') }}?v={{ time() }}" alt="HaloPesa" style="height: 25px; width: auto;"
                                                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                                                <i class="fas fa-mobile-alt text-warning" style="display: none;"></i>
                                                            </div>
                                                            <small class="fw-bold">HaloPesa</small>
                                                        </div>
                                                    </label>
                                                </div>

                                                <!-- Azam Pesa -->
                                                <div class="col-md-4 col-6 mb-3">
                                                    <input type="radio" class="btn-check" name="provider_direct" id="azampesa_direct" value="Azampesa" required>
                                                    <label class="btn btn-outline-info w-100 provider-card" for="azampesa_direct">
                                                        <div class="text-center p-2">
                                                            <div class="provider-icon mb-1">
                                                                <img src="{{ asset('images/mno/azampesa.png') }}?v={{ time() }}" alt="Azam Pesa" style="height: 25px; width: auto;"
                                                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                                                <i class="fas fa-mobile-alt text-info" style="display: none;"></i>
                                                            </div>
                                                            <small class="fw-bold">Azam Pesa</small>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12 mb-3">
                                            <label class="form-label">Your Phone Number <span
                                                    class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text">+255</span>
                                                <input type="text" class="form-control" name="phone_number"
                                                    placeholder="754123456" maxlength="9" required>
                                            </div>
                                            <div class="form-text">Enter your mobile money number (without +255)</div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Transaction ID <span
                                                    class="text-danger">*</span></label>
                                            <input type="text" class="form-control" name="transaction_id"
                                                placeholder="Mobile money transaction ID">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Confirmation Code</label>
                                            <input type="text" class="form-control" name="confirmation_code"
                                                placeholder="SMS confirmation code">
                                        </div>
                                    </div>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-warning btn-lg" id="manualSubmitBtn">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        <span id="manual-submit-text">Submit Payment Details</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Security Notice -->
                <div class="card mt-3">
                    <div class="card-body">
                        <h6><i class="fas fa-shield-alt text-success me-2"></i>Secure Payment</h6>
                        <p class="small text-muted mb-0">
                            Your payment is processed securely through AzamPay Payment Gateway.
                            We do not store your mobile money PIN, bank credentials, or any sensitive payment information.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Handle payment method selection
            document.querySelectorAll('input[name="payment_method"]').forEach(radio => {
                radio.addEventListener('change', function () {
                    const method = this.value;

                    // Hide all forms
                    document.getElementById('azampay-mobile-form').style.display = 'none';
                    document.getElementById('azampay-bank-form').style.display = 'none';
                    document.getElementById('manual-payment-forms').style.display = 'none';
                    document.getElementById('cash-fields').style.display = 'none';
                    document.getElementById('mobile-direct-fields').style.display = 'none';

                    // Show relevant form
                    switch (method) {
                        case 'azampay_mobile':
                            document.getElementById('azampay-mobile-form').style.display = 'block';
                            break;
                        case 'azampay_bank':
                            document.getElementById('azampay-bank-form').style.display = 'block';
                            break;
                        case 'cash':
                            document.getElementById('manual-payment-forms').style.display = 'block';
                            document.getElementById('cash-fields').style.display = 'block';
                            document.getElementById('manual_payment_method').value = 'cash';
                            document.getElementById('manual-submit-text').textContent = 'Submit Cash Payment Details';
                            break;
                        case 'mobile_money_direct':
                            document.getElementById('manual-payment-forms').style.display = 'block';
                            document.getElementById('mobile-direct-fields').style.display = 'block';
                            document.getElementById('manual_payment_method').value = 'mobile_money_direct';
                            document.getElementById('manual-submit-text').textContent = 'Submit Mobile Money Details';
                            break;
                    }
                });
            });

            // Initialize with default selection
            const defaultMethod = document.querySelector('input[name="payment_method"]:checked');
            if (defaultMethod) {
                defaultMethod.dispatchEvent(new Event('change'));
            }

            // Phone number formatting
            document.querySelectorAll('input[name="phone_number"], input[name="phone_number_direct"]').forEach(input => {
                input.addEventListener('input', function () {
                    this.value = this.value.replace(/\D/g, '');
                    if (this.value.length > 9) {
                        this.value = this.value.slice(0, 9);
                    }
                });
            });

            // Form submission handlers
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function () {
                    const submitBtn = this.querySelector('button[type="submit"]');
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                });
            });
        });
    </script>
@endsection

@push('styles')
    <style>
        .payment-method-card {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .payment-method-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .payment-method-card.selected {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }

        .provider-card {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid #dee2e6 !important;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .provider-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .btn-check:checked + .provider-card {
            border-color: var(--bs-primary) !important;
            background-color: rgba(var(--bs-primary-rgb), 0.1) !important;
            transform: scale(1.02);
        }

        .provider-icon img {
            max-height: 25px;
            max-width: 50px;
            object-fit: contain;
        }

        .amount-suggestion {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .amount-suggestion:hover {
            background-color: #0d6efd !important;
            color: white !important;
        }
    </style>
@endpush