@extends(auth()->user()->hasRole('admin') ? 'layouts.admin' : 'layouts.user')

@section('title', 'Branch Dashboard')
@if(auth()->user()->hasRole('admin'))
@section('page-title', 'Branch Dashboard')
@endif

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">
                    <i class="fas fa-code-branch me-2"></i>
                    {{ $branch->name }} Dashboard
                </h2>
                <p class="text-muted">Branch-level administration and statistics</p>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.multi-level.conference.dashboard', $branch->zone->conference) }}">
                                {{ $branch->zone->conference->name }}
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.multi-level.zone.dashboard', $branch->zone) }}">
                                {{ $branch->zone->name }}
                            </a>
                        </li>
                        <li class="breadcrumb-item active">{{ $branch->name }}</li>
                    </ol>
                </nav>
            </div>
            <div>
                @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('conference_admin') || auth()->user()->hasRole('zone_admin'))
                <a href="{{ route('admin.multi-level.zone.dashboard', $branch->zone) }}" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-left me-2"></i>Back to Zone
                </a>
                @endif
                <a href="{{ route('admin.reports.analytics') }}" class="btn btn-primary">
                    <i class="fas fa-chart-bar me-2"></i>Analytics
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="rounded-circle p-3" style="background: rgba(23, 162, 184, 0.2);">
                            <i class="fas fa-users fa-2x text-info"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">Total Users</h6>
                        <h3 class="mb-0 text-info">{{ $stats['total_users'] ?? 0 }}</h3>
                        <small class="text-info">
                            <i class="fas fa-user-friends"></i> Branch members
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="rounded-circle p-3" style="background: rgba(40, 167, 69, 0.2);">
                            <i class="fas fa-user-check fa-2x text-success"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">Registrations</h6>
                        <h3 class="mb-0 text-success">{{ $stats['total_registrations'] ?? 0 }}</h3>
                        <small class="text-success">
                            <i class="fas fa-clipboard-check"></i> Summit registrations
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="rounded-circle p-3" style="background: rgba(220, 53, 69, 0.2);">
                            <i class="fas fa-money-bill-wave fa-2x text-danger"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">Total Payments</h6>
                        <h3 class="mb-0 text-danger">TZS {{ number_format($stats['total_payments'] ?? 0) }}</h3>
                        <small class="text-danger">
                            <i class="fas fa-coins"></i> Completed payments
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="rounded-circle p-3" style="background: rgba(255, 193, 7, 0.2);">
                            <i class="fas fa-clock fa-2x text-warning"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">Pending Payments</h6>
                        <h3 class="mb-0 text-warning">{{ $stats['pending_payments'] ?? 0 }}</h3>
                        <small class="text-warning">
                            <i class="fas fa-hourglass-half"></i> Awaiting payment
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Branch Users -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    Branch Members
                </h5>
            </div>
            <div class="card-body">
                @if($users->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Contact</th>
                                    <th>University</th>
                                    <th>Registrations</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($users as $user)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($user->profile_image)
                                                <img src="{{ Storage::url($user->profile_image) }}" 
                                                     alt="Profile" class="rounded-circle me-3" 
                                                     style="width: 40px; height: 40px; object-fit: cover;">
                                            @else
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" 
                                                     style="width: 40px; height: 40px;">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                            @endif
                                            <div>
                                                <div class="fw-bold">{{ $user->full_name }}</div>
                                                @if($user->student_id)
                                                <small class="text-muted">ID: {{ $user->student_id }}</small>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>{{ $user->email }}</div>
                                        @if($user->phone)
                                        <small class="text-muted">{{ $user->phone }}</small>
                                        @endif
                                    </td>
                                    <td>{{ $user->university->name ?? 'N/A' }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ $user->summitRegistrations->count() }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $user->is_active ? 'success' : 'danger' }}">
                                            {{ $user->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('admin.users.show', $user) }}"
                                               class="btn btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.users.edit', $user) }}"
                                               class="btn btn-outline-info" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-4 p-3 bg-light rounded">
                        <div class="text-muted">
                            <i class="fas fa-info-circle me-2"></i>
                            Showing <strong>{{ $users->firstItem() ?? 0 }}</strong> to <strong>{{ $users->lastItem() ?? 0 }}</strong>
                            of <strong>{{ $users->total() }}</strong> results
                        </div>
                        <div>
                            {{ $users->links('pagination::bootstrap-4') }}
                        </div>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No users found in this branch.</p>
                        @can('create users')
                        <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add User
                        </a>
                        @endcan
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Recent Registrations -->
@if(isset($recentRegistrations) && $recentRegistrations->count() > 0)
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    Recent Registrations
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Summit</th>
                                <th>Date</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($recentRegistrations as $registration)
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ $registration->user->full_name }}</strong><br>
                                        <small class="text-muted">{{ $registration->user->email }}</small>
                                    </div>
                                </td>
                                <td>{{ $registration->summit->title }}</td>
                                <td>{{ $registration->created_at->format('M j, Y') }}</td>
                                <td>
                                    <span class="badge bg-{{ $registration->payment_complete ? 'success' : 'warning' }}">
                                        {{ $registration->payment_complete ? 'Paid' : 'Pending' }}
                                    </span>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endif
@endsection

@push('styles')
<style>
    .card {
        transition: transform 0.2s, box-shadow 0.2s;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }
</style>
@endpush
