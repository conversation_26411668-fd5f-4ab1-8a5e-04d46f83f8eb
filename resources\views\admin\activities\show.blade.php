@extends('layouts.admin')

@section('title', 'Activity Details')
@section('page-title', 'Activity Details')
@section('page-subtitle', 'View activity information')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>{{ $activity->title }}
                    </h5>
                    <div>
                        <a href="{{ route('admin.activities.edit', $activity) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit me-2"></i>Edit
                        </a>
                        <form action="{{ route('admin.activities.destroy', $activity) }}" method="POST" class="d-inline"
                              onsubmit="return confirm('Are you sure you want to delete this activity?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-sm">
                                <i class="fas fa-trash me-2"></i>Delete
                            </button>
                        </form>
                    </div>
                </div>
                <div class="card-body">
                    @if($activity->image)
                        <div class="text-center mb-4">
                            <img src="{{ Storage::url($activity->image) }}" 
                                 alt="{{ $activity->title }}" 
                                 class="rounded" 
                                 style="max-width: 100%; height: 300px; object-fit: cover;">
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-8">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="25%">Title:</th>
                                    <td>{{ $activity->title }}</td>
                                </tr>
                                @if($activity->date)
                                <tr>
                                    <th>Date:</th>
                                    <td>{{ $activity->date->format('l, F j, Y') }}</td>
                                </tr>
                                @endif
                                @if($activity->time)
                                <tr>
                                    <th>Time:</th>
                                    <td>{{ $activity->time->format('g:i A') }}</td>
                                </tr>
                                @endif
                                @if($activity->location)
                                <tr>
                                    <th>Location:</th>
                                    <td>{{ $activity->location }}</td>
                                </tr>
                                @endif
                                <tr>
                                    <th>Display Order:</th>
                                    <td>{{ $activity->order }}</td>
                                </tr>
                                <tr>
                                    <th>Status:</th>
                                    <td>
                                        <span class="badge {{ $activity->is_active ? 'bg-success' : 'bg-secondary' }}">
                                            {{ $activity->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                        @if($activity->is_featured)
                                            <span class="badge bg-warning ms-1">Featured</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Created:</th>
                                    <td>{{ $activity->created_at->format('M d, Y \a\t g:i A') }}</td>
                                </tr>
                                <tr>
                                    <th>Last Updated:</th>
                                    <td>{{ $activity->updated_at->format('M d, Y \a\t g:i A') }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-4">
                            <div class="bg-light p-3 rounded">
                                <h6 class="mb-2">Quick Info</h6>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-eye me-2 {{ $activity->is_active ? 'text-success' : 'text-muted' }}"></i>
                                    <small>{{ $activity->is_active ? 'Visible on landing page' : 'Hidden from landing page' }}</small>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-star me-2 {{ $activity->is_featured ? 'text-warning' : 'text-muted' }}"></i>
                                    <small>{{ $activity->is_featured ? 'Featured activity' : 'Regular activity' }}</small>
                                </div>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-image me-2 {{ $activity->image ? 'text-success' : 'text-muted' }}"></i>
                                    <small>{{ $activity->image ? 'Has image' : 'No image' }}</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <h6>Description</h6>
                            <div class="bg-light p-3 rounded">
                                {!! nl2br(e($activity->description)) !!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-cog me-2"></i>Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.activities.edit', $activity) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>Edit Activity
                        </a>
                        <a href="{{ route('admin.activities.index') }}" class="btn btn-secondary">
                            <i class="fas fa-list me-2"></i>All Activities
                        </a>
                        <a href="{{ route('admin.activities.create') }}" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>Add New Activity
                        </a>
                        <hr>
                        <form action="{{ route('admin.activities.destroy', $activity) }}" method="POST"
                              onsubmit="return confirm('Are you sure you want to delete this activity? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="fas fa-trash me-2"></i>Delete Activity
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            @if($activity->date || $activity->time)
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-clock me-2"></i>Schedule
                    </h6>
                </div>
                <div class="card-body">
                    @if($activity->date)
                        <div class="mb-2">
                            <strong>Date:</strong><br>
                            <span class="text-muted">{{ $activity->date->format('l, F j, Y') }}</span>
                        </div>
                    @endif
                    @if($activity->time)
                        <div class="mb-2">
                            <strong>Time:</strong><br>
                            <span class="text-muted">{{ $activity->time->format('g:i A') }}</span>
                        </div>
                    @endif
                    @if($activity->location)
                        <div>
                            <strong>Location:</strong><br>
                            <span class="text-muted">{{ $activity->location }}</span>
                        </div>
                    @endif
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
