@props(['paginator', 'showInfo' => true])

@if($paginator->hasPages())
<div class="pagination-wrapper">
    @if($showInfo)
    <div class="pagination-results">
        <span class="fw-medium">
            Showing {{ $paginator->firstItem() ?? 0 }} to {{ $paginator->lastItem() ?? 0 }} 
            of {{ $paginator->total() }} results
        </span>
    </div>
    @endif
    
    <div class="pagination-nav">
        <nav aria-label="Page navigation">
            <ul class="pagination">
                {{-- Previous Page Link --}}
                @if ($paginator->onFirstPage())
                    <li class="page-item disabled">
                        <span class="page-link">
                            <i class="fas fa-chevron-left"></i>
                            <span class="d-none d-sm-inline ms-1">Previous</span>
                        </span>
                    </li>
                @else
                    <li class="page-item">
                        <a class="page-link" href="{{ $paginator->previousPageUrl() }}" rel="prev">
                            <i class="fas fa-chevron-left"></i>
                            <span class="d-none d-sm-inline ms-1">Previous</span>
                        </a>
                    </li>
                @endif

                {{-- Pagination Elements --}}
                @foreach ($paginator->getUrlRange(1, $paginator->lastPage()) as $page => $url)
                    @if ($page == $paginator->currentPage())
                        <li class="page-item active">
                            <span class="page-link">{{ $page }}</span>
                        </li>
                    @else
                        <li class="page-item">
                            <a class="page-link" href="{{ $url }}">{{ $page }}</a>
                        </li>
                    @endif
                @endforeach

                {{-- Next Page Link --}}
                @if ($paginator->hasMorePages())
                    <li class="page-item">
                        <a class="page-link" href="{{ $paginator->nextPageUrl() }}" rel="next">
                            <span class="d-none d-sm-inline me-1">Next</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                @else
                    <li class="page-item disabled">
                        <span class="page-link">
                            <span class="d-none d-sm-inline me-1">Next</span>
                            <i class="fas fa-chevron-right"></i>
                        </span>
                    </li>
                @endif
            </ul>
        </nav>
    </div>
    
    @if($showInfo)
    <div class="pagination-results d-none d-md-block">
        <small class="text-muted">
            Page {{ $paginator->currentPage() }} of {{ $paginator->lastPage() }}
        </small>
    </div>
    @endif
</div>
@endif

<style>
/* Enhanced pagination for mobile */
@media (max-width: 576px) {
    .pagination .page-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .pagination-wrapper {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .pagination-results {
        order: 2;
    }
    
    .pagination-nav {
        order: 1;
    }
}

/* Compact pagination for small screens */
@media (max-width: 768px) {
    .pagination .page-item:not(.active):not(:first-child):not(:last-child) {
        display: none;
    }
    
    .pagination .page-item.active ~ .page-item:nth-child(-n+2),
    .pagination .page-item.active ~ .page-item:nth-last-child(-n+2) {
        display: inline-block;
    }
}
</style>
