@extends('layouts.user')

@section('title', 'My Profile')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0">My Profile</h2>
            <a href="{{ route('profile.edit') }}" class="btn btn-gradient">
                <i class="fas fa-edit me-2"></i>Edit Profile
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Profile Information -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-body text-center">
                @if($user->profile_image)
                    <img src="{{ Storage::url($user->profile_image) }}"
                         alt="Profile" class="rounded-circle mb-3"
                         style="width: 150px; height: 150px; object-fit: cover; border: 3px solid #007bff;"
                         onerror="console.error('Failed to load profile image:', this.src); this.style.border='3px solid red';">
                @else
                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3"
                         style="width: 150px; height: 150px;">
                        <i class="fas fa-user fa-4x text-white"></i>
                    </div>
                    <div class="alert alert-warning mt-2">
                        <small>No profile image set</small>
                    </div>
                @endif
                
                <h4 class="mb-1">{{ $user->full_name }}</h4>
                <p class="text-muted mb-3">{{ $user->email }}</p>
                
                @if($user->branch)
                <div class="mb-3">
                    <h6 class="text-muted text-uppercase mb-2">Organization</h6>
                    <p class="mb-1"><strong>Branch:</strong> {{ $user->branch->name }}</p>
                    <p class="mb-1"><strong>Zone:</strong> {{ $user->branch->zone->name }}</p>
                    <p class="mb-1"><strong>Conference:</strong> {{ $user->branch->zone->conference->name }}</p>
                </div>
                @endif
                
                @if($user->university)
                <div class="mb-3">
                    <h6 class="text-muted text-uppercase mb-2">Education</h6>
                    <p class="mb-1"><strong>University:</strong> {{ $user->university->name }}</p>
                    @if($user->course)
                    <p class="mb-1"><strong>Course:</strong> {{ $user->course }}</p>
                    @endif
                    @if($user->year_of_study)
                    <p class="mb-1"><strong>Year:</strong> {{ $user->year_of_study }}</p>
                    @endif
                </div>
                @endif
                
                <div class="d-grid gap-2">
                    <a href="{{ route('profile.edit') }}" class="btn btn-gradient">
                        <i class="fas fa-edit me-2"></i>Edit Profile
                    </a>
                    @if($user->summitRegistrations->where('payment_complete', true)->count() > 0)
                    <a href="{{ route('id-card.show') }}" class="btn btn-outline-primary">
                        <i class="fas fa-id-card me-2"></i>View ID Card
                    </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
    
    <!-- Detailed Information -->
    <div class="col-lg-8">
        <div class="row">
            <!-- Personal Information -->
            <div class="col-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-user me-2"></i>Personal Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">First Name</label>
                                    <p class="mb-0">{{ $user->first_name ?: 'Not provided' }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Last Name</label>
                                    <p class="mb-0">{{ $user->last_name ?: 'Not provided' }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Email</label>
                                    <p class="mb-0">{{ $user->email }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Phone</label>
                                    <p class="mb-0">{{ $user->phone ?: 'Not provided' }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Date of Birth</label>
                                    <p class="mb-0">{{ $user->date_of_birth ? $user->date_of_birth->format('F j, Y') : 'Not provided' }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Gender</label>
                                    <p class="mb-0">{{ $user->gender ? ucfirst($user->gender) : 'Not provided' }}</p>
                                </div>
                            </div>
                            @if($user->student_id)
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Student ID</label>
                                    <p class="mb-0">{{ $user->student_id }}</p>
                                </div>
                            </div>
                            @endif
                            @if($user->address)
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Address</label>
                                    <p class="mb-0">{{ $user->address }}</p>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Emergency Contact -->
            @if($user->emergency_contact_name || $user->emergency_contact_phone)
            <div class="col-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-phone me-2"></i>Emergency Contact</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Contact Name</label>
                                    <p class="mb-0">{{ $user->emergency_contact_name ?: 'Not provided' }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Contact Phone</label>
                                    <p class="mb-0">{{ $user->emergency_contact_phone ?: 'Not provided' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif
            
            <!-- Summit Registrations -->
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-mountain me-2"></i>Summit Registrations</h5>
                    </div>
                    <div class="card-body">
                        @if($user->summitRegistrations->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Summit</th>
                                        <th>Year</th>
                                        <th>Registration Date</th>
                                        <th>Payment Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($user->summitRegistrations as $registration)
                                    <tr>
                                        <td>
                                            <div>
                                                <div class="fw-bold">{{ $registration->summit->title }}</div>
                                                <small class="text-muted">{{ Str::limit($registration->summit->theme, 30) }}</small>
                                            </div>
                                        </td>
                                        <td>{{ $registration->summit->year }}</td>
                                        <td>{{ $registration->registered_at->format('M j, Y') }}</td>
                                        <td>
                                            @if($registration->payment_complete)
                                                <span class="badge bg-success">Paid</span>
                                            @else
                                                <span class="badge bg-warning">Pending</span>
                                                <br><small class="text-muted">TSh {{ number_format($registration->balance) }} remaining</small>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ route('registrations.show', $registration) }}" 
                                                   class="btn btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if(!$registration->payment_complete)
                                                <a href="{{ route('payments.create', $registration) }}" 
                                                   class="btn btn-outline-success">
                                                    <i class="fas fa-credit-card"></i>
                                                </a>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @else
                        <div class="text-center py-4">
                            <i class="fas fa-mountain fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Summit Registrations</h5>
                            <p class="text-muted">You haven't registered for any summits yet.</p>
                            <a href="{{ route('landing.index') }}" class="btn btn-gradient">
                                <i class="fas fa-plus me-2"></i>Register for Summit
                            </a>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
