<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class IdCard extends Model
{
    protected $fillable = [
        'user_id',
        'summit_id',
        'card_number',
        'qr_code',
        'qr_code_path',
        'is_active',
        'issued_at',
        'expires_at'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'issued_at' => 'datetime',
        'expires_at' => 'datetime'
    ];

    /**
     * Get the user that owns the ID card.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the summit that owns the ID card.
     */
    public function summit(): BelongsTo
    {
        return $this->belongsTo(Summit::class);
    }

    /**
     * Check if the ID card is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at < now();
    }

    /**
     * Scope a query to only include active ID cards.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
