<?php $__env->startSection('title', 'Activities Management'); ?>
<?php $__env->startSection('page-title', 'Activities Management'); ?>
<?php $__env->startSection('page-subtitle', 'Manage activities displayed on the landing page'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Enhanced Search -->
    <?php if (isset($component)) { $__componentOriginal7f45f03dbdf99a8f135c471b87d3c319 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7f45f03dbdf99a8f135c471b87d3c319 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.enhanced-search','data' => ['type' => 'activities','placeholder' => 'Search activities by title, description, location...','route' => ''.e(route('admin.activities.index')).'','filters' => [
            [
                'name' => 'search',
                'label' => 'Traditional Search',
                'type' => 'text',
                'placeholder' => 'Title, description, location...',
                'width' => 4
            ],
            [
                'name' => 'status',
                'label' => 'Status',
                'type' => 'select',
                'placeholder' => 'All Status',
                'width' => 2,
                'options' => ['1' => 'Active', '0' => 'Inactive']
            ],
            [
                'name' => 'featured',
                'label' => 'Featured',
                'type' => 'select',
                'placeholder' => 'All',
                'width' => 2,
                'options' => ['1' => 'Featured', '0' => 'Regular']
            ],
            [
                'name' => 'date_from',
                'label' => 'Date From',
                'type' => 'date',
                'width' => 2
            ],
            [
                'name' => 'date_to',
                'label' => 'Date To',
                'type' => 'date',
                'width' => 2
            ]
        ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('enhanced-search'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'activities','placeholder' => 'Search activities by title, description, location...','route' => ''.e(route('admin.activities.index')).'','filters' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
            [
                'name' => 'search',
                'label' => 'Traditional Search',
                'type' => 'text',
                'placeholder' => 'Title, description, location...',
                'width' => 4
            ],
            [
                'name' => 'status',
                'label' => 'Status',
                'type' => 'select',
                'placeholder' => 'All Status',
                'width' => 2,
                'options' => ['1' => 'Active', '0' => 'Inactive']
            ],
            [
                'name' => 'featured',
                'label' => 'Featured',
                'type' => 'select',
                'placeholder' => 'All',
                'width' => 2,
                'options' => ['1' => 'Featured', '0' => 'Regular']
            ],
            [
                'name' => 'date_from',
                'label' => 'Date From',
                'type' => 'date',
                'width' => 2
            ],
            [
                'name' => 'date_to',
                'label' => 'Date To',
                'type' => 'date',
                'width' => 2
            ]
        ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7f45f03dbdf99a8f135c471b87d3c319)): ?>
<?php $attributes = $__attributesOriginal7f45f03dbdf99a8f135c471b87d3c319; ?>
<?php unset($__attributesOriginal7f45f03dbdf99a8f135c471b87d3c319); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7f45f03dbdf99a8f135c471b87d3c319)): ?>
<?php $component = $__componentOriginal7f45f03dbdf99a8f135c471b87d3c319; ?>
<?php unset($__componentOriginal7f45f03dbdf99a8f135c471b87d3c319); ?>
<?php endif; ?>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>Activities
                    </h5>
                    <a href="<?php echo e(route('admin.activities.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Activity
                    </a>
                </div>
                <div class="card-body">
                    <?php if($activities->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Image</th>
                                        <th>Title</th>
                                        <th>Date & Time</th>
                                        <th>Location</th>
                                        <th>Order</th>
                                        <th>Status</th>
                                        <th>Featured</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $activities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <?php if($activity->image): ?>
                                                <img src="<?php echo e(Storage::url($activity->image)); ?>" 
                                                     alt="<?php echo e($activity->title); ?>" 
                                                     class="rounded" 
                                                     style="width: 50px; height: 50px; object-fit: cover;">
                                            <?php else: ?>
                                                <div class="bg-secondary rounded d-flex align-items-center justify-content-center" 
                                                     style="width: 50px; height: 50px;">
                                                    <i class="fas fa-image text-white"></i>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="fw-bold"><?php echo e($activity->title); ?></div>
                                            <small class="text-muted"><?php echo e(Str::limit($activity->description, 50)); ?></small>
                                        </td>
                                        <td>
                                            <?php if($activity->date): ?>
                                                <div><?php echo e($activity->date->format('M d, Y')); ?></div>
                                            <?php endif; ?>
                                            <?php if($activity->time): ?>
                                                <small class="text-muted"><?php echo e($activity->time->format('g:i A')); ?></small>
                                            <?php endif; ?>
                                            <?php if(!$activity->date && !$activity->time): ?>
                                                <span class="text-muted">Not scheduled</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($activity->location ?? 'N/A'); ?></td>
                                        <td><?php echo e($activity->order); ?></td>
                                        <td>
                                            <?php if($activity->is_active): ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Inactive</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($activity->is_featured): ?>
                                                <span class="badge bg-warning">Featured</span>
                                            <?php else: ?>
                                                <span class="badge bg-light text-dark">Regular</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.activities.show', $activity)); ?>" 
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.activities.edit', $activity)); ?>" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="<?php echo e(route('admin.activities.destroy', $activity)); ?>" 
                                                      method="POST" class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this activity?')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex justify-content-center">
                            <?php echo e($activities->links()); ?>

                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No activities found</h5>
                            <p class="text-muted">Start by adding your first activity.</p>
                            <a href="<?php echo e(route('admin.activities.create')); ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add Activity
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\summit-management-system\resources\views/admin/activities/index.blade.php ENDPATH**/ ?>