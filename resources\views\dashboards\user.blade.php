@extends('layouts.user')

@section('title', 'My Dashboard')

@push('styles')
<style>
    .profile-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #fff;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
</style>
@endpush

@section('content')
<!-- Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-1">Welcome back, {{ $user->first_name }}! 👋</h1>
                <p class="text-muted mb-0">Here's your summit management overview</p>
            </div>
            <div class="text-end">
                <small class="text-muted">{{ now()->format('l, F j, Y') }}</small>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1">Total Registrations</h6>
                        <h3 class="mb-0">{{ $registrations->count() }}</h3>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-clipboard-list fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stat-card-success">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1">Total Paid</h6>
                        <h3 class="mb-0">TZS {{ number_format($payments->where('status', 'completed')->sum('amount'), 0) }}</h3>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stat-card-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1">Pending Payments</h6>
                        <h3 class="mb-0">{{ $payments->whereIn('status', ['pending', 'processing'])->count() }}</h3>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stat-card-info">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1">ID Cards</h6>
                        <h3 class="mb-0">{{ $registrations->where('payment_complete', true)->count() }}</h3>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-id-card fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Row -->
<div class="row">
    <!-- Profile Summary -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>Profile Summary</h5>
            </div>
            <div class="card-body text-center">
                @if($user->profile_image)
                    <img src="{{ Storage::url($user->profile_image) }}"
                         alt="Profile" class="profile-avatar mb-3"
                         onerror="console.error('Failed to load image:', this.src); this.style.border='3px solid red';">
                @else
                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3"
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-user fa-2x text-white"></i>
                    </div>
                @endif
                <h5 class="mb-1">{{ $user->full_name }}</h5>
                <p class="text-muted mb-2">{{ $user->email }}</p>
                @if($user->branch)
                <div class="mb-3">
                    <small class="text-muted d-block">{{ $user->branch->name }}</small>
                    <small class="text-muted">{{ $user->branch->zone->name }}, {{ $user->branch->zone->conference->name }}</small>
                </div>
                @endif
                <div class="d-grid gap-2">
                    <a href="{{ route('profile.edit') }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Edit Profile
                    </a>
                    @if($currentSummit && !$currentRegistration)
                    <a href="{{ route('registrations.create', $currentSummit) }}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>Register for Summit
                    </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Analytics -->
    <div class="col-lg-8 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Payment Analytics</h5>
            </div>
            <div class="card-body">
                <canvas id="paymentChart" height="100"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    @if($currentSummit)
                    <div class="col-md-3 mb-3">
                        <div class="d-grid">
                            @if($currentRegistration)
                                <a href="{{ route('registrations.show', $currentRegistration) }}" class="btn btn-outline-primary">
                                    <i class="fas fa-eye me-2"></i>View Registration
                                </a>
                            @else
                                <a href="{{ route('registrations.create', $currentSummit) }}" class="btn btn-success">
                                    <i class="fas fa-plus me-2"></i>Register Now
                                </a>
                            @endif
                        </div>
                        <small class="text-muted mt-1">{{ $currentSummit->title }}</small>
                    </div>
                    @endif

                    <div class="col-md-3 mb-3">
                        <div class="d-grid">
                            <a href="{{ route('payments.index') }}" class="btn btn-outline-info">
                                <i class="fas fa-credit-card me-2"></i>View Payments
                            </a>
                        </div>
                        <small class="text-muted mt-1">Payment history</small>
                    </div>

                    <div class="col-md-3 mb-3">
                        <div class="d-grid">
                            <a href="{{ route('registrations.index') }}" class="btn btn-outline-warning">
                                <i class="fas fa-clipboard-list me-2"></i>My Registrations
                            </a>
                        </div>
                        <small class="text-muted mt-1">All registrations</small>
                    </div>

                    <div class="col-md-3 mb-3">
                        <div class="d-grid">
                            <a href="{{ route('id-cards.index') }}" class="btn btn-outline-success">
                                <i class="fas fa-id-card me-2"></i>ID Cards
                            </a>
                        </div>
                        <small class="text-muted mt-1">Download cards</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Current Summit Registration -->
@if($currentSummit && $currentRegistration)
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-star me-2"></i>Current Summit Registration</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h4>{{ $currentSummit->title }}</h4>
                        <p class="text-muted mb-2">{{ $currentSummit->theme }}</p>
                        <p class="mb-1">
                            <i class="fas fa-calendar text-primary me-2"></i>
                            {{ $currentSummit->start_date->format('F j') }} - 
                            {{ $currentSummit->end_date->format('F j, Y') }}
                        </p>
                        <p class="mb-1">
                            <i class="fas fa-map-marker-alt text-primary me-2"></i>
                            {{ $currentSummit->venue }}
                        </p>
                        
                        <!-- Payment Progress -->
                        <div class="mt-3">
                            <div class="d-flex justify-content-between mb-1">
                                <span>Payment Progress</span>
                                <span>TSh {{ number_format($currentRegistration->paid_amount) }} / TSh {{ number_format($currentRegistration->total_amount) }}</span>
                            </div>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-success" role="progressbar" 
                                     style="width: {{ ($currentRegistration->paid_amount / $currentRegistration->total_amount) * 100 }}%">
                                </div>
                            </div>
                            @if($currentRegistration->balance > 0)
                            <p class="text-warning mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Remaining Balance: TSh {{ number_format($currentRegistration->balance) }}
                            </p>
                            @else
                            <p class="text-success mb-0">
                                <i class="fas fa-check-circle me-2"></i>
                                Payment Complete!
                            </p>
                            @endif
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        @if($currentRegistration->balance > 0)
                        <a href="{{ route('payments.create', $currentRegistration) }}" 
                           class="btn btn-gradient mb-2">
                            <i class="fas fa-credit-card me-2"></i>Make Payment
                        </a>
                        @endif
                        <a href="{{ route('registrations.show', $currentRegistration) }}" 
                           class="btn btn-outline-primary mb-2">
                            <i class="fas fa-eye me-2"></i>View Details
                        </a>
                        @if($currentRegistration->payment_complete)
                        <a href="{{ route('id-cards.index') }}"
                           class="btn btn-success">
                            <i class="fas fa-id-card me-2"></i>Get ID Card
                        </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Upcoming Activities -->
@if($upcomingActivities->count() > 0)
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Upcoming Activities</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($upcomingActivities as $activity)
                    <div class="col-md-6 mb-3">
                        <div class="card border-start border-primary border-4">
                            <div class="card-body">
                                <h6 class="card-title">{{ $activity->title }}</h6>
                                <p class="card-text text-muted small">{{ Str::limit($activity->description, 80) }}</p>
                                <p class="mb-1">
                                    <i class="fas fa-clock text-primary me-2"></i>
                                    {{ $activity->start_time->format('M j, Y g:i A') }}
                                </p>
                                @if($activity->venue)
                                <p class="mb-0">
                                    <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                    {{ $activity->venue }}
                                </p>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                <div class="text-center">
                    <a href="{{ route('landing.activities', $currentSummit) }}" class="btn btn-outline-primary">
                        View All Activities <i class="fas fa-arrow-right ms-2"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Recent Activity -->
<div class="row">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Registrations</h5>
            </div>
            <div class="card-body">
                @if($registrations->count() > 0)
                @foreach($registrations->take(5) as $registration)
                <div class="timeline-item">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">{{ $registration->summit->title }}</h6>
                            <p class="text-muted small mb-1">{{ $registration->summit->year }}</p>
                            <p class="small mb-0">
                                Registered: {{ $registration->registered_at->format('M j, Y') }}
                            </p>
                        </div>
                        <span class="badge bg-{{ $registration->payment_complete ? 'success' : 'warning' }}">
                            {{ $registration->payment_complete ? 'Paid' : 'Pending' }}
                        </span>
                    </div>
                </div>
                @endforeach
                @else
                <p class="text-muted text-center">No registrations yet</p>
                @endif
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Recent Payments</h5>
            </div>
            <div class="card-body">
                @if($payments->count() > 0)
                @foreach($payments->take(5) as $payment)
                <div class="timeline-item">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">TSh {{ number_format($payment->amount) }}</h6>
                            <p class="text-muted small mb-1">{{ $payment->summitRegistration->summit->title }}</p>
                            <p class="small mb-0">
                                {{ $payment->paid_at ? $payment->paid_at->format('M j, Y') : $payment->created_at->format('M j, Y') }}
                            </p>
                        </div>
                        <span class="badge bg-{{ $payment->status === 'completed' ? 'success' : ($payment->status === 'pending' ? 'warning' : 'danger') }}">
                            {{ ucfirst($payment->status) }}
                        </span>
                    </div>
                </div>
                @endforeach
                @else
                <p class="text-muted text-center">No payments yet</p>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Payment Chart
    const ctx = document.getElementById('paymentChart').getContext('2d');

    const paymentData = @json($payments->groupBy(function($payment) {
        return $payment->created_at->format('M Y');
    })->map(function($payments) {
        return $payments->where('status', 'completed')->sum('amount');
    }));

    const labels = Object.keys(paymentData);
    const data = Object.values(paymentData);

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Payments (TZS)',
                data: data,
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#3b82f6',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#3b82f6',
                    borderWidth: 1,
                    callbacks: {
                        label: function(context) {
                            return 'TZS ' + new Intl.NumberFormat().format(context.parsed.y);
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return 'TZS ' + new Intl.NumberFormat().format(value);
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            elements: {
                point: {
                    hoverBackgroundColor: '#3b82f6'
                }
            }
        }
    });
});
</script>
@endpush
