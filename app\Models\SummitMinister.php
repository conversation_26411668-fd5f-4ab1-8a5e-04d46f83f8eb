<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SummitMinister extends Model
{
    protected $fillable = [
        'summit_id',
        'name',
        'title',
        'bio',
        'photo',
        'role',
        'organization',
        'contact_email',
        'contact_phone'
    ];

    /**
     * Get the summit that owns the minister.
     */
    public function summit(): BelongsTo
    {
        return $this->belongsTo(Summit::class);
    }
}
