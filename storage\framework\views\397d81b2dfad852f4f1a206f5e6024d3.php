<?php $__env->startSection('title', 'Summits Management'); ?>
<?php $__env->startSection('page-title', 'Summits Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">Summits Management</h2>
                <p class="text-muted">Manage summits in the system.</p>
            </div>
            <a href="<?php echo e(route('admin.summits.create')); ?>" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add Summit
            </a>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-search me-2"></i>Search & Filter</h5>
            </div>
            <div class="card-body">
                <form method="GET" action="<?php echo e(route('admin.summits.index')); ?>">
                    <div class="row">
                        <div class="col-md-6">
                            <input type="text" name="search" class="form-control" 
                                   placeholder="Search by title, theme, or venue..." 
                                   value="<?php echo e(request('search')); ?>">
                        </div>
                        <div class="col-md-3">
                            <select name="year" class="form-select">
                                <option value="">All Years</option>
                                <?php $__currentLoopData = $years; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $year): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($year); ?>" <?php echo e(request('year') == $year ? 'selected' : ''); ?>>
                                        <?php echo e($year); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Search
                                </button>
                                <a href="<?php echo e(route('admin.summits.index')); ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>Clear
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summits Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>Summits (<?php echo e($summits->total()); ?>)</h5>
            </div>
            <div class="card-body">
                <?php if($summits->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Summit</th>
                                <th>Year</th>
                                <th>Dates</th>
                                <th>Venue</th>
                                <th>Registrations</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $summits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $summit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <div>
                                        <div class="fw-bold"><?php echo e($summit->title); ?></div>
                                        <?php if($summit->theme): ?>
                                            <small class="text-muted"><?php echo e(Str::limit($summit->theme, 50)); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary"><?php echo e($summit->year); ?></span>
                                    <?php if($summit->is_current): ?>
                                        <span class="badge bg-success">Current</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div>
                                        <div><?php echo e($summit->start_date->format('M j')); ?> - <?php echo e($summit->end_date->format('M j, Y')); ?></div>
                                        <small class="text-muted">
                                            <?php if($summit->start_date > now()): ?>
                                                <span class="text-info">Upcoming</span>
                                            <?php elseif($summit->end_date < now()): ?>
                                                <span class="text-muted">Past</span>
                                            <?php else: ?>
                                                <span class="text-success">Ongoing</span>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                </td>
                                <td><?php echo e($summit->venue); ?></td>
                                <td>
                                    <div class="text-center">
                                        <span class="badge bg-info"><?php echo e($summit->registrations->count()); ?></span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo e($summit->is_active ? 'success' : 'secondary'); ?>">
                                        <?php echo e($summit->is_active ? 'Active' : 'Inactive'); ?>

                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?php echo e(route('admin.summits.show', $summit)); ?>" 
                                           class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.summits.edit', $summit)); ?>" 
                                           class="btn btn-outline-secondary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php if(!$summit->is_current): ?>
                                        <form method="POST" action="<?php echo e(route('admin.summits.set-current', $summit)); ?>" 
                                              class="d-inline">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="btn btn-outline-success" title="Set as Current"
                                                    onclick="return confirm('Set this as the current summit?')">
                                                <i class="fas fa-star"></i>
                                            </button>
                                        </form>
                                        <?php endif; ?>
                                        <?php if(!$summit->registrations->count()): ?>
                                        <form method="POST" action="<?php echo e(route('admin.summits.destroy', $summit)); ?>" 
                                              class="d-inline" onsubmit="return confirm('Are you sure you want to delete this summit?')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-outline-danger" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($summits->withQueryString()->links()); ?>

                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-mountain fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No summits found</h5>
                    <p class="text-muted">
                        <?php if(request('search')): ?>
                            No summits match your search criteria.
                        <?php else: ?>
                            Get started by adding your first summit.
                        <?php endif; ?>
                    </p>
                    <?php if(!request('search')): ?>
                    <a href="<?php echo e(route('admin.summits.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Summit
                    </a>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\summit-management-system\resources\views/admin/summits/index.blade.php ENDPATH**/ ?>