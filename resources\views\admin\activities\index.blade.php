@extends('layouts.admin')

@section('title', 'Activities Management')
@section('page-title', 'Activities Management')
@section('page-subtitle', 'Manage activities displayed on the landing page')

@section('content')
<div class="container-fluid">
    <!-- Enhanced Search -->
    <x-enhanced-search
        type="activities"
        placeholder="Search activities by title, description, location..."
        route="{{ route('admin.activities.index') }}"
        :filters="[
            [
                'name' => 'search',
                'label' => 'Traditional Search',
                'type' => 'text',
                'placeholder' => 'Title, description, location...',
                'width' => 4
            ],
            [
                'name' => 'status',
                'label' => 'Status',
                'type' => 'select',
                'placeholder' => 'All Status',
                'width' => 2,
                'options' => ['1' => 'Active', '0' => 'Inactive']
            ],
            [
                'name' => 'featured',
                'label' => 'Featured',
                'type' => 'select',
                'placeholder' => 'All',
                'width' => 2,
                'options' => ['1' => 'Featured', '0' => 'Regular']
            ],
            [
                'name' => 'date_from',
                'label' => 'Date From',
                'type' => 'date',
                'width' => 2
            ],
            [
                'name' => 'date_to',
                'label' => 'Date To',
                'type' => 'date',
                'width' => 2
            ]
        ]"
    />

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>Activities
                    </h5>
                    <a href="{{ route('admin.activities.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Activity
                    </a>
                </div>
                <div class="card-body">
                    @if($activities->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Image</th>
                                        <th>Title</th>
                                        <th>Date & Time</th>
                                        <th>Location</th>
                                        <th>Order</th>
                                        <th>Status</th>
                                        <th>Featured</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($activities as $activity)
                                    <tr>
                                        <td>
                                            @if($activity->image)
                                                <img src="{{ Storage::url($activity->image) }}" 
                                                     alt="{{ $activity->title }}" 
                                                     class="rounded" 
                                                     style="width: 50px; height: 50px; object-fit: cover;">
                                            @else
                                                <div class="bg-secondary rounded d-flex align-items-center justify-content-center" 
                                                     style="width: 50px; height: 50px;">
                                                    <i class="fas fa-image text-white"></i>
                                                </div>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="fw-bold">{{ $activity->title }}</div>
                                            <small class="text-muted">{{ Str::limit($activity->description, 50) }}</small>
                                        </td>
                                        <td>
                                            @if($activity->date)
                                                <div>{{ $activity->date->format('M d, Y') }}</div>
                                            @endif
                                            @if($activity->time)
                                                <small class="text-muted">{{ $activity->time->format('g:i A') }}</small>
                                            @endif
                                            @if(!$activity->date && !$activity->time)
                                                <span class="text-muted">Not scheduled</span>
                                            @endif
                                        </td>
                                        <td>{{ $activity->location ?? 'N/A' }}</td>
                                        <td>{{ $activity->order }}</td>
                                        <td>
                                            @if($activity->is_active)
                                                <span class="badge bg-success">Active</span>
                                            @else
                                                <span class="badge bg-secondary">Inactive</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($activity->is_featured)
                                                <span class="badge bg-warning">Featured</span>
                                            @else
                                                <span class="badge bg-light text-dark">Regular</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.activities.show', $activity) }}" 
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.activities.edit', $activity) }}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="{{ route('admin.activities.destroy', $activity) }}" 
                                                      method="POST" class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this activity?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex justify-content-center">
                            {{ $activities->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No activities found</h5>
                            <p class="text-muted">Start by adding your first activity.</p>
                            <a href="{{ route('admin.activities.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add Activity
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
