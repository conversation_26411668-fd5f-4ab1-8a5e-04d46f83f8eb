<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\UniversityController;
use App\Http\Controllers\Admin\ConferenceController;
use App\Http\Controllers\Admin\ZoneController;
use App\Http\Controllers\Admin\BranchController;
use App\Http\Controllers\Admin\SummitController;
use App\Http\Controllers\Admin\SummitActivityController;
use App\Http\Controllers\Admin\SummitMinisterController;
use App\Http\Controllers\Admin\ReportsController;
use App\Http\Controllers\Admin\PaymentController as AdminPaymentController;
use App\Http\Controllers\Admin\IdCardController as AdminIdCardController;
use App\Http\Controllers\Admin\RegistrationController as AdminRegistrationController;
use App\Http\Controllers\Admin\MinisterController as AdminMinisterController;
use App\Http\Controllers\Admin\ActivityController as AdminActivityController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\LandingController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\IdCardController;
use App\Http\Controllers\MealTrackingController;
use App\Http\Controllers\TreasurerController;

// Landing page routes
Route::get('/', [LandingController::class, 'index'])->name('landing.index');
Route::get('/summit/{summit}', [LandingController::class, 'summit'])->name('landing.summit');
Route::get('/summit/{summit}/activities', [LandingController::class, 'activities'])->name('landing.activities');
Route::get('/summit/{summit}/ministers', [LandingController::class, 'ministers'])->name('landing.ministers');



// Public ID Card Verification routes
Route::get('/verify-id-card/{card?}', [IdCardController::class, 'verify'])->name('id-card.verify');
Route::post('/api/verify-card', [IdCardController::class, 'verifyCard'])->name('api.verify-card');

// Public Meal Tracking API routes
Route::post('/api/meal-tracking/check-eligibility', [MealTrackingController::class, 'checkEligibility'])->name('api.meal-tracking.check-eligibility');
Route::post('/api/meal-tracking/user-history', [MealTrackingController::class, 'userHistory'])->name('api.meal-tracking.user-history');

Auth::routes();

Route::get('/home', function() {
    return redirect()->route('dashboard');
})->name('home');

// Dashboard routes
Route::middleware('auth')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // User profile routes
    Route::get('/profile', [ProfileController::class, 'show'])->name('profile.show');
    Route::get('/profile/edit', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');

    // User registration routes
    Route::get('/registrations', [DashboardController::class, 'registrations'])->name('registrations.index');
    Route::get('/registrations/create/{summit}', [DashboardController::class, 'createRegistration'])->name('registrations.create');
    Route::post('/registrations/{summit}', [DashboardController::class, 'storeRegistration'])->name('registrations.store');
    Route::get('/registrations/{registration}', [DashboardController::class, 'showRegistration'])->name('registrations.show');
    Route::patch('/registrations/{registration}/cancel', [DashboardController::class, 'cancelRegistration'])->name('registrations.cancel');

    // User payment routes
    Route::get('/my-payments', [DashboardController::class, 'payments'])->name('payments.index');

    // ID Card routes
    Route::get('/id-cards', [IdCardController::class, 'index'])->name('id-cards.index');
    Route::get('/id-cards/{idCard}', [IdCardController::class, 'show'])->name('id-cards.show');
    Route::post('/id-cards/generate', [IdCardController::class, 'generate'])->name('id-cards.generate');
    Route::get('/id-cards/{idCard}/download', [IdCardController::class, 'download'])->name('id-cards.download');
    Route::get('/id-cards/{idCard}/preview', [IdCardController::class, 'preview'])->name('id-cards.preview');
    Route::post('/id-cards/{idCard}/regenerate-qr', [IdCardController::class, 'regenerateQr'])->name('id-cards.regenerate-qr');

    // Legacy route for backward compatibility
    Route::get('/id-card', [DashboardController::class, 'idCard'])->name('id-card.show');

    // Meal Tracking routes
    Route::get('/meal-tracking/scanner', [MealTrackingController::class, 'scanner'])->name('meal-tracking.scanner');
    Route::post('/meal-tracking/scan', [MealTrackingController::class, 'scan'])->name('meal-tracking.scan');
    Route::get('/meal-tracking/dashboard', [MealTrackingController::class, 'dashboard'])->name('meal-tracking.dashboard');
    Route::get('/meal-tracking/reports', [MealTrackingController::class, 'reports'])->name('meal-tracking.reports');
    Route::post('/meal-tracking/manual', [MealTrackingController::class, 'manualTrack'])->name('meal-tracking.manual');

    // Help route
    Route::get('/help', [DashboardController::class, 'help'])->name('help');
});

// Treasurer routes
Route::middleware(['auth', 'role:treasurer|admin'])->prefix('treasurer')->name('treasurer.')->group(function () {
    Route::get('/dashboard', [TreasurerController::class, 'dashboard'])->name('dashboard');
    Route::get('/reports', [TreasurerController::class, 'reports'])->name('reports');
    Route::get('/payment-reports', [TreasurerController::class, 'paymentReports'])->name('payment-reports');
    Route::get('/analytics', [TreasurerController::class, 'analytics'])->name('analytics');
    Route::get('/outstanding-payments', [TreasurerController::class, 'outstandingPayments'])->name('outstanding-payments');

    // Export routes
    Route::get('/export/payment-report', [TreasurerController::class, 'exportPaymentReport'])->name('export.payment-report');
    Route::get('/export/revenue-summary', [TreasurerController::class, 'exportRevenueSummary'])->name('export.revenue-summary');

    // Treasurer access to admin resources (read-only)
    Route::get('/payments', [App\Http\Controllers\Admin\PaymentController::class, 'index'])->name('payments.index');
    Route::get('/payments/{payment}', [App\Http\Controllers\Admin\PaymentController::class, 'show'])->name('payments.show');
    Route::get('/registrations', [App\Http\Controllers\Admin\RegistrationController::class, 'index'])->name('registrations.index');
    Route::get('/registrations/{registration}', [App\Http\Controllers\Admin\RegistrationController::class, 'show'])->name('registrations.show');
    Route::get('/users', [App\Http\Controllers\Admin\UserController::class, 'index'])->name('users.index');
    Route::get('/users/{user}', [App\Http\Controllers\Admin\UserController::class, 'show'])->name('users.show');
});

// Primary Payment routes (AzamPay)
Route::middleware('auth')->group(function () {
    Route::get('/payments/{registration}/create', [App\Http\Controllers\AzamPayController::class, 'create'])->name('payments.create');
    Route::post('/payments/{registration}/mobile', [App\Http\Controllers\AzamPayController::class, 'processMobilePayment'])->name('payments.mobile');
    Route::post('/payments/{registration}/bank', [App\Http\Controllers\AzamPayController::class, 'processBankPayment'])->name('payments.bank');
    Route::post('/payments/{registration}/manual', [App\Http\Controllers\AzamPayController::class, 'processManualPayment'])->name('payments.manual');
    Route::get('/payments/{payment}', [App\Http\Controllers\AzamPayController::class, 'show'])->name('payments.show');
    Route::get('/payments/{payment}/status', [App\Http\Controllers\AzamPayController::class, 'status'])->name('payments.status');
    Route::get('/payments/{payment}/success', [App\Http\Controllers\AzamPayController::class, 'success'])->name('payments.success');
    Route::get('/payments/{payment}/cancel', [App\Http\Controllers\AzamPayController::class, 'cancel'])->name('payments.cancel');
});

// Legacy Selcom routes (for backward compatibility)
Route::middleware('auth')->group(function () {
    Route::get('/selcom/{registration}/create', [PaymentController::class, 'create'])->name('payments.selcom.create');
    Route::post('/selcom/{registration}', [PaymentController::class, 'store'])->name('payments.selcom.store');
    Route::get('/selcom/{payment}/status', [PaymentController::class, 'checkStatus'])->name('payments.selcom.status');
});

// AzamPay additional routes (for specific access patterns)
Route::middleware(['auth', 'throttle:payment'])->group(function () {
    Route::get('/azampay/{payment}/status', [App\Http\Controllers\AzamPayController::class, 'status'])->name('payments.azampay.status');
    Route::get('/azampay/{payment}/check', [App\Http\Controllers\AzamPayController::class, 'checkStatus'])->name('payments.azampay.check');
});

// Payment webhooks (no auth required)
Route::post('/payments/webhook', [PaymentController::class, 'webhook'])->name('payments.webhook');
Route::post('/azampay/webhook', [App\Http\Controllers\AzamPayController::class, 'webhook'])->name('payments.azampay.webhook');

// Profile routes
Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'show'])->name('profile.show');
    Route::get('/profile/edit', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [ProfileController::class, 'update'])->name('profile.update');
});

// Admin routes
Route::middleware(['auth', 'role:admin|conference_admin|zone_admin|branch_admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

    // User management - Available to all admin levels
    Route::resource('users', UserController::class);
    Route::get('/users/{user}/manage-roles', [UserController::class, 'manageRoles'])->name('users.manage-roles');
    Route::post('/users/assign-role', [UserController::class, 'assignRole'])->name('users.assign-role');
    Route::post('/users/remove-role', [UserController::class, 'removeRole'])->name('users.remove-role');
});

// Super admin only routes
Route::middleware(['auth', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    // Organization management - Super admin only
    Route::resource('universities', UniversityController::class);
    Route::resource('conferences', ConferenceController::class);
});

// Conference and Zone admin routes
Route::middleware(['auth', 'role:admin|conference_admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::resource('zones', ZoneController::class);
});

// Conference, Zone and Branch admin routes
Route::middleware(['auth', 'role:admin|conference_admin|zone_admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::resource('branches', BranchController::class);
});

// Conference admin and above routes
Route::middleware(['auth', 'role:admin|conference_admin'])->prefix('admin')->name('admin.')->group(function () {
    // Summit management
    Route::resource('summits', SummitController::class);
    Route::post('/summits/{summit}/set-current', [SummitController::class, 'setCurrent'])->name('summits.set-current');
    Route::resource('summits.activities', SummitActivityController::class)->except(['index', 'show']);
    Route::resource('summits.ministers', SummitMinisterController::class)->except(['index', 'show']);

    // Content management
    Route::resource('ministers', AdminMinisterController::class);
    Route::resource('activities', AdminActivityController::class);
});

// All admin levels routes
Route::middleware(['auth', 'role:admin|conference_admin|zone_admin|branch_admin'])->prefix('admin')->name('admin.')->group(function () {
    // Registration management
    Route::resource('registrations', AdminRegistrationController::class)->only(['index', 'show', 'update', 'destroy']);

    // Payment management
    Route::resource('payments', AdminPaymentController::class)->only(['index', 'show', 'update', 'destroy']);
    Route::patch('/payments/{payment}/approve', [AdminPaymentController::class, 'approve'])->name('payments.approve');
    Route::patch('/payments/{payment}/reject', [AdminPaymentController::class, 'reject'])->name('payments.reject');

    // ID Card Management
    Route::get('/manage-id-cards', [AdminIdCardController::class, 'index'])->name('id-cards.index');
    Route::get('/manage-id-cards/{idCard}', [AdminIdCardController::class, 'show'])->name('id-cards.show');
});

// Reports (accessible by admin, treasurer, and all admin levels)
Route::middleware(['auth', 'role:admin|treasurer|conference_admin|zone_admin|branch_admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/reports', [ReportsController::class, 'index'])->name('reports.index');
    Route::get('/reports/registrations', [ReportsController::class, 'registrations'])->name('reports.registrations');
    Route::get('/reports/payments', [ReportsController::class, 'payments'])->name('reports.payments');
    Route::get('/reports/summits', [ReportsController::class, 'summits'])->name('reports.summits');
    Route::get('/reports/users', [ReportsController::class, 'users'])->name('reports.users');
    Route::get('/reports/analytics', [ReportsController::class, 'analytics'])->name('reports.analytics');
    Route::post('/manage-id-cards/generate', [AdminIdCardController::class, 'generate'])->name('id-cards.generate');
    Route::patch('/manage-id-cards/{idCard}/deactivate', [AdminIdCardController::class, 'deactivate'])->name('id-cards.deactivate');
    Route::patch('/manage-id-cards/{idCard}/reactivate', [AdminIdCardController::class, 'reactivate'])->name('id-cards.reactivate');
    Route::post('/manage-id-cards/bulk-action', [AdminIdCardController::class, 'bulkAction'])->name('id-cards.bulk-action');
    Route::get('/manage-id-cards-export', [AdminIdCardController::class, 'export'])->name('id-cards.export');
    Route::get('/manage-id-cards-statistics', [AdminIdCardController::class, 'statistics'])->name('id-cards.statistics');

    // Registration Management (Admin)
    Route::get('/registrations', [AdminRegistrationController::class, 'index'])->name('registrations.index');
    Route::get('/registrations/{registration}', [AdminRegistrationController::class, 'show'])->name('registrations.show');
    Route::patch('/registrations/{registration}/cancel', [AdminRegistrationController::class, 'cancel'])->name('registrations.cancel');

    // AJAX routes for dependent dropdowns
    Route::get('/zones/by-conference/{conference}', [ZoneController::class, 'getByConference'])->name('zones.by-conference');
    Route::get('/branches/by-zone/{zone}', [BranchController::class, 'getByZone'])->name('branches.by-zone');
    Route::get('/branches/by-university/{university}', [BranchController::class, 'getByUniversity'])->name('branches.by-university');

    // Enhanced search API
    Route::get('/api/search', [App\Http\Controllers\Api\SearchController::class, 'search'])->name('api.search');

    // Session management API
    Route::middleware('auth')->group(function () {
        Route::get('/api/check-session', function () {
            return response()->json(['authenticated' => true]);
        });

        Route::post('/api/extend-session', function () {
            session()->regenerate();
            return response()->json(['extended' => true]);
        });
    });
});

// Multi-level admin routes (outside of admin prefix to avoid double prefix)
Route::middleware(['auth', 'role:admin|conference_admin|zone_admin|branch_admin'])->prefix('admin/multi-level')->name('admin.multi-level.')->group(function () {
    Route::get('/conference/{conference}/dashboard', [App\Http\Controllers\Admin\MultiLevelAdminController::class, 'conferenceDashboard'])->name('conference.dashboard');
    Route::get('/zone/{zone}/dashboard', [App\Http\Controllers\Admin\MultiLevelAdminController::class, 'zoneDashboard'])->name('zone.dashboard');
    Route::get('/branch/{branch}/dashboard', [App\Http\Controllers\Admin\MultiLevelAdminController::class, 'branchDashboard'])->name('branch.dashboard');

    Route::get('/conference/{conference}/export', [App\Http\Controllers\Admin\MultiLevelAdminController::class, 'exportConferenceReport'])->name('conference.export');
    Route::get('/zone/{zone}/export', [App\Http\Controllers\Admin\MultiLevelAdminController::class, 'exportZoneReport'])->name('zone.export');
    Route::get('/branch/{branch}/export', [App\Http\Controllers\Admin\MultiLevelAdminController::class, 'exportBranchReport'])->name('branch.export');
});

// Admin routes continued
Route::middleware(['auth', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    // SuperAdmin routes for role and permission management
    Route::middleware(['role:admin'])->prefix('superadmin')->name('superadmin.')->group(function () {
        Route::prefix('roles')->name('roles.')->group(function () {
            Route::get('/', [App\Http\Controllers\SuperAdmin\RolePermissionController::class, 'index'])->name('index');
            Route::get('/user-roles', [App\Http\Controllers\SuperAdmin\RolePermissionController::class, 'userRoles'])->name('user-roles');
            Route::get('/{role}', [App\Http\Controllers\SuperAdmin\RolePermissionController::class, 'showRole'])->name('show');
            Route::get('/data/ajax', [App\Http\Controllers\SuperAdmin\RolePermissionController::class, 'getRoleData'])->name('data');

            Route::post('/create', [App\Http\Controllers\SuperAdmin\RolePermissionController::class, 'createRole'])->name('create');
            Route::delete('/{role}', [App\Http\Controllers\SuperAdmin\RolePermissionController::class, 'deleteRole'])->name('delete');

            Route::post('/assign', [App\Http\Controllers\SuperAdmin\RolePermissionController::class, 'assignRole'])->name('assign');
            Route::post('/remove', [App\Http\Controllers\SuperAdmin\RolePermissionController::class, 'removeRole'])->name('remove');
            Route::post('/bulk-assign', [App\Http\Controllers\SuperAdmin\RolePermissionController::class, 'bulkAssignRoles'])->name('bulk-assign');

            Route::post('/{role}/add-permission', [App\Http\Controllers\SuperAdmin\RolePermissionController::class, 'addPermissionToRole'])->name('add-permission');
            Route::post('/{role}/remove-permission', [App\Http\Controllers\SuperAdmin\RolePermissionController::class, 'removePermissionFromRole'])->name('remove-permission');
        });
    });
});
