@extends('layouts.user')

@section('title', 'ID Card Details')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">ID Card Details</h2>
                <p class="text-muted">{{ $idCard->summit->title }} ({{ $idCard->summit->year }})</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('id-cards.download', $idCard) }}" class="btn btn-primary">
                    <i class="fas fa-download me-2"></i>Download PDF
                </a>
                <a href="{{ route('id-cards.index') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to ID Cards
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- ID Card Preview -->
    <div class="col-lg-8">
        <div class="card shadow-lg border-0" style="border-radius: 20px;">
            <div class="card-body p-0">
                <!-- ID Card Design -->
                <div class="id-card-container" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 20px; padding: 30px; color: white; position: relative; overflow: hidden;">
                    <!-- Background Pattern -->
                    <div class="position-absolute" style="top: -50px; right: -50px; opacity: 0.1;">
                        <i class="fas fa-mountain" style="font-size: 200px;"></i>
                    </div>
                    
                    <!-- Header -->
                    <div class="text-center mb-4">
                        <h4 class="mb-1 fw-bold">TUCASA SUMMIT</h4>
                        <h5 class="mb-0">{{ $idCard->summit->title }}</h5>
                        <small class="opacity-75">{{ $idCard->summit->year }}</small>
                    </div>
                    
                    <!-- Main Content -->
                    <div class="row align-items-center">
                        <!-- Profile Section -->
                        <div class="col-md-4 text-center">
                            @if($idCard->user->profile_image)
                                <img src="{{ Storage::url($idCard->user->profile_image) }}" 
                                     alt="Profile" 
                                     class="rounded-circle border border-3 border-white mb-3"
                                     style="width: 120px; height: 120px; object-fit: cover;">
                            @else
                                <div class="rounded-circle bg-white d-inline-flex align-items-center justify-content-center border border-3 border-white mb-3"
                                     style="width: 120px; height: 120px;">
                                    <i class="fas fa-user fa-3x text-primary"></i>
                                </div>
                            @endif
                            
                            <!-- QR Code -->
                            @if($idCard->qr_code_path && Storage::disk('public')->exists($idCard->qr_code_path))
                            <div class="mt-3">
                                <div class="bg-white p-2 rounded d-inline-block">
                                    <img src="{{ Storage::url($idCard->qr_code_path) }}" 
                                         alt="QR Code" 
                                         style="width: 80px; height: 80px;">
                                </div>
                            </div>
                            @endif
                        </div>
                        
                        <!-- Details Section -->
                        <div class="col-md-8">
                            <div class="mb-3">
                                <h3 class="mb-1 fw-bold">{{ $idCard->user->full_name }}</h3>
                                <p class="mb-1 opacity-75">{{ $idCard->user->email }}</p>
                                @if($idCard->user->phone)
                                    <p class="mb-0 opacity-75">{{ $idCard->user->phone }}</p>
                                @endif
                            </div>
                            
                            <div class="row">
                                <div class="col-6">
                                    <div class="mb-3">
                                        <small class="opacity-75 d-block">BRANCH</small>
                                        <strong>{{ $idCard->user->branch->name ?? 'N/A' }}</strong>
                                    </div>
                                    <div class="mb-3">
                                        <small class="opacity-75 d-block">UNIVERSITY</small>
                                        <strong>{{ $idCard->user->university->name ?? 'N/A' }}</strong>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="mb-3">
                                        <small class="opacity-75 d-block">CONFERENCE</small>
                                        <strong>{{ $idCard->user->branch->zone->conference->name ?? 'N/A' }}</strong>
                                    </div>
                                    <div class="mb-3">
                                        <small class="opacity-75 d-block">STUDENT ID</small>
                                        <strong>{{ $idCard->user->student_id ?? 'N/A' }}</strong>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Card Number -->
                            <div class="bg-white bg-opacity-20 rounded p-3 mt-3">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <small class="opacity-75 d-block">CARD NUMBER</small>
                                        <strong class="fs-5">{{ $idCard->card_number }}</strong>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-id-card fa-2x opacity-50"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Footer -->
                    <div class="row mt-4 pt-3 border-top border-white border-opacity-25">
                        <div class="col-6">
                            <small class="opacity-75 d-block">ISSUED</small>
                            <strong>{{ $idCard->issued_at->format('M j, Y') }}</strong>
                        </div>
                        <div class="col-6 text-end">
                            <small class="opacity-75 d-block">EXPIRES</small>
                            <strong>{{ $idCard->expires_at->format('M j, Y') }}</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Card Information -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Card Information</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Card Number:</strong></td>
                        <td>{{ $idCard->card_number }}</td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>Active
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Issued:</strong></td>
                        <td>{{ $idCard->issued_at->format('F j, Y \a\t g:i A') }}</td>
                    </tr>
                    <tr>
                        <td><strong>Expires:</strong></td>
                        <td>{{ $idCard->expires_at->format('F j, Y \a\t g:i A') }}</td>
                    </tr>
                    <tr>
                        <td><strong>Summit:</strong></td>
                        <td>{{ $idCard->summit->title }}</td>
                    </tr>
                    <tr>
                        <td><strong>Venue:</strong></td>
                        <td>{{ $idCard->summit->venue }}</td>
                    </tr>
                    <tr>
                        <td><strong>Dates:</strong></td>
                        <td>{{ $idCard->summit->start_date->format('M j') }} - {{ $idCard->summit->end_date->format('M j, Y') }}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('id-cards.download', $idCard) }}" class="btn btn-primary">
                        <i class="fas fa-download me-2"></i>Download PDF
                    </a>
                    <a href="{{ route('id-cards.preview', $idCard) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-search me-2"></i>Print Preview
                    </a>
                    <button type="button" class="btn btn-outline-info" onclick="shareCard()">
                        <i class="fas fa-share me-2"></i>Share Card
                    </button>
                    <form method="POST" action="{{ route('id-cards.regenerate-qr', $idCard) }}" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-outline-warning w-100" 
                                onclick="return confirm('Are you sure you want to regenerate the QR code?')">
                            <i class="fas fa-qrcode me-2"></i>Regenerate QR Code
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Verification -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Verification</h5>
            </div>
            <div class="card-body">
                <p class="text-muted small">Others can verify your ID card using:</p>
                <div class="mb-2">
                    <strong>Card Number:</strong>
                    <code>{{ $idCard->card_number }}</code>
                </div>
                <div class="mb-3">
                    <strong>Verification URL:</strong>
                    <div class="input-group">
                        <input type="text" class="form-control form-control-sm" 
                               value="{{ route('id-card.verify', $idCard->card_number) }}" 
                               id="verificationUrl" readonly>
                        <button class="btn btn-outline-secondary btn-sm" type="button" onclick="copyUrl()">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    This URL can be used to verify your ID card authenticity.
                </small>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function shareCard() {
    if (navigator.share) {
        navigator.share({
            title: 'TUCASA Summit ID Card',
            text: 'Verify my summit ID card: {{ $idCard->card_number }}',
            url: '{{ route("id-card.verify", $idCard->card_number) }}'
        });
    } else {
        copyUrl();
        alert('Verification URL copied to clipboard!');
    }
}

function copyUrl() {
    const urlInput = document.getElementById('verificationUrl');
    urlInput.select();
    urlInput.setSelectionRange(0, 99999);
    document.execCommand('copy');
}
</script>
@endpush

@push('styles')
<style>
.id-card-container {
    min-height: 400px;
}

@media print {
    .id-card-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
}
</style>
@endpush
