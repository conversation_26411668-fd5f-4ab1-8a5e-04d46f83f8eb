<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'selcom' => [
        'api_key' => env('SELCOM_API_KEY'),
        'secret_key' => env('SELCOM_SECRET_KEY'),
        'base_url' => env('SELCOM_BASE_URL', 'https://apigw.selcommobile.com/v1'),
        'vendor_type' => env('SELCOM_VENDOR_TYPE', 'PAYBILL'),
        'msisdn' => env('SELCOM_MSISDN'),
        'enabled' => env('SELCOM_ENABLED', false), // Disabled for AzamPay migration
    ],

    'azampay' => [
        'base_url' => env('AZAMPAY_BASE_URL', 'https://sandbox.azampay.co.tz'),
        'app_name' => env('AZAMPAY_APP_NAME', 'TUCASA_STU'),
        'client_id' => env('AZAMPAY_CLIENT_ID'),
        'client_secret' => env('AZAMPAY_CLIENT_SECRET'),
        'api_key' => env('AZAMPAY_API_KEY'),
        'merchant_mobile' => env('AZAMPAY_MERCHANT_MOBILE'),
        'merchant_account' => env('AZAMPAY_MERCHANT_ACCOUNT'),
        'webhook_secret' => env('AZAMPAY_WEBHOOK_SECRET'),
        'enabled' => env('AZAMPAY_ENABLED', true),
    ],

    'africastalking' => [
        'username' => env('AFRICASTALKING_USERNAME'),
        'api_key' => env('AFRICASTALKING_API_KEY'),
        'from' => env('AFRICASTALKING_FROM'),
    ],

];
