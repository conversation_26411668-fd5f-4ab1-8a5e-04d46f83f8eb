<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class Conference extends Model
{
    protected $fillable = [
        'name',
        'code',
        'region',
        'description',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    /**
     * Get the zones for the conference.
     */
    public function zones(): HasMany
    {
        return $this->hasMany(Zone::class);
    }

    /**
     * Get all branches in the conference through zones.
     */
    public function branches(): HasManyThrough
    {
        return $this->hasManyThrough(Branch::class, Zone::class);
    }

    /**
     * Get all users in the conference through zones and branches.
     */
    public function users()
    {
        return User::whereHas('branch.zone', function($query) {
            $query->where('conference_id', $this->id);
        });
    }
}
