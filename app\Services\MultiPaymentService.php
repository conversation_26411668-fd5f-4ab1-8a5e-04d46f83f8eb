<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\SummitRegistration;
use App\Services\SelcomPaymentService;
use Illuminate\Support\Facades\Log;

class MultiPaymentService
{
    protected $selcomService;

    public function __construct(SelcomPaymentService $selcomService)
    {
        $this->selcomService = $selcomService;
    }

    /**
     * Process payment based on method
     */
    public function processPayment(SummitRegistration $registration, array $paymentData)
    {
        $method = $paymentData['payment_method'];

        return match($method) {
            'selcom' => $this->processSelcomPayment($registration, $paymentData),
            'bank_transfer' => $this->processBankTransfer($registration, $paymentData),
            'cash' => $this->processCashPayment($registration, $paymentData),
            'mobile_money_direct' => $this->processMobileMoneyDirect($registration, $paymentData),
            default => ['success' => false, 'message' => 'Unsupported payment method']
        };
    }

    /**
     * Process Selcom payment (existing integration)
     */
    private function processSelcomPayment(SummitRegistration $registration, array $data)
    {
        return $this->selcomService->initiatePayment(
            $registration,
            $data['amount'],
            $data['phone_number'],
            $data['mno_provider'] ?? 'vodacom'
        );
    }

    /**
     * Process bank transfer payment
     */
    private function processBankTransfer(SummitRegistration $registration, array $data)
    {
        $paymentReference = 'BANK_' . $registration->id . '_' . time();

        $payment = Payment::create([
            'user_id' => $registration->user_id,
            'summit_registration_id' => $registration->id,
            'payment_reference' => $paymentReference,
            'amount' => $data['amount'],
            'payment_method' => 'bank_transfer',
            'status' => 'pending_verification',
            'gateway_data' => [
                'bank_name' => $data['bank_name'] ?? null,
                'account_number' => $data['account_number'] ?? null,
                'reference_number' => $data['reference_number'] ?? null,
                'transfer_date' => $data['transfer_date'] ?? null,
                'depositor_name' => $data['depositor_name'] ?? null,
            ],
        ]);

        // Send notification to admin for verification
        $this->notifyAdminForVerification($payment, 'bank_transfer');

        return [
            'success' => true,
            'payment' => $payment,
            'message' => 'Bank transfer recorded. Payment will be verified by admin within 24 hours.',
            'requires_verification' => true
        ];
    }

    /**
     * Process cash payment (for in-person payments)
     */
    private function processCashPayment(SummitRegistration $registration, array $data)
    {
        $paymentReference = 'CASH_' . $registration->id . '_' . time();

        $payment = Payment::create([
            'user_id' => $registration->user_id,
            'summit_registration_id' => $registration->id,
            'payment_reference' => $paymentReference,
            'amount' => $data['amount'],
            'payment_method' => 'cash',
            'status' => 'pending_verification',
            'gateway_data' => [
                'received_by' => $data['received_by'] ?? null,
                'receipt_number' => $data['receipt_number'] ?? null,
                'payment_location' => $data['payment_location'] ?? null,
                'payment_date' => $data['payment_date'] ?? now()->format('Y-m-d'),
            ],
        ]);

        // Send notification to admin for verification
        $this->notifyAdminForVerification($payment, 'cash');

        return [
            'success' => true,
            'payment' => $payment,
            'message' => 'Cash payment recorded. Payment will be verified by admin.',
            'requires_verification' => true
        ];
    }

    /**
     * Process direct mobile money (without Selcom)
     */
    private function processMobileMoneyDirect(SummitRegistration $registration, array $data)
    {
        $paymentReference = 'MM_' . $registration->id . '_' . time();

        $payment = Payment::create([
            'user_id' => $registration->user_id,
            'summit_registration_id' => $registration->id,
            'payment_reference' => $paymentReference,
            'amount' => $data['amount'],
            'payment_method' => 'mobile_money_direct',
            'status' => 'pending_verification',
            'gateway_data' => [
                'mno_provider' => $data['mno_provider'] ?? null,
                'phone_number' => $data['phone_number'] ?? null,
                'transaction_id' => $data['transaction_id'] ?? null,
                'confirmation_code' => $data['confirmation_code'] ?? null,
            ],
        ]);

        // Send notification to admin for verification
        $this->notifyAdminForVerification($payment, 'mobile_money_direct');

        return [
            'success' => true,
            'payment' => $payment,
            'message' => 'Mobile money payment recorded. Payment will be verified by admin.',
            'requires_verification' => true
        ];
    }

    /**
     * Get payment instructions for each method
     */
    public function getPaymentInstructions(string $method): array
    {
        return match($method) {
            'selcom' => [
                'title' => 'Selcom Mobile Money Payment',
                'instructions' => [
                    'Select your mobile money provider',
                    'Enter your mobile phone number',
                    'Click "Proceed to Payment"',
                    'You will receive a USSD prompt on your phone',
                    'Enter your mobile money PIN to complete the payment',
                    'You will receive a confirmation SMS upon successful payment'
                ],
                'processing_time' => 'Instant',
                'verification_required' => false
            ],
            'bank_transfer' => [
                'title' => 'Bank Transfer Payment',
                'instructions' => [
                    'Transfer money to our bank account:',
                    'Bank: CRDB Bank',
                    'Account Name: TUCASA STU',
                    'Account Number: 0150-*********',
                    'Use your registration number as reference',
                    'Upload proof of transfer or enter transfer details',
                    'Wait for admin verification'
                ],
                'processing_time' => '24-48 hours',
                'verification_required' => true
            ],
            'cash' => [
                'title' => 'Cash Payment',
                'instructions' => [
                    'Visit our office during business hours',
                    'Bring your registration details',
                    'Pay cash to the treasurer',
                    'Collect your receipt',
                    'Payment will be recorded immediately'
                ],
                'processing_time' => 'Instant (in-person)',
                'verification_required' => true
            ],
            'mobile_money_direct' => [
                'title' => 'Direct Mobile Money Transfer',
                'instructions' => [
                    'Send money directly to our mobile money account:',
                    'M-Pesa: 0754-123456 (TUCASA STU)',
                    'Tigo Pesa: 0654-123456 (TUCASA STU)',
                    'Airtel Money: 0684-123456 (TUCASA STU)',
                    'Use your registration number as reference',
                    'Enter the transaction ID and confirmation code',
                    'Wait for admin verification'
                ],
                'processing_time' => '2-24 hours',
                'verification_required' => true
            ],
            default => [
                'title' => 'Unknown Payment Method',
                'instructions' => ['Please contact support'],
                'processing_time' => 'Unknown',
                'verification_required' => false
            ]
        };
    }

    /**
     * Verify manual payment (for admin use)
     */
    public function verifyPayment(Payment $payment, bool $approved, string $notes = null)
    {
        if ($approved) {
            $payment->update([
                'status' => 'completed',
                'paid_at' => now(),
                'admin_notes' => $notes
            ]);

            // Update registration
            $registration = $payment->summitRegistration;
            $registration->paid_amount += $payment->amount;
            $registration->balance = $registration->total_amount - $registration->paid_amount;
            $registration->payment_complete = $registration->balance <= 0;
            $registration->save();

            return ['success' => true, 'message' => 'Payment verified and approved'];
        } else {
            $payment->update([
                'status' => 'failed',
                'admin_notes' => $notes ?? 'Payment verification failed'
            ]);

            return ['success' => false, 'message' => 'Payment verification failed'];
        }
    }

    /**
     * Get available payment methods
     */
    public function getAvailablePaymentMethods(): array
    {
        return [
            'selcom' => [
                'name' => 'Mobile Money (Selcom)',
                'description' => 'Pay instantly using M-Pesa, Tigo Pesa, Airtel Money, or Halopesa',
                'icon' => 'fas fa-mobile-alt',
                'instant' => true,
                'enabled' => config('services.selcom.enabled', true)
            ],
            'bank_transfer' => [
                'name' => 'Bank Transfer',
                'description' => 'Transfer money directly to our bank account',
                'icon' => 'fas fa-university',
                'instant' => false,
                'enabled' => true
            ],
            'cash' => [
                'name' => 'Cash Payment',
                'description' => 'Pay cash at our office during business hours',
                'icon' => 'fas fa-money-bill-wave',
                'instant' => false,
                'enabled' => true
            ],
            'mobile_money_direct' => [
                'name' => 'Direct Mobile Money',
                'description' => 'Send money directly to our mobile money accounts',
                'icon' => 'fas fa-phone',
                'instant' => false,
                'enabled' => true
            ]
        ];
    }

    /**
     * Send notification to admin for manual verification
     */
    private function notifyAdminForVerification(Payment $payment, string $method)
    {
        // Log for now - you can implement email/SMS notifications later
        Log::info("Payment verification required", [
            'payment_id' => $payment->id,
            'method' => $method,
            'amount' => $payment->amount,
            'user' => $payment->user->full_name,
            'reference' => $payment->payment_reference
        ]);

        // TODO: Send email to admin/treasurer
        // TODO: Send SMS notification
        // TODO: Create admin notification in database
    }
}
