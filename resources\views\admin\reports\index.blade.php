@extends(auth()->user()->hasRole('admin') ? 'layouts.admin' : 'layouts.user')

@section('title', 'Reports Dashboard')
@if(auth()->user()->hasRole('admin'))
@section('page-title', 'Reports & Analytics')
@endif

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <h2 class="mb-0">Reports & Analytics</h2>
        <p class="text-muted">Comprehensive reporting and analytics for your summit management system.</p>
    </div>
</div>

<!-- Overview Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="modern-stats-card blue h-100">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <h3 class="stat-number">{{ number_format($stats['total_users']) }}</h3>
            <p class="stat-label">Total Users</p>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="modern-stats-card green h-100">
            <div class="stat-icon">
                <i class="fas fa-user-plus"></i>
            </div>
            <h3 class="stat-number">{{ number_format($stats['total_registrations']) }}</h3>
            <p class="stat-label">Total Registrations</p>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="modern-stats-card orange h-100">
            <div class="stat-icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <h3 class="stat-number">TSh {{ number_format($stats['total_revenue']) }}</h3>
            <p class="stat-label">Total Revenue</p>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="modern-stats-card purple h-100">
            <div class="stat-icon">
                <i class="fas fa-code-branch"></i>
            </div>
            <h3 class="stat-number">{{ number_format($stats['active_branches']) }}</h3>
            <p class="stat-label">Active Branches</p>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row mb-4">
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>This Month</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>New Registrations</span>
                    <span class="badge bg-primary">{{ $recentStats['registrations_this_month'] }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Payments Received</span>
                    <span class="badge bg-success">TSh {{ number_format($recentStats['payments_this_month']) }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>New Users</span>
                    <span class="badge bg-info">{{ $recentStats['new_users_this_month'] }}</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Payment Status Overview</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center">
                        <div class="mb-3">
                            <h3 class="text-success">{{ number_format($stats['completed_payments']) }}</h3>
                            <p class="text-muted mb-0">Completed Payments</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="mb-3">
                            <h3 class="text-warning">{{ number_format($stats['pending_payments']) }}</h3>
                            <p class="text-muted mb-0">Pending Payments</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="mb-3">
                            <h3 class="text-primary">{{ number_format($stats['total_summits']) }}</h3>
                            <p class="text-muted mb-0">Total Summits</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Report Categories -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-user-plus me-2"></i>Registration Reports</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Detailed reports on summit registrations, payment status, and participant demographics.</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Registration by summit</li>
                    <li><i class="fas fa-check text-success me-2"></i>Payment status tracking</li>
                    <li><i class="fas fa-check text-success me-2"></i>Conference-wise breakdown</li>
                    <li><i class="fas fa-check text-success me-2"></i>Date range filtering</li>
                </ul>
                <a href="{{ route('admin.reports.registrations') }}" class="btn btn-primary">
                    <i class="fas fa-chart-line me-2"></i>View Registration Reports
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Payment Reports</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Comprehensive payment analytics, transaction history, and revenue tracking.</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Payment method breakdown</li>
                    <li><i class="fas fa-check text-success me-2"></i>Revenue analytics</li>
                    <li><i class="fas fa-check text-success me-2"></i>Transaction status</li>
                    <li><i class="fas fa-check text-success me-2"></i>Failed payment tracking</li>
                </ul>
                <a href="{{ route('admin.reports.payments') }}" class="btn btn-success">
                    <i class="fas fa-money-bill-wave me-2"></i>View Payment Reports
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0"><i class="fas fa-mountain me-2"></i>Summit Reports</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Summit performance metrics, attendance tracking, and event analytics.</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Summit performance</li>
                    <li><i class="fas fa-check text-success me-2"></i>Attendance statistics</li>
                    <li><i class="fas fa-check text-success me-2"></i>Revenue per summit</li>
                    <li><i class="fas fa-check text-success me-2"></i>Year-over-year comparison</li>
                </ul>
                <a href="{{ route('admin.reports.summits') }}" class="btn btn-warning">
                    <i class="fas fa-chart-bar me-2"></i>View Summit Reports
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-users me-2"></i>User Reports</h5>
            </div>
            <div class="card-body">
                <p class="card-text">User management reports, role distribution, and activity tracking.</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>User demographics</li>
                    <li><i class="fas fa-check text-success me-2"></i>Role distribution</li>
                    <li><i class="fas fa-check text-success me-2"></i>University breakdown</li>
                    <li><i class="fas fa-check text-success me-2"></i>Activity tracking</li>
                </ul>
                <a href="{{ route('admin.reports.users') }}" class="btn btn-info">
                    <i class="fas fa-user-chart me-2"></i>View User Reports
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Analytics Dashboard -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Advanced Analytics</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Interactive charts, trends analysis, and comprehensive data visualization.</p>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-chart-line text-primary me-2"></i>Monthly registration trends</li>
                            <li><i class="fas fa-chart-area text-success me-2"></i>Payment analytics</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-chart-pie text-warning me-2"></i>Conference-wise distribution</li>
                            <li><i class="fas fa-chart-bar text-info me-2"></i>Branch performance metrics</li>
                        </ul>
                    </div>
                </div>
                <a href="{{ route('admin.reports.analytics') }}" class="btn btn-dark">
                    <i class="fas fa-analytics me-2"></i>View Analytics Dashboard
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
