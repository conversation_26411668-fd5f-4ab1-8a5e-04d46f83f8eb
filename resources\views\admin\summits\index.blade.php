@extends('layouts.admin')

@section('title', 'Summits Management')
@section('page-title', 'Summits Management')

@section('content')
<div class="admin-page-container">
    <!-- Page Header -->
    <div class="page-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1 class="page-title">Summits Management</h1>
                <p class="page-subtitle">Manage summits in the system.</p>
            </div>
            <a href="{{ route('admin.summits.create') }}" class="btn-primary">
                <span class="material-icons">add</span>
                Add Summit
            </a>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="search-card">
        <h2 class="search-title">
            <span class="material-icons">search</span>
            Search & Filter
        </h2>
        <form method="GET" action="{{ route('admin.summits.index') }}" class="search-form">
            <div class="search-inputs">
                <div class="form-group">
                    <label class="form-label">Search</label>
                    <input
                        type="text"
                        name="search"
                        class="form-input"
                        placeholder="Search by name, theme, location..."
                        value="{{ request('search') }}"
                    >
                </div>
                <div class="form-group">
                    <label class="form-label">Status</label>
                    <select name="status" class="form-select">
                        <option value="">All Status</option>
                        <option value="upcoming" {{ request('status') == 'upcoming' ? 'selected' : '' }}>Upcoming</option>
                        <option value="ongoing" {{ request('status') == 'ongoing' ? 'selected' : '' }}>Ongoing</option>
                        <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                    </select>
                </div>
            </div>
            <div class="search-actions">
                <button type="submit" class="btn-primary">
                    <span class="material-icons">search</span>
                    Search
                </button>
                <a href="{{ route('admin.summits.index') }}" class="btn-outline-secondary">
                    <span class="material-icons">clear</span>
                    Clear
                </a>
            </div>
        </form>
    </div>

    <!-- Summits Table -->
    <div class="data-table-card">
        <div class="table-header">
            <h2 class="table-title">
                <span class="material-icons">event</span>
                Summits ({{ $summits->total() }})
            </h2>
        </div>

        @if($summits->count() > 0)
        <div class="table-responsive">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Summit</th>
                        <th>Dates</th>
                        <th>Location</th>
                        <th>Registrations</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                        <div class="col-md-6">
                            <input type="text" name="search" class="form-control" 
                                   placeholder="Search by title, theme, or venue..." 
                                   value="{{ request('search') }}">
                        </div>
                        <div class="col-md-3">
                            <select name="year" class="form-select">
                                <option value="">All Years</option>
                                @foreach($years as $year)
                                    <option value="{{ $year }}" {{ request('year') == $year ? 'selected' : '' }}>
                                        {{ $year }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Search
                                </button>
                                <a href="{{ route('admin.summits.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>Clear
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summits Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>Summits ({{ $summits->total() }})</h5>
            </div>
            <div class="card-body">
                @if($summits->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Summit</th>
                                <th>Year</th>
                                <th>Dates</th>
                                <th>Venue</th>
                                <th>Registrations</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($summits as $summit)
                            <tr>
                                <td>
                                    <div>
                                        <div class="fw-bold">{{ $summit->title }}</div>
                                        @if($summit->theme)
                                            <small class="text-muted">{{ Str::limit($summit->theme, 50) }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ $summit->year }}</span>
                                    @if($summit->is_current)
                                        <span class="badge bg-success">Current</span>
                                    @endif
                                </td>
                                <td>
                                    <div>
                                        <div>{{ $summit->start_date->format('M j') }} - {{ $summit->end_date->format('M j, Y') }}</div>
                                        <small class="text-muted">
                                            @if($summit->start_date > now())
                                                <span class="text-info">Upcoming</span>
                                            @elseif($summit->end_date < now())
                                                <span class="text-muted">Past</span>
                                            @else
                                                <span class="text-success">Ongoing</span>
                                            @endif
                                        </small>
                                    </div>
                                </td>
                                <td>{{ $summit->venue }}</td>
                                <td>
                                    <div class="text-center">
                                        <span class="badge bg-info">{{ $summit->registrations->count() }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $summit->is_active ? 'success' : 'secondary' }}">
                                        {{ $summit->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('admin.summits.show', $summit) }}" 
                                           class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.summits.edit', $summit) }}" 
                                           class="btn btn-outline-secondary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @if(!$summit->is_current)
                                        <form method="POST" action="{{ route('admin.summits.set-current', $summit) }}" 
                                              class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-outline-success" title="Set as Current"
                                                    onclick="return confirm('Set this as the current summit?')">
                                                <i class="fas fa-star"></i>
                                            </button>
                                        </form>
                                        @endif
                                        @if(!$summit->registrations->count())
                                        <form method="POST" action="{{ route('admin.summits.destroy', $summit) }}" 
                                              class="d-inline" onsubmit="return confirm('Are you sure you want to delete this summit?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-outline-danger" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $summits->withQueryString()->links() }}
                </div>
                @else
                <div class="text-center py-4">
                    <i class="fas fa-mountain fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No summits found</h5>
                    <p class="text-muted">
                        @if(request('search'))
                            No summits match your search criteria.
                        @else
                            Get started by adding your first summit.
                        @endif
                    </p>
                    @if(!request('search'))
                    <a href="{{ route('admin.summits.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Summit
                    </a>
                    @endif
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
