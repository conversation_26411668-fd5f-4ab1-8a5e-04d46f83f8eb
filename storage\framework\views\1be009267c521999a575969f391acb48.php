<?php $__env->startSection('title', 'Registrations Management'); ?>
<?php $__env->startSection('page-title', 'Registrations Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">Registrations Management</h2>
                <p class="text-muted">Manage and monitor summit registrations.</p>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Search -->
<?php if (isset($component)) { $__componentOriginal7f45f03dbdf99a8f135c471b87d3c319 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7f45f03dbdf99a8f135c471b87d3c319 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.enhanced-search','data' => ['type' => 'registrations','placeholder' => 'Search registrations by user name, email, registration number...','route' => ''.e(route('admin.registrations.index')).'','filters' => [
        [
            'name' => 'search',
            'label' => 'Traditional Search',
            'type' => 'text',
            'placeholder' => 'Name, email, registration number...',
            'width' => 4
        ],
        [
            'name' => 'summit_id',
            'label' => 'Summit',
            'type' => 'select',
            'placeholder' => 'All Summits',
            'width' => 3,
            'options' => $summits->pluck('title', 'id')->toArray()
        ],
        [
            'name' => 'status',
            'label' => 'Status',
            'type' => 'select',
            'placeholder' => 'All Status',
            'width' => 2,
            'options' => ['confirmed' => 'Confirmed', 'pending' => 'Pending', 'cancelled' => 'Cancelled']
        ],
        [
            'name' => 'payment_status',
            'label' => 'Payment',
            'type' => 'select',
            'placeholder' => 'All',
            'width' => 3,
            'options' => ['complete' => 'Payment Complete', 'partial' => 'Partial Payment', 'pending' => 'Payment Pending']
        ]
    ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('enhanced-search'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'registrations','placeholder' => 'Search registrations by user name, email, registration number...','route' => ''.e(route('admin.registrations.index')).'','filters' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
        [
            'name' => 'search',
            'label' => 'Traditional Search',
            'type' => 'text',
            'placeholder' => 'Name, email, registration number...',
            'width' => 4
        ],
        [
            'name' => 'summit_id',
            'label' => 'Summit',
            'type' => 'select',
            'placeholder' => 'All Summits',
            'width' => 3,
            'options' => $summits->pluck('title', 'id')->toArray()
        ],
        [
            'name' => 'status',
            'label' => 'Status',
            'type' => 'select',
            'placeholder' => 'All Status',
            'width' => 2,
            'options' => ['confirmed' => 'Confirmed', 'pending' => 'Pending', 'cancelled' => 'Cancelled']
        ],
        [
            'name' => 'payment_status',
            'label' => 'Payment',
            'type' => 'select',
            'placeholder' => 'All',
            'width' => 3,
            'options' => ['complete' => 'Payment Complete', 'partial' => 'Partial Payment', 'pending' => 'Payment Pending']
        ]
    ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7f45f03dbdf99a8f135c471b87d3c319)): ?>
<?php $attributes = $__attributesOriginal7f45f03dbdf99a8f135c471b87d3c319; ?>
<?php unset($__attributesOriginal7f45f03dbdf99a8f135c471b87d3c319); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7f45f03dbdf99a8f135c471b87d3c319)): ?>
<?php $component = $__componentOriginal7f45f03dbdf99a8f135c471b87d3c319; ?>
<?php unset($__componentOriginal7f45f03dbdf99a8f135c471b87d3c319); ?>
<?php endif; ?>

<!-- Traditional Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" action="<?php echo e(route('admin.registrations.index')); ?>">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="summit_id" class="form-label">Summit</label>
                            <select name="summit_id" id="summit_id" class="form-select">
                                <option value="">All Summits</option>
                                <?php $__currentLoopData = $summits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $summit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($summit->id); ?>" <?php echo e(request('summit_id') == $summit->id ? 'selected' : ''); ?>>
                                        <?php echo e($summit->title); ?> (<?php echo e($summit->year); ?>)
                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="payment_status" class="form-label">Payment Status</label>
                            <select name="payment_status" id="payment_status" class="form-select">
                                <option value="">All Statuses</option>
                                <option value="paid" <?php echo e(request('payment_status') == 'paid' ? 'selected' : ''); ?>>Paid</option>
                                <option value="pending" <?php echo e(request('payment_status') == 'pending' ? 'selected' : ''); ?>>Pending</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="Search by name, email, or registration number..." 
                                   value="<?php echo e(request('search')); ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Filter
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Registrations Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clipboard-list me-2"></i>Registrations 
                    <span class="badge bg-primary ms-2"><?php echo e($registrations->total()); ?></span>
                </h5>
            </div>
            <div class="card-body">
                <?php if($registrations->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Registration #</th>
                                <th>User</th>
                                <th>Summit</th>
                                <th>Status</th>
                                <th>Amount</th>
                                <th>Payment Status</th>
                                <th>Registered Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $registrations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $registration): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="<?php echo e($registration->status === 'cancelled' ? 'table-secondary' : ''); ?>">
                                <td>
                                    <code><?php echo e($registration->registration_number); ?></code>
                                </td>
                                <td>
                                    <div>
                                        <strong class="<?php echo e($registration->status === 'cancelled' ? 'text-muted' : ''); ?>">
                                            <?php echo e($registration->user->full_name); ?>

                                        </strong><br>
                                        <small class="text-muted"><?php echo e($registration->user->email); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong class="<?php echo e($registration->status === 'cancelled' ? 'text-muted' : ''); ?>">
                                            <?php echo e($registration->summit->title); ?>

                                            <?php if($registration->status === 'cancelled'): ?>
                                                <i class="fas fa-times-circle text-danger ms-1" title="Cancelled"></i>
                                            <?php endif; ?>
                                        </strong><br>
                                        <small class="text-muted"><?php echo e($registration->summit->year); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo e($registration->status === 'confirmed' ? 'success' : ($registration->status === 'cancelled' ? 'danger' : 'warning')); ?>">
                                        <?php echo e(ucfirst($registration->status)); ?>

                                    </span>
                                </td>
                                <td>
                                    <div>
                                        <strong class="<?php echo e($registration->status === 'cancelled' ? 'text-muted' : ''); ?>">
                                            TZS <?php echo e(number_format($registration->total_amount, 0)); ?>

                                        </strong><br>
                                        <?php if($registration->status !== 'cancelled'): ?>
                                            <small class="text-muted">
                                                Paid: TZS <?php echo e(number_format($registration->paid_amount, 0)); ?>

                                            </small>
                                        <?php else: ?>
                                            <small class="text-muted">Registration cancelled</small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php if($registration->status === 'cancelled'): ?>
                                        <span class="badge bg-secondary">Cancelled</span>
                                    <?php else: ?>
                                        <span class="badge <?php echo e($registration->payment_complete ? 'bg-success' : 'bg-warning'); ?>">
                                            <?php echo e($registration->payment_complete ? 'Paid' : 'Pending'); ?>

                                        </span>
                                        <?php if($registration->balance > 0): ?>
                                            <br><small class="text-danger">Balance: TZS <?php echo e(number_format($registration->balance, 2)); ?></small>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($registration->registered_at->format('M j, Y')); ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?php echo e(route('admin.registrations.show', $registration)); ?>" 
                                           class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($registrations->withQueryString()->links()); ?>

                </div>
                <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Registrations Found</h5>
                    <p class="text-muted">No registrations match your current filters.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\summit-management-system\resources\views/admin/registrations/index.blade.php ENDPATH**/ ?>