@extends('layouts.admin')

@section('title', 'User Role Assignment')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">👥 User Role Assignment</h2>
                    <p class="text-muted mb-0">Assign and manage user roles and permissions</p>
                </div>
                <div>
                    <a href="{{ route('admin.superadmin.roles.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Roles
                    </a>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#bulkAssignModal">
                        <i class="fas fa-users-cog me-2"></i>Bulk Assign
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.superadmin.roles.user-roles') }}" id="filter-form">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Search Users</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request('search') }}" placeholder="Name, email...">
                    </div>
                    <div class="col-md-3">
                        <label for="role" class="form-label">Filter by Role</label>
                        <select class="form-select" id="role" name="role">
                            <option value="">All Roles</option>
                            <option value="no_role" {{ request('role') === 'no_role' ? 'selected' : '' }}>No Role Assigned</option>
                            @foreach($roles as $role)
                            <option value="{{ $role->name }}" {{ request('role') === $role->name ? 'selected' : '' }}>
                                {{ ucwords(str_replace('_', ' ', $role->name)) }}
                            </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">User Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <a href="{{ route('admin.superadmin.roles.user-roles') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Clear
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-users me-2"></i>Users ({{ $users->total() }})
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>User</th>
                            <th>Current Role</th>
                            <th>Admin Level</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($users as $user)
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input user-checkbox" value="{{ $user->id }}">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-3">
                                        @if($user->profile_image)
                                        <img src="{{ Storage::url($user->profile_image) }}" 
                                             class="rounded-circle" width="40" height="40" alt="Avatar">
                                        @else
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px;">
                                            <span class="text-white fw-bold">{{ substr($user->first_name, 0, 1) }}</span>
                                        </div>
                                        @endif
                                    </div>
                                    <div>
                                        <strong>{{ $user->name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $user->email }}</small>
                                        @if($user->branch)
                                        <br>
                                        <small class="text-info">
                                            {{ $user->branch->name }} - {{ $user->branch->zone->name }}
                                        </small>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td>
                                @if($user->roles->count() > 0)
                                    @foreach($user->roles as $role)
                                    <span class="badge bg-primary me-1">
                                        {{ ucwords(str_replace('_', ' ', $role->name)) }}
                                    </span>
                                    @endforeach
                                @else
                                    <span class="badge bg-secondary">No Role</span>
                                @endif
                            </td>
                            <td>
                                @if($user->admin_conference_id)
                                    <small class="text-primary">
                                        <i class="fas fa-building me-1"></i>{{ $user->adminConference->name ?? 'N/A' }}
                                    </small>
                                @elseif($user->admin_zone_id)
                                    <small class="text-info">
                                        <i class="fas fa-map-marked-alt me-1"></i>{{ $user->adminZone->name ?? 'N/A' }}
                                    </small>
                                @elseif($user->admin_branch_id)
                                    <small class="text-secondary">
                                        <i class="fas fa-sitemap me-1"></i>{{ $user->adminBranch->name ?? 'N/A' }}
                                    </small>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                @if($user->is_active)
                                    <span class="badge bg-success">Active</span>
                                @else
                                    <span class="badge bg-danger">Inactive</span>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                            onclick="openAssignRoleModal({{ $user->id }}, '{{ $user->name }}', '{{ $user->roles->first()->name ?? '' }}')">
                                        <i class="fas fa-user-tag"></i>
                                    </button>
                                    @if($user->roles->count() > 0)
                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                            onclick="removeUserRole({{ $user->id }}, '{{ $user->roles->first()->name }}', '{{ $user->name }}')">
                                        <i class="fas fa-user-minus"></i>
                                    </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-users fa-3x mb-3"></i>
                                    <p>No users found matching your criteria.</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="text-muted">
                    Showing {{ $users->firstItem() ?? 0 }} to {{ $users->lastItem() ?? 0 }} of {{ $users->total() }} users
                </div>
                {{ $users->links() }}
            </div>
        </div>
    </div>
</div>

<!-- Assign Role Modal -->
<div class="modal fade" id="assignRoleModal" tabindex="-1" aria-labelledby="assignRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ route('admin.superadmin.roles.assign') }}" id="assignRoleForm">
                @csrf
                <input type="hidden" name="user_id" id="assignUserId">
                <div class="modal-header">
                    <h5 class="modal-title" id="assignRoleModalLabel">
                        <i class="fas fa-user-tag me-2"></i>Assign Role
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">User</label>
                        <input type="text" class="form-control" id="assignUserName" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="assignRole" class="form-label">Role</label>
                        <select class="form-select" id="assignRole" name="role" required>
                            <option value="">Select Role</option>
                            @foreach($roles as $role)
                            <option value="{{ $role->name }}">{{ ucwords(str_replace('_', ' ', $role->name)) }}</option>
                            @endforeach
                        </select>
                    </div>
                    
                    <!-- Admin Level Assignment -->
                    <div id="adminLevelSection" style="display: none;">
                        <div class="mb-3" id="conferenceSection" style="display: none;">
                            <label for="adminConference" class="form-label">Conference</label>
                            <select class="form-select" id="adminConference" name="admin_conference_id">
                                <option value="">Select Conference</option>
                            </select>
                        </div>
                        
                        <div class="mb-3" id="zoneSection" style="display: none;">
                            <label for="adminZone" class="form-label">Zone</label>
                            <select class="form-select" id="adminZone" name="admin_zone_id">
                                <option value="">Select Zone</option>
                            </select>
                        </div>
                        
                        <div class="mb-3" id="branchSection" style="display: none;">
                            <label for="adminBranch" class="form-label">Branch</label>
                            <select class="form-select" id="adminBranch" name="admin_branch_id">
                                <option value="">Select Branch</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Assign Role
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Assign Modal -->
<div class="modal fade" id="bulkAssignModal" tabindex="-1" aria-labelledby="bulkAssignModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ route('admin.superadmin.roles.bulk-assign') }}" id="bulkAssignForm">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="bulkAssignModalLabel">
                        <i class="fas fa-users-cog me-2"></i>Bulk Assign Roles
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Selected Users</label>
                        <div id="selectedUsersCount" class="form-text">No users selected</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="bulkRole" class="form-label">Role to Assign</label>
                        <select class="form-select" id="bulkRole" name="role" required>
                            <option value="">Select Role</option>
                            @foreach($roles as $role)
                            <option value="{{ $role->name }}">{{ ucwords(str_replace('_', ' ', $role->name)) }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="bulkAssignBtn" disabled>
                        <i class="fas fa-save me-2"></i>Assign to Selected Users
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Auto-submit filters
document.addEventListener('DOMContentLoaded', function() {
    const filterInputs = document.querySelectorAll('#filter-form select, #filter-form input[type="text"]');

    filterInputs.forEach(input => {
        if (input.type === 'text') {
            let timeout;
            input.addEventListener('input', function() {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    document.getElementById('filter-form').submit();
                }, 800);
            });
        } else {
            input.addEventListener('change', function() {
                document.getElementById('filter-form').submit();
            });
        }
    });
});

// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.user-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateBulkAssignButton();
});

// Update bulk assign button
function updateBulkAssignButton() {
    const selectedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
    const bulkAssignBtn = document.getElementById('bulkAssignBtn');
    const selectedUsersCount = document.getElementById('selectedUsersCount');

    if (selectedCheckboxes.length > 0) {
        bulkAssignBtn.disabled = false;
        selectedUsersCount.textContent = `${selectedCheckboxes.length} user(s) selected`;
    } else {
        bulkAssignBtn.disabled = true;
        selectedUsersCount.textContent = 'No users selected';
    }
}

// Add event listeners to checkboxes
document.querySelectorAll('.user-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateBulkAssignButton);
});

// Open assign role modal
function openAssignRoleModal(userId, userName, currentRole) {
    document.getElementById('assignUserId').value = userId;
    document.getElementById('assignUserName').value = userName;
    document.getElementById('assignRole').value = currentRole;

    // Reset admin level sections
    document.getElementById('adminLevelSection').style.display = 'none';
    document.getElementById('conferenceSection').style.display = 'none';
    document.getElementById('zoneSection').style.display = 'none';
    document.getElementById('branchSection').style.display = 'none';

    // Show modal
    new bootstrap.Modal(document.getElementById('assignRoleModal')).show();

    // Load conferences if needed
    if (['conference_admin', 'zone_admin', 'branch_admin'].includes(currentRole)) {
        loadConferences();
        showAdminLevelSection(currentRole);
    }
}

// Handle role change
document.getElementById('assignRole').addEventListener('change', function() {
    const role = this.value;
    const adminLevelSection = document.getElementById('adminLevelSection');

    if (['conference_admin', 'zone_admin', 'branch_admin'].includes(role)) {
        adminLevelSection.style.display = 'block';
        loadConferences();
        showAdminLevelSection(role);
    } else {
        adminLevelSection.style.display = 'none';
    }
});

// Show appropriate admin level section
function showAdminLevelSection(role) {
    // Hide all sections first
    document.getElementById('conferenceSection').style.display = 'none';
    document.getElementById('zoneSection').style.display = 'none';
    document.getElementById('branchSection').style.display = 'none';

    // Show relevant section
    if (role === 'conference_admin') {
        document.getElementById('conferenceSection').style.display = 'block';
    } else if (role === 'zone_admin') {
        document.getElementById('conferenceSection').style.display = 'block';
        document.getElementById('zoneSection').style.display = 'block';
    } else if (role === 'branch_admin') {
        document.getElementById('conferenceSection').style.display = 'block';
        document.getElementById('zoneSection').style.display = 'block';
        document.getElementById('branchSection').style.display = 'block';
    }
}

// Load conferences
function loadConferences() {
    fetch('/superadmin/roles/data?conferences=1')
        .then(response => response.json())
        .then(data => {
            const conferenceSelect = document.getElementById('adminConference');
            conferenceSelect.innerHTML = '<option value="">Select Conference</option>';

            data.conferences.forEach(conference => {
                conferenceSelect.innerHTML += `<option value="${conference.id}">${conference.name}</option>`;
            });
        })
        .catch(error => console.error('Error loading conferences:', error));
}

// Load zones when conference changes
document.getElementById('adminConference').addEventListener('change', function() {
    const conferenceId = this.value;
    const zoneSelect = document.getElementById('adminZone');
    const branchSelect = document.getElementById('adminBranch');

    // Reset zones and branches
    zoneSelect.innerHTML = '<option value="">Select Zone</option>';
    branchSelect.innerHTML = '<option value="">Select Branch</option>';

    if (conferenceId) {
        fetch(`/admin/superadmin/roles/data/ajax?zones=1&conference_id=${conferenceId}`)
            .then(response => response.json())
            .then(data => {
                data.zones.forEach(zone => {
                    zoneSelect.innerHTML += `<option value="${zone.id}">${zone.name}</option>`;
                });
            })
            .catch(error => console.error('Error loading zones:', error));
    }
});

// Load branches when zone changes
document.getElementById('adminZone').addEventListener('change', function() {
    const zoneId = this.value;
    const branchSelect = document.getElementById('adminBranch');

    // Reset branches
    branchSelect.innerHTML = '<option value="">Select Branch</option>';

    if (zoneId) {
        fetch(`/admin/superadmin/roles/data/ajax?branches=1&zone_id=${zoneId}`)
            .then(response => response.json())
            .then(data => {
                data.branches.forEach(branch => {
                    branchSelect.innerHTML += `<option value="${branch.id}">${branch.name}</option>`;
                });
            })
            .catch(error => console.error('Error loading branches:', error));
    }
});

// Remove user role
function removeUserRole(userId, roleName, userName) {
    if (confirm(`Are you sure you want to remove the "${roleName}" role from ${userName}?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("admin.superadmin.roles.remove") }}';

        form.innerHTML = `
            @csrf
            <input type="hidden" name="user_id" value="${userId}">
            <input type="hidden" name="role" value="${roleName}">
        `;

        document.body.appendChild(form);
        form.submit();
    }
}

// Handle bulk assign form submission
document.getElementById('bulkAssignForm').addEventListener('submit', function(e) {
    const selectedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
    const userIds = Array.from(selectedCheckboxes).map(cb => cb.value);

    // Add hidden inputs for selected user IDs
    userIds.forEach(userId => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'user_ids[]';
        input.value = userId;
        this.appendChild(input);
    });
});
</script>
@endpush
