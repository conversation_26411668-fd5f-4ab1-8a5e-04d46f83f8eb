<?php

namespace App\Policies;

use App\Models\SummitRegistration;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class SummitRegistrationPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return false;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, SummitRegistration $summitRegistration): bool
    {
        // Users can view their own registrations
        if ($user->id === $summitRegistration->user_id) {
            return true;
        }

        // Admins and treasurers can view all registrations
        return $user->hasPermissionTo('view registrations') || $user->hasRole(['admin', 'treasurer', 'moderator']);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return false;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, SummitRegistration $summitRegistration): bool
    {
        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, SummitRegistration $summitRegistration): bool
    {
        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, SummitRegistration $summitRegistration): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, SummitRegistration $summitRegistration): bool
    {
        return false;
    }
}
