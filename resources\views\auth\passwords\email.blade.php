@extends('layouts.auth')

@section('title', 'Reset Password - TUCASA Summit Management')

@section('content')
<div class="auth-container">
    <div class="auth-card">
        <!-- Header -->
        <div class="auth-header">
            <div class="auth-logo">
                <div class="auth-logo-icon">T</div>
                <div class="auth-logo-text">TUCASA Account</div>
            </div>
            <h1 class="auth-title">Reset Password</h1>
            <p class="auth-subtitle">Enter your email address and we'll send you a link to reset your password</p>
        </div>

        @if (session('status'))
            <div style="background-color: #d4edda; color: #155724; padding: 16px; border-radius: 8px; margin-bottom: 24px; border: 1px solid #c3e6cb;">
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span class="material-icons" style="font-size: 20px;">check_circle</span>
                    {{ session('status') }}
                </div>
            </div>
        @endif

        <!-- Form -->
        <form method="POST" action="{{ route('password.email') }}" class="auth-form">
            @csrf

            <!-- Email Field -->
            <div class="form-group">
                <label for="email" class="form-label">Email Address</label>
                <input
                    id="email"
                    type="email"
                    class="form-input @error('email') error @enderror"
                    name="email"
                    value="{{ old('email') }}"
                    required
                    autocomplete="email"
                    autofocus
                    placeholder="<EMAIL>"
                >
                @error('email')
                    <div class="error-message">
                        <span class="material-icons" style="font-size: 16px;">error</span>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Submit Button -->
            <button type="submit" class="auth-button">Send Password Reset Link</button>
        </form>

        <!-- Footer -->
        <div class="auth-footer">
            Remember your password? <a href="{{ route('login') }}">Sign in here</a>
        </div>
    </div>
</div>
@endsection
