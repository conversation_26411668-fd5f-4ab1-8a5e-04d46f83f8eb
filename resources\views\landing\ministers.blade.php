<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ministers - {{ $summit->title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        .minister-card {
            transition: transform 0.3s;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            height: 100%;
        }
        .minister-card:hover {
            transform: translateY(-5px);
        }
        .minister-photo {
            height: 250px;
            object-fit: cover;
            border-radius: 10px 10px 0 0;
        }
        .role-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .role-badge {
            background: #667eea;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .social-links a {
            color: #667eea;
            font-size: 1.2em;
            margin-right: 10px;
            transition: color 0.3s;
        }
        .social-links a:hover {
            color: #764ba2;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ route('landing.index') }}">
                <i class="fas fa-mountain"></i> TUCASA STU Summit
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('landing.index') }}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('landing.summit', $summit) }}">Summit Details</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('landing.activities', $summit) }}">Activities</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ route('landing.ministers', $summit) }}">Ministers</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    @guest
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('login') }}">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('register') }}">Register</a>
                    </li>
                    @else
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            {{ Auth::user()->name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ route('dashboard') }}">Dashboard</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ route('logout') }}" 
                                   onclick="event.preventDefault(); document.getElementById('logout-form').submit();">Logout</a></li>
                        </ul>
                        <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                            @csrf
                        </form>
                    </li>
                    @endguest
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="display-4 fw-bold">Summit Ministers</h1>
                    <p class="lead">{{ $summit->title }} - {{ $summit->year }}</p>
                    <p class="mb-0">Meet the inspiring speakers and ministers who will be sharing God's word</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Ministers Section -->
    <section class="py-5">
        <div class="container">
            @if($ministers->count() > 0)
                <div class="row">
                    @foreach($ministers as $minister)
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card minister-card h-100">
                            @if($minister->image)
                            <img src="{{ Storage::url($minister->image) }}"
                                 class="card-img-top minister-photo"
                                 alt="{{ $minister->name }}"
                                 style="height: 250px; object-fit: cover;">
                            @else
                            <div class="card-img-top minister-photo bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                                <i class="fas fa-user fa-4x text-muted"></i>
                            </div>
                            @endif

                            <div class="card-body">
                                <h5 class="card-title">{{ $minister->name }}</h5>

                                @if($minister->title)
                                <p class="text-muted mb-2">{{ $minister->title }}</p>
                                @endif

                                @if($minister->position)
                                <p class="text-muted mb-2">
                                    <i class="fas fa-briefcase me-1"></i>{{ $minister->position }}
                                </p>
                                @endif

                                @if($minister->bio)
                                <p class="card-text">{{ Str::limit($minister->bio, 150) }}</p>
                                @endif

                                @if($minister->email || $minister->phone)
                                <div class="border-top pt-3">
                                    <h6 class="text-muted mb-2">Contact Information</h6>
                                    @if($minister->email)
                                    <p class="mb-1">
                                        <i class="fas fa-envelope me-2"></i>
                                        <a href="mailto:{{ $minister->email }}" class="text-decoration-none">
                                            {{ $minister->email }}
                                        </a>
                                    </p>
                                    @endif

                                    @if($minister->phone)
                                    <p class="mb-1">
                                        <i class="fas fa-phone me-2"></i>
                                        <a href="tel:{{ $minister->phone }}" class="text-decoration-none">
                                            {{ $minister->phone }}
                                        </a>
                                    </p>
                                    @endif
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
            <div class="text-center py-5">
                <i class="fas fa-users fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">No Ministers Listed</h4>
                <p class="text-muted">Ministers for this summit haven't been announced yet. Check back soon!</p>
                <a href="{{ route('landing.summit', $summit) }}" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Summit Details
                </a>
            </div>
            @endif
        </div>
    </section>

    @if($ministers->count() > 0)
    <!-- Call to Action -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h3>Join Us for Inspiring Messages</h3>
                    <p class="lead text-muted">Don't miss the opportunity to hear from these amazing ministers!</p>
                    @auth
                        @php
                            $userRegistration = Auth::user()->summitRegistrations()
                                ->where('summit_id', $summit->id)
                                ->whereIn('status', ['pending', 'confirmed'])
                                ->first();
                        @endphp
                        @if(!$userRegistration)
                        <a href="{{ route('registrations.create', $summit) }}" class="btn btn-primary btn-lg">
                            <i class="fas fa-user-plus me-2"></i>Register for Summit
                        </a>
                        @else
                        <div class="alert alert-success d-inline-block">
                            <i class="fas fa-check-circle me-2"></i>You're already registered for this summit!
                            <a href="{{ route('registrations.show', $userRegistration) }}" class="btn btn-sm btn-outline-success ms-2">
                                View Registration
                            </a>
                        </div>
                        @endif
                    @else
                    <a href="{{ route('register') }}" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-user-plus me-2"></i>Register for Summit
                    </a>
                    <a href="{{ route('login') }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>Login
                    </a>
                    @endauth
                </div>
            </div>
        </div>
    </section>
    @endif

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>TUCASA STU Summit</h5>
                    <p class="text-muted">Empowering students through faith and fellowship.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted">&copy; {{ date('Y') }} TUCASA STU. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
