@extends('layouts.auth')

@section('title', 'Login - TUCASA Summit Management')

@push('styles')
<style>
body {
    background-color: #f5f5f5;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.auth-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    width: 100%;
    max-width: 400px;
    padding: 40px 32px;
}

.auth-header {
    text-align: center;
    margin-bottom: 32px;
}

.auth-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 24px;
}

.auth-logo-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 18px;
}

.auth-logo-text {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
}

.auth-title {
    font-size: 28px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 8px 0;
}

.auth-subtitle {
    font-size: 16px;
    color: #666;
    margin: 0;
    line-height: 1.5;
}

.auth-form {
    margin-bottom: 24px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 1.5px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.2s ease;
    background: white;
    box-sizing: border-box;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input.error {
    border-color: #ef4444;
}

.error-message {
    color: #ef4444;
    font-size: 14px;
    margin-top: 6px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.password-input-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 4px;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 20px 0;
}

.remember-me input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #667eea;
}

.remember-me label {
    font-size: 14px;
    color: #374151;
    cursor: pointer;
}

.auth-button {
    width: 100%;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 14px 20px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 16px;
}

.auth-button:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.auth-button:active {
    transform: translateY(0);
}

.forgot-password {
    text-align: center;
    margin: 16px 0;
}

.forgot-password a {
    color: #667eea;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

.forgot-password a:hover {
    text-decoration: underline;
}

.auth-footer {
    text-align: center;
    color: #6b7280;
    font-size: 14px;
}

.auth-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.auth-footer a:hover {
    text-decoration: underline;
}

@media (max-width: 480px) {
    .auth-card {
        padding: 32px 24px;
        margin: 16px;
    }

    .auth-title {
        font-size: 24px;
    }
}
</style>
@endpush

@section('content')
<div class="auth-container">
    <div class="auth-card">
        <!-- Header -->
        <div class="auth-header">
            <div class="auth-logo">
                <div class="auth-logo-icon">T</div>
                <div class="auth-logo-text">TUCASA Account</div>
            </div>
            <h1 class="auth-title">Login</h1>
            <p class="auth-subtitle">Enter your details below and login to your account</p>
        </div>

        <!-- Form -->
        <form method="POST" action="{{ route('login') }}" class="auth-form">
            @csrf

            <!-- Email Field -->
            <div class="form-group">
                <label for="email" class="form-label">Email or phone</label>
                <input
                    id="email"
                    type="email"
                    class="form-input @error('email') error @enderror"
                    name="email"
                    value="{{ old('email') }}"
                    required
                    autocomplete="email"
                    autofocus
                    placeholder="<EMAIL>"
                >
                @error('email')
                    <div class="error-message">
                        <span class="material-icons" style="font-size: 16px;">error</span>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Password Field -->
            <div class="form-group">
                <label for="password" class="form-label">Password</label>
                <div class="password-input-container">
                    <input
                        id="password"
                        type="password"
                        class="form-input @error('password') error @enderror"
                        name="password"
                        required
                        autocomplete="current-password"
                        placeholder="••••••••"
                    >
                    <button type="button" class="password-toggle" onclick="togglePassword()">
                        <span class="material-icons" id="password-icon">visibility</span>
                    </button>
                </div>
                @error('password')
                    <div class="error-message">
                        <span class="material-icons" style="font-size: 16px;">error</span>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Forgot Password Link -->
            @if (Route::has('password.request'))
            <div class="forgot-password">
                <a href="{{ route('password.request') }}">Forgot password?</a>
            </div>
            @endif

            <!-- Login Button -->
            <button type="submit" class="auth-button">Login</button>

            <!-- Remember Me -->
            <div class="remember-me">
                <input
                    type="checkbox"
                    name="remember"
                    id="remember"
                    {{ old('remember') ? 'checked' : '' }}
                >
                <label for="remember">Remember me</label>
            </div>
        </form>

        <!-- Footer -->
        <div class="auth-footer">
            Don't have an account? <a href="{{ route('register') }}">Signup here</a>
        </div>
    </div>
</div>

<script>
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const passwordIcon = document.getElementById('password-icon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordIcon.textContent = 'visibility_off';
    } else {
        passwordInput.type = 'password';
        passwordIcon.textContent = 'visibility';
    }
}
</script>
@endsection
