@extends('layouts.admin')

@section('title', 'Edit University')
@section('page-title', 'Edit University')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">Edit University</h2>
                <p class="text-muted">Update university information.</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('admin.universities.show', $university) }}" class="btn btn-outline-info">
                    <i class="fas fa-eye me-2"></i>View Details
                </a>
                <a href="{{ route('admin.universities.index') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Universities
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-university me-2"></i>University Information</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.universities.update', $university) }}">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="name" class="form-label">University Name <span class="text-danger">*</span></label>
                                <input type="text" name="name" id="name" class="form-control @error('name') is-invalid @enderror" 
                                       value="{{ old('name', $university->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="code" class="form-label">University Code <span class="text-danger">*</span></label>
                                <input type="text" name="code" id="code" class="form-control @error('code') is-invalid @enderror" 
                                       value="{{ old('code', $university->code) }}" placeholder="e.g., UDSM" required>
                                @error('code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="location" class="form-label">Location</label>
                        <input type="text" name="location" id="location" class="form-control @error('location') is-invalid @enderror" 
                               value="{{ old('location', $university->location) }}" placeholder="e.g., Dar es Salaam">
                        @error('location')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea name="description" id="description" class="form-control @error('description') is-invalid @enderror" 
                                  rows="4" placeholder="Brief description of the university...">{{ old('description', $university->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input type="hidden" name="is_active" value="0">
                            <input type="checkbox" name="is_active" id="is_active" class="form-check-input"
                                   value="1" {{ old('is_active', $university->is_active) ? 'checked' : '' }}>
                            <label for="is_active" class="form-check-label">
                                Active University
                            </label>
                        </div>
                        <small class="text-muted">Inactive universities won't be available for selection.</small>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update University
                        </button>
                        <a href="{{ route('admin.universities.show', $university) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>University Stats</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Total Branches</span>
                    <span class="badge bg-info">{{ $university->branches()->count() }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Total Users</span>
                    <span class="badge bg-success">{{ $university->users()->count() }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Created</span>
                    <span class="text-muted">{{ $university->created_at->format('M j, Y') }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Last Updated</span>
                    <span class="text-muted">{{ $university->updated_at->format('M j, Y') }}</span>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Warning</h5>
            </div>
            <div class="card-body">
                <p class="text-muted small">
                    Changing the university code may affect existing relationships. 
                    Deactivating a university will prevent new registrations but won't affect existing users.
                </p>
            </div>
        </div>
    </div>
</div>
@endsection
