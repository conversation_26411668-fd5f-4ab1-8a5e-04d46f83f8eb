@extends('errors.layout')

@section('title', 'Server Error')

@section('icon')
    <i class="fas fa-exclamation-triangle text-danger"></i>
@endsection

@section('code', '500')

@section('title', 'Internal Server Error')

@section('message')
    Something went wrong on our end. Our team has been notified and is working to fix this issue.
    Please try again in a few moments.
@endsection

@section('actions')
    <button id="refresh-btn" onclick="window.location.reload()" class="btn btn-primary">
        <i class="fas fa-sync-alt me-2"></i>Refresh Page
    </button>
    
    <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
        <i class="fas fa-home me-2"></i>Go to Dashboard
    </a>
@endsection

@section('details')
    <h6><i class="fas fa-tools me-2"></i>What you can try:</h6>
    <ul>
        <li>Refresh the page - the issue might be temporary</li>
        <li>Wait a few minutes and try again</li>
        <li>Clear your browser cache and cookies</li>
        <li>Try accessing the page from a different browser</li>
        <li>Contact support if the problem persists</li>
    </ul>
    
    @if(config('app.debug') && isset($exception))
    <div class="mt-3 p-3 bg-light border-start border-danger border-3">
        <h6 class="text-danger"><i class="fas fa-bug me-2"></i>Debug Information:</h6>
        <small class="text-muted font-monospace">
            {{ $exception->getMessage() }}<br>
            <strong>File:</strong> {{ $exception->getFile() }}<br>
            <strong>Line:</strong> {{ $exception->getLine() }}
        </small>
    </div>
    @endif
@endsection

@php
    $autoRefresh = true;
@endphp
