{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@popperjs/core": "^2.11.6", "@tailwindcss/vite": "^4.0.0", "axios": "^1.8.2", "bootstrap": "^5.2.3", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "sass": "^1.56.1", "tailwindcss": "^4.0.0", "vite": "^6.2.4"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@material/web": "^2.3.0", "@mui/icons-material": "^7.2.0", "@mui/lab": "^7.0.0-beta.14", "@mui/material": "^7.2.0", "@mui/x-data-grid": "^8.8.0", "material-components-web": "^14.0.0"}}