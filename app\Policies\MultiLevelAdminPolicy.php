<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Conference;
use App\Models\Zone;
use App\Models\Branch;

class MultiLevelAdminPolicy
{
    /**
     * Check if user can view conference statistics
     */
    public function viewConferenceStats(User $user, Conference $conference)
    {
        // Super admin can view all
        if ($user->hasRole('admin')) {
            return true;
        }

        // Conference admin can view their own conference
        if ($user->hasRole('conference_admin') && $user->admin_conference_id === $conference->id) {
            return true;
        }

        return false;
    }

    /**
     * Check if user can view zone statistics
     */
    public function viewZoneStats(User $user, Zone $zone)
    {
        // Super admin can view all
        if ($user->hasRole('admin')) {
            return true;
        }

        // Conference admin can view zones in their conference
        if ($user->hasRole('conference_admin') && $user->admin_conference_id === $zone->conference_id) {
            return true;
        }

        // Zone admin can view their own zone
        if ($user->hasRole('zone_admin') && $user->admin_zone_id === $zone->id) {
            return true;
        }

        return false;
    }

    /**
     * Check if user can view branch statistics
     */
    public function viewBranchStats(User $user, Branch $branch)
    {
        // Super admin can view all
        if ($user->hasRole('admin')) {
            return true;
        }

        // Conference admin can view branches in their conference
        if ($user->hasRole('conference_admin') && $user->admin_conference_id === $branch->zone->conference_id) {
            return true;
        }

        // Zone admin can view branches in their zone
        if ($user->hasRole('zone_admin') && $user->admin_zone_id === $branch->zone_id) {
            return true;
        }

        // Branch admin can view their own branch
        if ($user->hasRole('branch_admin') && $user->admin_branch_id === $branch->id) {
            return true;
        }

        return false;
    }

    /**
     * Check if user can manage zones
     */
    public function manageZones(User $user, Conference $conference = null)
    {
        // Super admin can manage all
        if ($user->hasRole('admin')) {
            return true;
        }

        // Conference admin can manage zones in their conference
        if ($user->hasRole('conference_admin') && $conference && $user->admin_conference_id === $conference->id) {
            return true;
        }

        return false;
    }

    /**
     * Check if user can manage branches
     */
    public function manageBranches(User $user, Zone $zone = null)
    {
        // Super admin can manage all
        if ($user->hasRole('admin')) {
            return true;
        }

        // Conference admin can manage branches in their conference
        if ($user->hasRole('conference_admin') && $zone && $user->admin_conference_id === $zone->conference_id) {
            return true;
        }

        // Zone admin can manage branches in their zone
        if ($user->hasRole('zone_admin') && $zone && $user->admin_zone_id === $zone->id) {
            return true;
        }

        return false;
    }

    /**
     * Check if user can view users at their level
     */
    public function viewLevelUsers(User $user, $level = null, $levelId = null)
    {
        // Super admin can view all
        if ($user->hasRole('admin')) {
            return true;
        }

        switch ($level) {
            case 'conference':
                return $user->hasRole('conference_admin') && $user->admin_conference_id === $levelId;
            
            case 'zone':
                if ($user->hasRole('conference_admin')) {
                    $zone = Zone::find($levelId);
                    return $zone && $user->admin_conference_id === $zone->conference_id;
                }
                return $user->hasRole('zone_admin') && $user->admin_zone_id === $levelId;
            
            case 'branch':
                if ($user->hasRole('conference_admin')) {
                    $branch = Branch::find($levelId);
                    return $branch && $user->admin_conference_id === $branch->zone->conference_id;
                }
                if ($user->hasRole('zone_admin')) {
                    $branch = Branch::find($levelId);
                    return $branch && $user->admin_zone_id === $branch->zone_id;
                }
                return $user->hasRole('branch_admin') && $user->admin_branch_id === $levelId;
        }

        return false;
    }

    /**
     * Check if user can export reports at their level
     */
    public function exportReports(User $user, $level = null, $levelId = null)
    {
        // Use the same logic as viewing stats
        switch ($level) {
            case 'conference':
                $conference = Conference::find($levelId);
                return $conference ? $this->viewConferenceStats($user, $conference) : false;
            
            case 'zone':
                $zone = Zone::find($levelId);
                return $zone ? $this->viewZoneStats($user, $zone) : false;
            
            case 'branch':
                $branch = Branch::find($levelId);
                return $branch ? $this->viewBranchStats($user, $branch) : false;
        }

        return false;
    }
}
