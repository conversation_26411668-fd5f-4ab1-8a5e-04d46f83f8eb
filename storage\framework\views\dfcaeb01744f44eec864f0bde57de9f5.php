<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ministers - <?php echo e($summit->title); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        .minister-card {
            transition: transform 0.3s;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            height: 100%;
        }
        .minister-card:hover {
            transform: translateY(-5px);
        }
        .minister-photo {
            height: 250px;
            object-fit: cover;
            border-radius: 10px 10px 0 0;
        }
        .role-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .role-badge {
            background: #667eea;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .social-links a {
            color: #667eea;
            font-size: 1.2em;
            margin-right: 10px;
            transition: color 0.3s;
        }
        .social-links a:hover {
            color: #764ba2;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<?php echo e(route('landing.index')); ?>">
                <i class="fas fa-mountain"></i> TUCASA STU Summit
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('landing.index')); ?>">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('landing.summit', $summit)); ?>">Summit Details</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('landing.activities', $summit)); ?>">Activities</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="<?php echo e(route('landing.ministers', $summit)); ?>">Ministers</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if(auth()->guard()->guest()): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('login')); ?>">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('register')); ?>">Register</a>
                    </li>
                    <?php else: ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <?php echo e(Auth::user()->name); ?>

                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?php echo e(route('logout')); ?>" 
                                   onclick="event.preventDefault(); document.getElementById('logout-form').submit();">Logout</a></li>
                        </ul>
                        <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" class="d-none">
                            <?php echo csrf_field(); ?>
                        </form>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="display-4 fw-bold">Summit Ministers</h1>
                    <p class="lead"><?php echo e($summit->title); ?> - <?php echo e($summit->year); ?></p>
                    <p class="mb-0">Meet the inspiring speakers and ministers who will be sharing God's word</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Ministers Section -->
    <section class="py-5">
        <div class="container">
            <?php if($ministers->count() > 0): ?>
                <div class="row">
                    <?php $__currentLoopData = $ministers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $minister): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card minister-card h-100">
                            <?php if($minister->image): ?>
                            <img src="<?php echo e(Storage::url($minister->image)); ?>"
                                 class="card-img-top minister-photo"
                                 alt="<?php echo e($minister->name); ?>"
                                 style="height: 250px; object-fit: cover;">
                            <?php else: ?>
                            <div class="card-img-top minister-photo bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                                <i class="fas fa-user fa-4x text-muted"></i>
                            </div>
                            <?php endif; ?>

                            <div class="card-body">
                                <h5 class="card-title"><?php echo e($minister->name); ?></h5>

                                <?php if($minister->title): ?>
                                <p class="text-muted mb-2"><?php echo e($minister->title); ?></p>
                                <?php endif; ?>

                                <?php if($minister->position): ?>
                                <p class="text-muted mb-2">
                                    <i class="fas fa-briefcase me-1"></i><?php echo e($minister->position); ?>

                                </p>
                                <?php endif; ?>

                                <?php if($minister->bio): ?>
                                <p class="card-text"><?php echo e(Str::limit($minister->bio, 150)); ?></p>
                                <?php endif; ?>

                                <?php if($minister->email || $minister->phone): ?>
                                <div class="border-top pt-3">
                                    <h6 class="text-muted mb-2">Contact Information</h6>
                                    <?php if($minister->email): ?>
                                    <p class="mb-1">
                                        <i class="fas fa-envelope me-2"></i>
                                        <a href="mailto:<?php echo e($minister->email); ?>" class="text-decoration-none">
                                            <?php echo e($minister->email); ?>

                                        </a>
                                    </p>
                                    <?php endif; ?>

                                    <?php if($minister->phone): ?>
                                    <p class="mb-1">
                                        <i class="fas fa-phone me-2"></i>
                                        <a href="tel:<?php echo e($minister->phone); ?>" class="text-decoration-none">
                                            <?php echo e($minister->phone); ?>

                                        </a>
                                    </p>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-users fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">No Ministers Listed</h4>
                <p class="text-muted">Ministers for this summit haven't been announced yet. Check back soon!</p>
                <a href="<?php echo e(route('landing.summit', $summit)); ?>" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Summit Details
                </a>
            </div>
            <?php endif; ?>
        </div>
    </section>

    <?php if($ministers->count() > 0): ?>
    <!-- Call to Action -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h3>Join Us for Inspiring Messages</h3>
                    <p class="lead text-muted">Don't miss the opportunity to hear from these amazing ministers!</p>
                    <?php if(auth()->guard()->check()): ?>
                        <?php
                            $userRegistration = Auth::user()->summitRegistrations()
                                ->where('summit_id', $summit->id)
                                ->whereIn('status', ['pending', 'confirmed'])
                                ->first();
                        ?>
                        <?php if(!$userRegistration): ?>
                        <a href="<?php echo e(route('registrations.create', $summit)); ?>" class="btn btn-primary btn-lg">
                            <i class="fas fa-user-plus me-2"></i>Register for Summit
                        </a>
                        <?php else: ?>
                        <div class="alert alert-success d-inline-block">
                            <i class="fas fa-check-circle me-2"></i>You're already registered for this summit!
                            <a href="<?php echo e(route('registrations.show', $userRegistration)); ?>" class="btn btn-sm btn-outline-success ms-2">
                                View Registration
                            </a>
                        </div>
                        <?php endif; ?>
                    <?php else: ?>
                    <a href="<?php echo e(route('register')); ?>" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-user-plus me-2"></i>Register for Summit
                    </a>
                    <a href="<?php echo e(route('login')); ?>" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>Login
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>TUCASA STU Summit</h5>
                    <p class="text-muted">Empowering students through faith and fellowship.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted">&copy; <?php echo e(date('Y')); ?> TUCASA STU. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
<?php /**PATH C:\wamp64\www\summit-management-system\resources\views/landing/ministers.blade.php ENDPATH**/ ?>