@extends('layouts.user')

@section('title', 'Outstanding Payments')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">⏰ Outstanding Payments</h2>
                    <p class="text-muted mb-0">Pending payments and collection follow-ups</p>
                </div>
                <div>
                    <a href="{{ route('treasurer.dashboard') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back
                    </a>
                    <button class="btn btn-warning" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>Print Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    @if(isset($summary))
    <div class="row mb-4">
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <h6>Total Outstanding</h6>
                    <h3>{{ number_format($summary['total_outstanding'] ?? 0, 0) }} TZS</h3>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <h6>Pending Registrations</h6>
                    <h3>{{ $summary['count'] ?? 0 }}</h3>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h6>Average Outstanding</h6>
                    <h3>{{ $summary['count'] > 0 ? number_format(($summary['total_outstanding'] ?? 0) / $summary['count'], 0) : 0 }} TZS</h3>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Outstanding by Summit -->
    @if(isset($summary['by_summit']) && count($summary['by_summit']) > 0)
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Outstanding by Summit</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($summary['by_summit'] as $summitTitle => $summitData)
                        <div class="col-md-4 mb-3">
                            <div class="card border-warning">
                                <div class="card-body">
                                    <h6 class="card-title">{{ $summitTitle }}</h6>
                                    <p class="card-text">
                                        <strong>{{ $summitData['count'] }}</strong> pending registrations<br>
                                        <strong>{{ number_format($summitData['amount'], 0) }} TZS</strong> outstanding
                                    </p>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Outstanding Registrations Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Outstanding Registrations</h5>
                </div>
                <div class="card-body">
                    @if(isset($outstandingRegistrations) && $outstandingRegistrations->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Registration Date</th>
                                        <th>Participant</th>
                                        <th>Branch</th>
                                        <th>Summit</th>
                                        <th>Outstanding Amount</th>
                                        <th>Days Pending</th>
                                        <th>Priority</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($outstandingRegistrations as $registration)
                                    @php
                                        $daysPending = (int) $registration->registered_at->diffInDays(now());
                                        $priority = $daysPending > 30 ? 'high' : ($daysPending > 14 ? 'medium' : 'low');
                                    @endphp
                                    <tr>
                                        <td>{{ $registration->registered_at->format('M j, Y') }}</td>
                                        <td>
                                            <strong>{{ $registration->user->full_name }}</strong><br>
                                            <small class="text-muted">{{ $registration->user->email }}</small><br>
                                            <small class="text-muted">{{ $registration->user->phone ?? 'No phone' }}</small>
                                        </td>
                                        <td>
                                            {{ $registration->user->branch->name ?? 'N/A' }}<br>
                                            <small class="text-muted">{{ $registration->user->branch->zone->conference->name ?? 'N/A' }}</small>
                                        </td>
                                        <td>
                                            <strong>{{ $registration->summit->title }}</strong><br>
                                            <small class="text-muted">{{ $registration->summit->year }}</small>
                                        </td>
                                        <td>
                                            <strong class="text-danger">{{ number_format($registration->balance, 0) }} TZS</strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $priority === 'high' ? 'danger' : ($priority === 'medium' ? 'warning' : 'secondary') }}">
                                                {{ $daysPending }} days
                                            </span>
                                        </td>
                                        <td>
                                            @if($priority === 'high')
                                                <span class="badge bg-danger">High</span>
                                            @elseif($priority === 'medium')
                                                <span class="badge bg-warning">Medium</span>
                                            @else
                                                <span class="badge bg-secondary">Low</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="mailto:{{ $registration->user->email }}?subject=Payment Reminder - {{ $registration->summit->title }}&body=Dear {{ $registration->user->full_name }}, This is a reminder about your pending payment of {{ number_format($registration->balance, 0) }} TZS for {{ $registration->summit->title }}." 
                                                   class="btn btn-outline-primary" title="Send Email">
                                                    <i class="fas fa-envelope"></i>
                                                </a>
                                                @if($registration->user->phone)
                                                <a href="tel:{{ $registration->user->phone }}" 
                                                   class="btn btn-outline-success" title="Call">
                                                    <i class="fas fa-phone"></i>
                                                </a>
                                                @endif
                                                <a href="{{ route('admin.registrations.show', $registration) }}" 
                                                   class="btn btn-outline-info" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h5 class="text-success">All Payments Up to Date!</h5>
                            <p class="text-muted">No outstanding payments found.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Follow-up Actions -->
    @if(isset($outstandingRegistrations) && $outstandingRegistrations->count() > 0)
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Recommended Follow-up Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>High Priority (30+ days)</h6>
                                <p class="mb-0">
                                    {{ $outstandingRegistrations->filter(function($r) { return (int) $r->registered_at->diffInDays(now()) > 30; })->count() }} registrations
                                    require immediate follow-up
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-clock me-2"></i>Medium Priority (14-30 days)</h6>
                                <p class="mb-0">
                                    {{ $outstandingRegistrations->filter(function($r) { $days = (int) $r->registered_at->diffInDays(now()); return $days >= 14 && $days <= 30; })->count() }} registrations
                                    need reminder emails
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Low Priority (< 14 days)</h6>
                                <p class="mb-0">
                                    {{ $outstandingRegistrations->filter(function($r) { return (int) $r->registered_at->diffInDays(now()) < 14; })->count() }} registrations
                                    within normal timeframe
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection

@push('styles')
<style>
    @media print {
        .btn, .card-header {
            display: none !important;
        }
        .card {
            border: none !important;
            box-shadow: none !important;
        }
    }
</style>
@endpush
