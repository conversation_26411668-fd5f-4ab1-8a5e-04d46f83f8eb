@extends('layouts.admin')

@section('title', 'Edit Conference')
@section('page-title', 'Edit Conference')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">Edit Conference</h2>
                <p class="text-muted">Update conference information.</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('admin.conferences.show', $conference) }}" class="btn btn-outline-info">
                    <i class="fas fa-eye me-2"></i>View Details
                </a>
                <a href="{{ route('admin.conferences.index') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Conferences
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-globe me-2"></i>Conference Information</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.conferences.update', $conference) }}">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="name" class="form-label">Conference Name <span class="text-danger">*</span></label>
                                <input type="text" name="name" id="name" class="form-control @error('name') is-invalid @enderror" 
                                       value="{{ old('name', $conference->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="code" class="form-label">Conference Code <span class="text-danger">*</span></label>
                                <input type="text" name="code" id="code" class="form-control @error('code') is-invalid @enderror" 
                                       value="{{ old('code', $conference->code) }}" placeholder="e.g., EAC" required>
                                @error('code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="region" class="form-label">Region</label>
                        <input type="text" name="region" id="region" class="form-control @error('region') is-invalid @enderror" 
                               value="{{ old('region', $conference->region) }}" placeholder="e.g., East Africa">
                        @error('region')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea name="description" id="description" class="form-control @error('description') is-invalid @enderror" 
                                  rows="4" placeholder="Brief description of the conference...">{{ old('description', $conference->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" name="is_active" id="is_active" class="form-check-input" 
                                   value="1" {{ old('is_active', $conference->is_active) ? 'checked' : '' }}>
                            <label for="is_active" class="form-check-label">
                                Active Conference
                            </label>
                        </div>
                        <small class="text-muted">Inactive conferences won't be available for selection.</small>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Conference
                        </button>
                        <a href="{{ route('admin.conferences.show', $conference) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Conference Stats</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Total Zones</span>
                    <span class="badge bg-info">{{ $conference->zones()->count() }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Total Branches</span>
                    <span class="badge bg-success">{{ $conference->zones()->withCount('branches')->get()->sum('branches_count') }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Created</span>
                    <span class="text-muted">{{ $conference->created_at->format('M j, Y') }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Last Updated</span>
                    <span class="text-muted">{{ $conference->updated_at->format('M j, Y') }}</span>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
