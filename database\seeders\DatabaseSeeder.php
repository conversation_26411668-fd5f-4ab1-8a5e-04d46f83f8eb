<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            RolePermissionSeeder::class,
            OrganizationSeeder::class,
        ]);

        // Create default admin user
        $admin = User::factory()->create([
            'name' => 'System Administrator',
            'first_name' => 'System',
            'last_name' => 'Administrator',
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);
        $admin->assignRole('admin');

        // Create default treasurer user
        $treasurer = User::factory()->create([
            'name' => 'System Treasurer',
            'first_name' => 'System',
            'last_name' => 'Treasurer',
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);
        $treasurer->assignRole('treasurer');
    }
}
