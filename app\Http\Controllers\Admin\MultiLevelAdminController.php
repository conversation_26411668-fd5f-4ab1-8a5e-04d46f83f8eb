<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Conference;
use App\Models\Zone;
use App\Models\Branch;
use App\Models\Payment;
use App\Models\SummitRegistration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MultiLevelAdminController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin|conference_admin|zone_admin|branch_admin']);
    }

    /**
     * Conference Admin Dashboard
     */
    public function conferenceDashboard(Conference $conference)
    {
        $this->authorize('viewConferenceStats', $conference);

        $stats = $this->getConferenceStats($conference);
        $zones = $conference->zones()->withCount(['branches', 'users'])->get();
        $recentRegistrations = $this->getConferenceRegistrations($conference, 10);
        $paymentStats = $this->getConferencePaymentStats($conference);

        return view('admin.multi-level.conference-dashboard', compact(
            'conference', 'stats', 'zones', 'recentRegistrations', 'paymentStats'
        ));
    }

    /**
     * Zone Admin Dashboard
     */
    public function zoneDashboard(Zone $zone)
    {
        $this->authorize('viewZoneStats', $zone);

        $stats = $this->getZoneStats($zone);
        $branches = $zone->branches()->withCount('users')->get();
        $recentRegistrations = $this->getZoneRegistrations($zone, 10);
        $paymentStats = $this->getZonePaymentStats($zone);

        return view('admin.multi-level.zone-dashboard', compact(
            'zone', 'stats', 'branches', 'recentRegistrations', 'paymentStats'
        ));
    }

    /**
     * Branch Admin Dashboard
     */
    public function branchDashboard(Branch $branch)
    {
        $this->authorize('viewBranchStats', $branch);

        $stats = $this->getBranchStats($branch);
        $users = $branch->users()->with('summitRegistrations')->paginate(15);
        $recentRegistrations = $this->getBranchRegistrations($branch, 10);
        $paymentStats = $this->getBranchPaymentStats($branch);

        return view('admin.multi-level.branch-dashboard', compact(
            'branch', 'stats', 'users', 'recentRegistrations', 'paymentStats'
        ));
    }

    /**
     * Get Conference Statistics
     */
    private function getConferenceStats(Conference $conference)
    {
        $zoneIds = $conference->zones()->pluck('id');
        $branchIds = Branch::whereIn('zone_id', $zoneIds)->pluck('id');
        $userIds = User::whereIn('branch_id', $branchIds)->pluck('id');

        return [
            'total_zones' => $conference->zones()->count(),
            'total_branches' => Branch::whereIn('zone_id', $zoneIds)->count(),
            'total_users' => User::whereIn('branch_id', $branchIds)->count(),
            'total_registrations' => SummitRegistration::whereIn('user_id', $userIds)->count(),
            'completed_payments' => Payment::whereIn('user_id', $userIds)->where('status', 'completed')->count(),
            'total_revenue' => Payment::whereIn('user_id', $userIds)->where('status', 'completed')->sum('amount'),
            'pending_payments' => Payment::whereIn('user_id', $userIds)->where('status', 'pending')->count(),
        ];
    }

    /**
     * Get Zone Statistics
     */
    private function getZoneStats(Zone $zone)
    {
        $branchIds = $zone->branches()->pluck('id');
        $userIds = User::whereIn('branch_id', $branchIds)->pluck('id');

        return [
            'total_branches' => $zone->branches()->count(),
            'total_users' => User::whereIn('branch_id', $branchIds)->count(),
            'total_registrations' => SummitRegistration::whereIn('user_id', $userIds)->count(),
            'completed_payments' => Payment::whereIn('user_id', $userIds)->where('status', 'completed')->count(),
            'total_revenue' => Payment::whereIn('user_id', $userIds)->where('status', 'completed')->sum('amount'),
            'pending_payments' => Payment::whereIn('user_id', $userIds)->where('status', 'pending')->count(),
        ];
    }

    /**
     * Get Branch Statistics
     */
    private function getBranchStats(Branch $branch)
    {
        $userIds = $branch->users()->pluck('id');

        return [
            'total_users' => $branch->users()->count(),
            'total_registrations' => SummitRegistration::whereIn('user_id', $userIds)->count(),
            'completed_payments' => Payment::whereIn('user_id', $userIds)->where('status', 'completed')->count(),
            'total_revenue' => Payment::whereIn('user_id', $userIds)->where('status', 'completed')->sum('amount'),
            'pending_payments' => Payment::whereIn('user_id', $userIds)->where('status', 'pending')->count(),
            'active_users' => $branch->users()->where('is_active', true)->count(),
        ];
    }

    /**
     * Get Conference Registrations
     */
    private function getConferenceRegistrations(Conference $conference, $limit = 10)
    {
        $zoneIds = $conference->zones()->pluck('id');
        $branchIds = Branch::whereIn('zone_id', $zoneIds)->pluck('id');
        $userIds = User::whereIn('branch_id', $branchIds)->pluck('id');

        return SummitRegistration::whereIn('user_id', $userIds)
            ->with(['user.branch.zone', 'summit'])
            ->latest()
            ->limit($limit)
            ->get();
    }

    /**
     * Get Zone Registrations
     */
    private function getZoneRegistrations(Zone $zone, $limit = 10)
    {
        $branchIds = $zone->branches()->pluck('id');
        $userIds = User::whereIn('branch_id', $branchIds)->pluck('id');

        return SummitRegistration::whereIn('user_id', $userIds)
            ->with(['user.branch', 'summit'])
            ->latest()
            ->limit($limit)
            ->get();
    }

    /**
     * Get Branch Registrations
     */
    private function getBranchRegistrations(Branch $branch, $limit = 10)
    {
        $userIds = $branch->users()->pluck('id');

        return SummitRegistration::whereIn('user_id', $userIds)
            ->with(['user', 'summit'])
            ->latest()
            ->limit($limit)
            ->get();
    }

    /**
     * Get Conference Payment Statistics
     */
    private function getConferencePaymentStats(Conference $conference)
    {
        $zoneIds = $conference->zones()->pluck('id');
        $branchIds = Branch::whereIn('zone_id', $zoneIds)->pluck('id');
        $userIds = User::whereIn('branch_id', $branchIds)->pluck('id');

        return Payment::whereIn('user_id', $userIds)
            ->select('status', DB::raw('count(*) as count'), DB::raw('sum(amount) as total'))
            ->groupBy('status')
            ->get()
            ->keyBy('status');
    }

    /**
     * Get Zone Payment Statistics
     */
    private function getZonePaymentStats(Zone $zone)
    {
        $branchIds = $zone->branches()->pluck('id');
        $userIds = User::whereIn('branch_id', $branchIds)->pluck('id');

        return Payment::whereIn('user_id', $userIds)
            ->select('status', DB::raw('count(*) as count'), DB::raw('sum(amount) as total'))
            ->groupBy('status')
            ->get()
            ->keyBy('status');
    }

    /**
     * Get Branch Payment Statistics
     */
    private function getBranchPaymentStats(Branch $branch)
    {
        $userIds = $branch->users()->pluck('id');

        return Payment::whereIn('user_id', $userIds)
            ->select('status', DB::raw('count(*) as count'), DB::raw('sum(amount) as total'))
            ->groupBy('status')
            ->get()
            ->keyBy('status');
    }

    /**
     * Export Conference Report
     */
    public function exportConferenceReport(Conference $conference)
    {
        $this->authorize('viewConferenceStats', $conference);
        
        // Implementation for exporting conference report
        // This would generate PDF/Excel reports for the conference
    }

    /**
     * Export Zone Report
     */
    public function exportZoneReport(Zone $zone)
    {
        $this->authorize('viewZoneStats', $zone);
        
        // Implementation for exporting zone report
    }

    /**
     * Export Branch Report
     */
    public function exportBranchReport(Branch $branch)
    {
        $this->authorize('viewBranchStats', $branch);
        
        // Implementation for exporting branch report
    }
}
