<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use <PERSON>tie\Permission\Models\Permission;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles if they don't exist
        $roles = [
            'admin' => 'Super Administrator',
            'user' => 'Regular User',
            'treasurer' => 'Treasurer',
            'conference_admin' => 'Conference Administrator',
            'zone_admin' => 'Zone Administrator',
            'branch_admin' => 'Branch Administrator',
        ];

        foreach ($roles as $roleName => $description) {
            Role::firstOrCreate(
                ['name' => $roleName],
                ['guard_name' => 'web']
            );
        }

        // Create permissions if they don't exist
        $permissions = [
            // User management
            'view users',
            'create users',
            'edit users',
            'delete users',
            'manage user roles',
            
            // Registration management
            'view registrations',
            'create registrations',
            'edit registrations',
            'delete registrations',
            'approve registrations',
            
            // Payment management
            'view payments',
            'create payments',
            'edit payments',
            'delete payments',
            'process payments',
            
            // Report management
            'view reports',
            'export reports',
            'view financial reports',
            'view analytics',
            
            // Summit management
            'view summits',
            'create summits',
            'edit summits',
            'delete summits',
            'manage summit activities',
            
            // Organization management
            'view organizations',
            'create organizations',
            'edit organizations',
            'delete organizations',
            
            // Content management
            'view content',
            'create content',
            'edit content',
            'delete content',
            
            // ID Card management
            'view id cards',
            'generate id cards',
            'download id cards',
            
            // Meal tracking
            'view meal tracking',
            'scan qr codes',
            'manage meal tracking',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission],
                ['guard_name' => 'web']
            );
        }

        // Assign permissions to roles
        $adminRole = Role::where('name', 'admin')->first();
        $treasurerRole = Role::where('name', 'treasurer')->first();
        $conferenceAdminRole = Role::where('name', 'conference_admin')->first();
        $zoneAdminRole = Role::where('name', 'zone_admin')->first();
        $branchAdminRole = Role::where('name', 'branch_admin')->first();
        $userRole = Role::where('name', 'user')->first();

        // Admin gets all permissions
        if ($adminRole) {
            $adminRole->syncPermissions(Permission::all());
        }

        // Treasurer permissions
        if ($treasurerRole) {
            $treasurerRole->syncPermissions([
                'view payments',
                'edit payments',
                'process payments',
                'view reports',
                'export reports',
                'view financial reports',
                'view analytics',
            ]);
        }

        // Conference Admin permissions
        if ($conferenceAdminRole) {
            $conferenceAdminRole->syncPermissions([
                'view users',
                'edit users',
                'view registrations',
                'edit registrations',
                'approve registrations',
                'view payments',
                'view reports',
                'export reports',
                'view analytics',
                'view summits',
                'edit summits',
                'manage summit activities',
                'view id cards',
                'generate id cards',
                'download id cards',
                'view meal tracking',
                'manage meal tracking',
            ]);
        }

        // Zone Admin permissions
        if ($zoneAdminRole) {
            $zoneAdminRole->syncPermissions([
                'view users',
                'edit users',
                'view registrations',
                'edit registrations',
                'view payments',
                'view reports',
                'export reports',
                'view id cards',
                'generate id cards',
                'download id cards',
                'view meal tracking',
            ]);
        }

        // Branch Admin permissions
        if ($branchAdminRole) {
            $branchAdminRole->syncPermissions([
                'view users',
                'view registrations',
                'edit registrations',
                'view payments',
                'view reports',
                'view id cards',
                'generate id cards',
                'download id cards',
            ]);
        }

        // User permissions (basic)
        if ($userRole) {
            $userRole->syncPermissions([
                'view registrations',
                'create registrations',
                'view payments',
                'create payments',
                'view id cards',
            ]);
        }

        $this->command->info('Roles and permissions seeded successfully!');
    }
}
