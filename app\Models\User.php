<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'first_name',
        'last_name',
        'email',
        'phone',
        'date_of_birth',
        'gender',
        'profile_image',
        'branch_id',
        'university_id',
        'student_id',
        'year_of_study',
        'course',
        'address',
        'emergency_contact_name',
        'emergency_contact_phone',
        'is_active',
        'password',
        'admin_conference_id',
        'admin_zone_id',
        'admin_branch_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'date_of_birth' => 'date',
            'is_active' => 'boolean',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the branch that owns the user.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the university that owns the user.
     */
    public function university(): BelongsTo
    {
        return $this->belongsTo(University::class);
    }

    /**
     * Get the conference this user administers.
     */
    public function adminConference(): BelongsTo
    {
        return $this->belongsTo(Conference::class, 'admin_conference_id');
    }

    /**
     * Get the zone this user administers.
     */
    public function adminZone(): BelongsTo
    {
        return $this->belongsTo(Zone::class, 'admin_zone_id');
    }

    /**
     * Get the branch this user administers.
     */
    public function adminBranch(): BelongsTo
    {
        return $this->belongsTo(Branch::class, 'admin_branch_id');
    }

    /**
     * Get the summit registrations for the user.
     */
    public function summitRegistrations(): HasMany
    {
        return $this->hasMany(SummitRegistration::class);
    }

    /**
     * Get the payments for the user.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the ID cards for the user.
     */
    public function idCards(): HasMany
    {
        return $this->hasMany(IdCard::class);
    }

    /**
     * Get the meal trackings for the user.
     */
    public function mealTrackings(): HasMany
    {
        return $this->hasMany(MealTracking::class);
    }

    /**
     * Get the gate entries for the user.
     */
    public function gateEntries(): HasMany
    {
        return $this->hasMany(GateEntry::class);
    }

    /**
     * Get the user's full name.
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }
}
