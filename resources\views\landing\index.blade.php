<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TUCASA STU Summit Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
        }
        .countdown-box {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 10px;
            text-align: center;
        }
        .minister-card {
            transition: transform 0.3s;
        }
        .minister-card:hover {
            transform: translateY(-5px);
        }
        .gallery-img {
            height: 200px;
            object-fit: cover;
            border-radius: 10px;
            transition: transform 0.3s;
            cursor: pointer;
        }
        .gallery-img:hover {
            transform: scale(1.05);
        }
        .gallery-section {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ route('landing.index') }}">
                <i class="fas fa-mountain"></i> TUCASA STU Summit
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('landing.index') }}">Home</a>
                    </li>
                    @if($currentSummit)
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('landing.summit', $currentSummit) }}">Summit Details</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('landing.activities', $currentSummit) }}">Activities</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('landing.ministers', $currentSummit) }}">Ministers</a>
                    </li>
                    @endif
                </ul>
                <ul class="navbar-nav">
                    @guest
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('login') }}">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('register') }}">Register</a>
                    </li>
                    @else
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            {{ Auth::user()->name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ route('profile.show') }}">Profile</a></li>
                            @if(Auth::user()->hasRole('admin'))
                            <li><a class="dropdown-item" href="{{ route('admin.dashboard') }}">Admin Dashboard</a></li>
                            @endif
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ route('logout') }}"
                                   onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                    Logout
                                </a>
                                <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                    @csrf
                                </form>
                            </li>
                        </ul>
                    </li>
                    @endguest
                </ul>
            </div>
        </div>
    </nav>

    @if($currentSummit)
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-4">{{ $currentSummit->title }}</h1>
            <p class="lead mb-4">{{ $currentSummit->theme }}</p>
            <p class="mb-4">
                <i class="fas fa-calendar"></i> {{ $currentSummit->start_date->format('F j') }} - {{ $currentSummit->end_date->format('F j, Y') }} |
                <i class="fas fa-map-marker-alt"></i> {{ $currentSummit->venue }}
            </p>
            
            @if($daysUntilSummit !== null)
                @if($daysUntilSummit > 0)
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <h3 class="mb-4">Countdown to Summit</h3>
                        <div class="row">
                            <div class="col-3">
                                <div class="countdown-box">
                                    <h2>{{ $daysUntilSummit }}</h2>
                                    <p>Days</p>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="countdown-box">
                                    <h2>{{ now()->diffInHours($currentSummit->start_date) % 24 }}</h2>
                                    <p>Hours</p>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="countdown-box">
                                    <h2>{{ now()->diffInMinutes($currentSummit->start_date) % 60 }}</h2>
                                    <p>Minutes</p>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="countdown-box">
                                    <h2>{{ now()->diffInSeconds($currentSummit->start_date) % 60 }}</h2>
                                    <p>Seconds</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @elseif($daysUntilSummit === 0)
                <div class="alert alert-success">
                    <h3><i class="fas fa-star"></i> Summit is happening today!</h3>
                </div>
                @else
                <div class="alert alert-info">
                    <h3><i class="fas fa-check-circle"></i> Summit has concluded</h3>
                </div>
                @endif
            @endif
            
            @auth
            <div class="mt-4">
                @if($currentSummit)
                    @php
                        $userRegistration = Auth::user()->summitRegistrations()
                            ->where('summit_id', $currentSummit->id)
                            ->whereIn('status', ['pending', 'confirmed'])
                            ->first();
                    @endphp
                    @if(!$userRegistration)
                        <a href="{{ route('registrations.create', $currentSummit) }}" class="btn btn-light btn-lg me-3">
                            <i class="fas fa-user-plus"></i> Register for Summit
                        </a>
                    @else
                        <a href="{{ route('registrations.show', $userRegistration) }}" class="btn btn-success btn-lg me-3">
                            <i class="fas fa-check-circle"></i> View Registration
                        </a>
                    @endif
                @else
                    <a href="{{ route('dashboard') }}" class="btn btn-light btn-lg me-3">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                @endif
                <a href="{{ route('profile.show') }}" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-user"></i> My Profile
                </a>
            </div>
            @else
            <div class="mt-4">
                <a href="{{ route('register') }}" class="btn btn-light btn-lg me-3">
                    <i class="fas fa-user-plus"></i> Join Us
                </a>
                <a href="{{ route('login') }}" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-sign-in-alt"></i> Login
                </a>
            </div>
            @endauth
        </div>
    </section>

    <!-- Summit Information -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <h2 class="mb-4">About This Summit</h2>
                    <p class="lead">{{ $currentSummit->description }}</p>
                    
                    <div class="row mt-5">
                        <div class="col-md-6">
                            <h4><i class="fas fa-dollar-sign text-primary"></i> Registration Fee</h4>
                            <p class="h5 text-success">TSh {{ number_format($currentSummit->registration_fee) }}</p>
                        </div>
                        <div class="col-md-6">
                            <h4><i class="fas fa-clock text-primary"></i> Registration Deadline</h4>
                            <p class="h5 text-warning">{{ $currentSummit->registration_deadline->format('F j, Y') }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    @if($currentSummit->poster_image)
                    <img src="{{ Storage::url($currentSummit->poster_image) }}" 
                         alt="Summit Poster" 
                         class="img-fluid rounded shadow">
                    @endif
                </div>
            </div>
        </div>
    </section>

    <!-- Activities Section -->
    @if($activities && $activities->count() > 0)
    <section class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">Featured Activities</h2>
            <div class="row">
                @foreach($activities->take(3) as $activity)
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        @if($activity->image)
                        <img src="{{ Storage::url($activity->image) }}" class="card-img-top" alt="{{ $activity->title }}" style="height: 200px; object-fit: cover;">
                        @else
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            <div class="text-center">
                                <i class="fas fa-calendar-alt fa-3x text-muted mb-2"></i>
                                <p class="text-muted mb-0 small">{{ Str::limit($activity->title, 30) }}</p>
                            </div>
                        </div>
                        @endif
                        <div class="card-body">
                            <h5 class="card-title">{{ $activity->title }}</h5>
                            <p class="card-text">{{ Str::limit($activity->description, 100) }}</p>
                            <div class="text-muted">
                                @if($activity->date)
                                <small>
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ $activity->date->format('M j, Y') }}
                                </small>
                                <br>
                                @endif
                                @if($activity->time)
                                <small>
                                    <i class="fas fa-clock me-1"></i>
                                    {{ $activity->time->format('g:i A') }}
                                </small>
                                <br>
                                @endif
                                @if($activity->location)
                                <small>
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    {{ $activity->location }}
                                </small>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            @if($currentSummit)
            <div class="text-center mt-4">
                <a href="{{ route('landing.activities', $currentSummit) }}" class="btn btn-primary">
                    View All Activities <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            @endif
        </div>
    </section>
    @endif

    <!-- Ministers Section -->
    @if($ministers && $ministers->count() > 0)
    <section class="py-5 bg-light">
        <div class="container">
            <h2 class="text-center mb-5">Featured Ministers</h2>
            <div class="row">
                @foreach($ministers->take(3) as $minister)
                <div class="col-md-4 mb-4">
                    <div class="card minister-card h-100">
                        @if($minister->image)
                        <img src="{{ Storage::url($minister->image) }}" class="card-img-top" alt="{{ $minister->name }}" style="height: 250px; object-fit: cover;">
                        @else
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                            <div class="text-center">
                                <i class="fas fa-user fa-4x text-muted mb-2"></i>
                                <p class="text-muted mb-0">{{ $minister->name }}</p>
                            </div>
                        </div>
                        @endif
                        <div class="card-body">
                            <h5 class="card-title">{{ $minister->name }}</h5>
                            <p class="card-text text-muted">{{ $minister->title }}</p>
                            @if($minister->position)
                            <p class="card-text"><small class="text-muted">{{ $minister->position }}</small></p>
                            @endif
                            @if($minister->bio)
                            <p class="card-text">{{ Str::limit($minister->bio, 100) }}</p>
                            @endif
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            @if($currentSummit)
            <div class="text-center mt-4">
                <a href="{{ route('landing.ministers', $currentSummit) }}" class="btn btn-primary">
                    View All Ministers <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            @endif
        </div>
    </section>
    @endif

    <!-- Gallery Section -->
    @if($currentSummit && $currentSummit->gallery_images && count($currentSummit->gallery_images) > 0)
    <section class="py-5 gallery-section">
        <div class="container">
            <h2 class="text-center mb-5">Summit Gallery</h2>
            <div class="row">
                @foreach(array_slice($currentSummit->gallery_images, 0, 6) as $image)
                <div class="col-md-4 col-sm-6 mb-4">
                    <img src="{{ Storage::url($image) }}"
                         alt="Summit Gallery"
                         class="img-fluid gallery-img w-100"
                         data-bs-toggle="modal"
                         data-bs-target="#galleryModal"
                         data-bs-image="{{ Storage::url($image) }}">
                </div>
                @endforeach
            </div>
            @if(count($currentSummit->gallery_images) > 6)
            <div class="text-center mt-4">
                <a href="{{ route('landing.summit', $currentSummit) }}" class="btn btn-primary">
                    View All Gallery Images <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            @endif
        </div>
    </section>
    @endif

    @else
    <!-- No Current Summit -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-4">TUCASA STU Summit Management System</h1>
            <p class="lead mb-4">Stay tuned for upcoming summit announcements</p>
            <div class="mt-4">
                @auth
                <a href="{{ route('profile.show') }}" class="btn btn-light btn-lg">
                    <i class="fas fa-user"></i> My Profile
                </a>
                @else
                <a href="{{ route('register') }}" class="btn btn-light btn-lg me-3">
                    <i class="fas fa-user-plus"></i> Join Us
                </a>
                <a href="{{ route('login') }}" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-sign-in-alt"></i> Login
                </a>
                @endauth
            </div>
        </div>
    </section>
    @endif

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p>&copy; {{ date('Y') }} TUCASA STU Summit Management System. All rights reserved.</p>
        </div>
    </footer>

    <!-- Gallery Modal -->
    <div class="modal fade" id="galleryModal" tabindex="-1" aria-labelledby="galleryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="galleryModalLabel">Summit Gallery</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="Gallery Image" class="img-fluid">
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Gallery modal functionality
        document.addEventListener('DOMContentLoaded', function() {
            const galleryModal = document.getElementById('galleryModal');
            const modalImage = document.getElementById('modalImage');

            if (galleryModal) {
                galleryModal.addEventListener('show.bs.modal', function (event) {
                    const button = event.relatedTarget;
                    const imageSrc = button.getAttribute('data-bs-image');
                    modalImage.src = imageSrc;
                });
            }
        });
    </script>
</body>
</html>
