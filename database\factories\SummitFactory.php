<?php

namespace Database\Factories;

use App\Models\Summit;
use Illuminate\Database\Eloquent\Factories\Factory;

class SummitFactory extends Factory
{
    protected $model = Summit::class;

    public function definition(): array
    {
        $startDate = $this->faker->dateTimeBetween('now', '+6 months');
        $endDate = $this->faker->dateTimeBetween($startDate, $startDate->format('Y-m-d') . ' +7 days');

        return [
            'title' => $this->faker->sentence(3),
            'theme' => $this->faker->sentence(4),
            'description' => $this->faker->paragraph(),
            'venue' => $this->faker->address(),
            'start_date' => $startDate,
            'end_date' => $endDate,
            'year' => $startDate->format('Y'),
            'registration_fee' => $this->faker->numberBetween(50000, 200000), // TZS
            'early_bird_fee' => $this->faker->numberBetween(30000, 150000), // TZS
            'early_bird_deadline' => $this->faker->dateTimeBetween('now', $startDate),
            'max_participants' => $this->faker->numberBetween(100, 1000),
            'is_active' => true,
            'is_registration_open' => true,
        ];
    }

    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    public function registrationClosed(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_registration_open' => false,
        ]);
    }
}
