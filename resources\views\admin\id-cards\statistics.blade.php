@extends('layouts.admin')

@section('title', 'ID Card Statistics')
@section('page-title', 'ID Card Statistics')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">ID Card Statistics</h2>
                <p class="text-muted">Detailed analytics and insights for ID card management.</p>
            </div>
            <a href="{{ route('admin.id-cards.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to ID Cards
            </a>
        </div>
    </div>
</div>

<!-- Overview Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="modern-stats-card blue h-100">
            <div class="stat-icon">
                <i class="fas fa-id-card"></i>
            </div>
            <h3 class="stat-number">{{ number_format($statistics['total_cards']) }}</h3>
            <p class="stat-label">Total Cards</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="modern-stats-card green h-100">
            <div class="stat-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h3 class="stat-number">{{ number_format($statistics['active_cards']) }}</h3>
            <p class="stat-label">Active Cards</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="modern-stats-card orange h-100">
            <div class="stat-icon">
                <i class="fas fa-clock"></i>
            </div>
            <h3 class="stat-number">{{ number_format($statistics['expired_cards']) }}</h3>
            <p class="stat-label">Expired Cards</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="modern-stats-card purple h-100">
            <div class="stat-icon">
                <i class="fas fa-calendar"></i>
            </div>
            <h3 class="stat-number">{{ number_format($statistics['cards_this_month']) }}</h3>
            <p class="stat-label">This Month</p>
        </div>
    </div>
</div>

<!-- Monthly Trends -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Monthly Card Generation Trends</h5>
            </div>
            <div class="card-body">
                <canvas id="monthlyChart" height="100"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Card Status Distribution</h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Summit Breakdown -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-mountain me-2"></i>Cards by Summit</h5>
            </div>
            <div class="card-body">
                @if($cardsBySummit->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Summit</th>
                                <th>Total Cards</th>
                                <th>Active Cards</th>
                                <th>Expired Cards</th>
                                <th>Percentage</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($cardsBySummit as $summit)
                            <tr>
                                <td>
                                    <div>
                                        <div class="fw-bold">{{ $summit->title }}</div>
                                        <small class="text-muted">{{ $summit->year }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ $summit->count }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-success">{{ $summit->active_count ?? 0 }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-warning">{{ $summit->expired_count ?? 0 }}</span>
                                </td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar" 
                                             style="width: {{ $statistics['total_cards'] > 0 ? ($summit->count / $statistics['total_cards']) * 100 : 0 }}%">
                                            {{ $statistics['total_cards'] > 0 ? round(($summit->count / $statistics['total_cards']) * 100, 1) : 0 }}%
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="text-center py-4">
                    <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Data Available</h5>
                    <p class="text-muted">No ID cards have been generated yet.</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Monthly Trends Chart
const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
const monthlyData = @json($cardsByMonth);
const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
const monthlyValues = months.map((month, index) => monthlyData[index + 1] || 0);

new Chart(monthlyCtx, {
    type: 'line',
    data: {
        labels: months,
        datasets: [{
            label: 'Cards Generated',
            data: monthlyValues,
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Status Distribution Chart
const statusCtx = document.getElementById('statusChart').getContext('2d');
new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: ['Active', 'Expired', 'Inactive'],
        datasets: [{
            data: [
                {{ $statistics['active_cards'] }},
                {{ $statistics['expired_cards'] }},
                {{ $statistics['total_cards'] - $statistics['active_cards'] - $statistics['expired_cards'] }}
            ],
            backgroundColor: [
                '#28a745',
                '#ffc107',
                '#6c757d'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
@endpush
