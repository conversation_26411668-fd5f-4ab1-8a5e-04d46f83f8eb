@extends('layouts.admin')

@section('title', 'Role & Permission Management')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">🔐 Role & Permission Management</h2>
                    <p class="text-muted mb-0">Manage user roles, permissions, and access control</p>
                </div>
                <div>
                    <a href="{{ route('admin.superadmin.roles.user-roles') }}" class="btn btn-primary me-2">
                        <i class="fas fa-users me-2"></i>Manage User Roles
                    </a>
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createRoleModal">
                        <i class="fas fa-plus me-2"></i>Create Role
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100" style="background: #e6f4ea; border: none;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: rgba(40, 167, 69, 0.1);">
                                <i class="fas fa-user-tag fa-2x text-success"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Total Roles</h6>
                            <h3 class="mb-0">{{ $roleStats['total_roles'] }}</h3>
                            <small class="text-success">
                                <i class="fas fa-shield-alt"></i> System roles
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100" style="background: #fff7e6; border: none;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: rgba(255, 193, 7, 0.1);">
                                <i class="fas fa-key fa-2x text-warning"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Total Permissions</h6>
                            <h3 class="mb-0">{{ $roleStats['total_permissions'] }}</h3>
                            <small class="text-warning">
                                <i class="fas fa-lock"></i> Access controls
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100" style="background: #e6f0ff; border: none;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: rgba(13, 110, 253, 0.1);">
                                <i class="fas fa-users fa-2x text-primary"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Users with Roles</h6>
                            <h3 class="mb-0">{{ $roleStats['users_with_roles'] }}</h3>
                            <small class="text-primary">
                                <i class="fas fa-check"></i> Assigned users
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100" style="background: #ffe6e6; border: none;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: rgba(220, 53, 69, 0.1);">
                                <i class="fas fa-user-slash fa-2x text-danger"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Users without Roles</h6>
                            <h3 class="mb-0">{{ $roleStats['users_without_roles'] }}</h3>
                            <small class="text-danger">
                                <i class="fas fa-exclamation-triangle"></i> Need assignment
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Roles Section -->
    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-tag me-2"></i>System Roles
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Role Name</th>
                                    <th>Users</th>
                                    <th>Permissions</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($roles as $role)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="role-icon me-3">
                                                @switch($role->name)
                                                    @case('admin')
                                                        <i class="fas fa-crown text-warning"></i>
                                                        @break
                                                    @case('treasurer')
                                                        <i class="fas fa-coins text-success"></i>
                                                        @break
                                                    @case('conference_admin')
                                                        <i class="fas fa-building text-primary"></i>
                                                        @break
                                                    @case('zone_admin')
                                                        <i class="fas fa-map-marked-alt text-info"></i>
                                                        @break
                                                    @case('branch_admin')
                                                        <i class="fas fa-sitemap text-secondary"></i>
                                                        @break
                                                    @case('moderator')
                                                        <i class="fas fa-user-shield text-purple"></i>
                                                        @break
                                                    @default
                                                        <i class="fas fa-user text-muted"></i>
                                                @endswitch
                                            </div>
                                            <div>
                                                <strong>{{ ucwords(str_replace('_', ' ', $role->name)) }}</strong>
                                                <br>
                                                <small class="text-muted">{{ $role->name }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ $role->users_count }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ $role->permissions_count }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.superadmin.roles.show', $role) }}"
                                               class="btn btn-sm btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if(!in_array($role->name, ['admin', 'user']))
                                            <form method="POST" action="{{ route('admin.superadmin.roles.delete', $role) }}"
                                                  class="d-inline" onsubmit="return confirm('Are you sure you want to delete this role?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete Role">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-key me-2"></i>System Permissions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="permission-list" style="max-height: 400px; overflow-y: auto;">
                        @foreach($permissions->groupBy(function($permission) {
                            return explode(' ', $permission->name)[0];
                        }) as $group => $groupPermissions)
                        <div class="permission-group mb-3">
                            <h6 class="text-primary border-bottom pb-1">
                                <i class="fas fa-folder me-2"></i>{{ ucfirst($group) }}
                            </h6>
                            @foreach($groupPermissions as $permission)
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <small class="text-muted">{{ $permission->name }}</small>
                                <span class="badge bg-light text-dark">{{ $permission->roles_count }}</span>
                            </div>
                            @endforeach
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Role Modal -->
<div class="modal fade" id="createRoleModal" tabindex="-1" aria-labelledby="createRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST" action="{{ route('admin.superadmin.roles.create') }}">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="createRoleModalLabel">
                        <i class="fas fa-plus me-2"></i>Create New Role
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="roleName" class="form-label">Role Name</label>
                        <input type="text" class="form-control" id="roleName" name="name" required>
                        <div class="form-text">Use lowercase with underscores (e.g., custom_admin)</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Permissions</label>
                        <div class="row">
                            @foreach($permissions->chunk(ceil($permissions->count() / 3)) as $chunk)
                            <div class="col-md-4">
                                @foreach($chunk as $permission)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                           name="permissions[]" value="{{ $permission->name }}" 
                                           id="perm_{{ $permission->id }}">
                                    <label class="form-check-label" for="perm_{{ $permission->id }}">
                                        <small>{{ $permission->name }}</small>
                                    </label>
                                </div>
                                @endforeach
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>Create Role
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.role-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 50%;
}

.permission-group {
    border-left: 3px solid #e9ecef;
    padding-left: 1rem;
}

.permission-list::-webkit-scrollbar {
    width: 6px;
}

.permission-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.permission-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.permission-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.text-purple {
    color: #6f42c1 !important;
}
</style>
@endpush
