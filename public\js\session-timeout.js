/**
 * Session Timeout Management
 * Handles automatic logout after 5 minutes of inactivity
 */

class SessionTimeoutManager {
    constructor(options = {}) {
        this.options = {
            timeout: 5 * 60 * 1000, // 5 minutes in milliseconds
            warningTime: 1 * 60 * 1000, // Show warning 1 minute before timeout
            checkInterval: 30 * 1000, // Check every 30 seconds
            loginUrl: '/login',
            ...options
        };
        
        this.lastActivity = Date.now();
        this.warningShown = false;
        this.timeoutTimer = null;
        this.warningTimer = null;
        this.checkTimer = null;
        this.modal = null;
        
        this.init();
    }
    
    init() {
        this.createWarningModal();
        this.bindEvents();
        this.startTimers();
        
        // Handle AJAX session expiry responses
        this.setupAjaxHandler();
    }
    
    createWarningModal() {
        // Create modal HTML
        const modalHtml = `
            <div class="modal fade" id="sessionTimeoutModal" tabindex="-1" aria-labelledby="sessionTimeoutModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content border-warning">
                        <div class="modal-header bg-warning text-dark">
                            <h5 class="modal-title" id="sessionTimeoutModalLabel">
                                <i class="fas fa-exclamation-triangle me-2"></i>Session Timeout Warning
                            </h5>
                        </div>
                        <div class="modal-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-clock fa-3x text-warning mb-3"></i>
                                <h6>Your session will expire in:</h6>
                                <div class="countdown-timer">
                                    <span id="countdown-minutes">01</span>:<span id="countdown-seconds">00</span>
                                </div>
                            </div>
                            <p class="text-muted">
                                You will be automatically logged out due to inactivity. 
                                Click "Stay Logged In" to continue your session.
                            </p>
                        </div>
                        <div class="modal-footer justify-content-center">
                            <button type="button" class="btn btn-success" id="stayLoggedInBtn">
                                <i class="fas fa-check me-2"></i>Stay Logged In
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="logoutNowBtn">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout Now
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        this.modal = new bootstrap.Modal(document.getElementById('sessionTimeoutModal'));
        
        // Bind modal events
        document.getElementById('stayLoggedInBtn').addEventListener('click', () => {
            this.extendSession();
        });
        
        document.getElementById('logoutNowBtn').addEventListener('click', () => {
            this.logout();
        });
    }
    
    bindEvents() {
        // Track user activity
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        
        events.forEach(event => {
            document.addEventListener(event, () => {
                this.updateActivity();
            }, { passive: true });
        });
        
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.checkSession();
            }
        });
        
        // Handle window focus
        window.addEventListener('focus', () => {
            this.checkSession();
        });
    }
    
    startTimers() {
        // Clear existing timers
        this.clearTimers();
        
        // Set warning timer
        this.warningTimer = setTimeout(() => {
            this.showWarning();
        }, this.options.timeout - this.options.warningTime);
        
        // Set timeout timer
        this.timeoutTimer = setTimeout(() => {
            this.logout();
        }, this.options.timeout);
        
        // Set periodic check timer
        this.checkTimer = setInterval(() => {
            this.checkSession();
        }, this.options.checkInterval);
    }
    
    clearTimers() {
        if (this.warningTimer) {
            clearTimeout(this.warningTimer);
            this.warningTimer = null;
        }
        
        if (this.timeoutTimer) {
            clearTimeout(this.timeoutTimer);
            this.timeoutTimer = null;
        }
        
        if (this.checkTimer) {
            clearInterval(this.checkTimer);
            this.checkTimer = null;
        }
    }
    
    updateActivity() {
        this.lastActivity = Date.now();
        
        if (this.warningShown) {
            this.hideWarning();
        }
        
        this.startTimers();
    }
    
    showWarning() {
        this.warningShown = true;
        this.modal.show();
        this.startCountdown();
    }
    
    hideWarning() {
        this.warningShown = false;
        this.modal.hide();
    }
    
    startCountdown() {
        const countdownInterval = setInterval(() => {
            const timeLeft = this.options.warningTime - (Date.now() - (this.lastActivity + this.options.timeout - this.options.warningTime));
            
            if (timeLeft <= 0) {
                clearInterval(countdownInterval);
                this.logout();
                return;
            }
            
            const minutes = Math.floor(timeLeft / 60000);
            const seconds = Math.floor((timeLeft % 60000) / 1000);
            
            document.getElementById('countdown-minutes').textContent = minutes.toString().padStart(2, '0');
            document.getElementById('countdown-seconds').textContent = seconds.toString().padStart(2, '0');
        }, 1000);
    }
    
    extendSession() {
        this.updateActivity();
        this.hideWarning();
        
        // Make a request to extend session
        fetch('/api/extend-session', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
            }
        }).catch(error => {
            console.warn('Failed to extend session:', error);
        });
    }
    
    checkSession() {
        // Check if session is still valid
        fetch('/api/check-session', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (response.status === 401) {
                this.logout();
            }
        })
        .catch(error => {
            console.warn('Session check failed:', error);
        });
    }
    
    logout() {
        this.clearTimers();
        this.hideWarning();
        
        // Show logout message
        this.showLogoutMessage();
        
        // Redirect to login after a short delay
        setTimeout(() => {
            window.location.href = this.options.loginUrl;
        }, 2000);
    }
    
    showLogoutMessage() {
        // Create logout overlay
        const overlay = document.createElement('div');
        overlay.className = 'session-logout-overlay';
        overlay.innerHTML = `
            <div class="session-logout-message">
                <div class="text-center">
                    <i class="fas fa-clock fa-3x text-warning mb-3"></i>
                    <h4>Session Expired</h4>
                    <p>You have been logged out due to inactivity.</p>
                    <p>Redirecting to login page...</p>
                    <div class="spinner-border text-primary mt-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(overlay);
    }
    
    setupAjaxHandler() {
        // Intercept AJAX responses for session expiry
        const originalFetch = window.fetch;
        
        window.fetch = function(...args) {
            return originalFetch.apply(this, args)
                .then(response => {
                    if (response.status === 401) {
                        response.json().then(data => {
                            if (data.session_expired) {
                                sessionManager.logout();
                            }
                        }).catch(() => {
                            // If we can't parse JSON, still handle 401
                            sessionManager.logout();
                        });
                    }
                    return response;
                });
        };
        
        // Handle jQuery AJAX if present
        if (window.jQuery) {
            jQuery(document).ajaxError(function(event, xhr, settings) {
                if (xhr.status === 401) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.session_expired) {
                            sessionManager.logout();
                        }
                    } catch (e) {
                        sessionManager.logout();
                    }
                }
            });
        }
    }
}

// CSS for session timeout components
const sessionTimeoutCSS = `
    .countdown-timer {
        font-size: 2rem;
        font-weight: bold;
        color: #dc3545;
        font-family: 'Courier New', monospace;
    }
    
    .session-logout-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }
    
    .session-logout-message {
        background: white;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        max-width: 400px;
        width: 90%;
    }
    
    .session-logout-message h4 {
        color: #dc3545;
        margin-bottom: 1rem;
    }
    
    .session-logout-message p {
        color: #6c757d;
        margin-bottom: 0.5rem;
    }
`;

// Add CSS to document
const style = document.createElement('style');
style.textContent = sessionTimeoutCSS;
document.head.appendChild(style);

// Initialize session manager when DOM is ready
let sessionManager;

document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if user is authenticated
    if (document.querySelector('meta[name="user-authenticated"]')) {
        sessionManager = new SessionTimeoutManager({
            loginUrl: document.querySelector('meta[name="login-url"]')?.getAttribute('content') || '/login'
        });
    }
});

// Export for manual initialization
window.SessionTimeoutManager = SessionTimeoutManager;
