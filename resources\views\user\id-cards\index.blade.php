@extends('layouts.user')

@section('title', 'My ID Cards')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <h2 class="mb-0">My ID Cards</h2>
        <p class="text-muted">Generate and manage your summit ID cards.</p>
    </div>
</div>

<!-- Generate New Card Section -->
@if($currentSummit && $canGenerateForCurrent)
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-id-card me-2"></i>Generate ID Card for {{ $currentSummit->title }}</h5>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-2">{{ $currentSummit->title }} ({{ $currentSummit->year }})</h6>
                        <p class="text-muted mb-2">
                            <i class="fas fa-calendar me-2"></i>
                            {{ $currentSummit->start_date->format('M j') }} - {{ $currentSummit->end_date->format('M j, Y') }}
                        </p>
                        <p class="text-muted mb-0">
                            <i class="fas fa-map-marker-alt me-2"></i>{{ $currentSummit->venue }}
                        </p>
                        <small class="text-success">
                            <i class="fas fa-check-circle me-1"></i>Payment completed - You can generate your ID card
                        </small>
                    </div>
                    <div class="col-md-4 text-end">
                        <form method="POST" action="{{ route('id-cards.generate') }}">
                            @csrf
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-id-card me-2"></i>Generate ID Card
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@elseif($currentSummit)
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Payment Required</h5>
            </div>
            <div class="card-body">
                <p class="mb-2">Complete your payment for <strong>{{ $currentSummit->title }}</strong> to generate your ID card.</p>
                <a href="{{ route('registrations.index') }}" class="btn btn-warning">
                    <i class="fas fa-credit-card me-2"></i>Complete Payment
                </a>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Existing ID Cards -->
@if($activeCards->count() > 0)
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-id-badge me-2"></i>Your ID Cards</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($activeCards as $card)
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card h-100 shadow-sm border-0" style="border-radius: 15px;">
                            <!-- Card Header with Summit Info -->
                            <div class="card-header bg-gradient-primary text-white text-center" style="border-radius: 15px 15px 0 0;">
                                <h6 class="mb-1">{{ $card->summit->title }}</h6>
                                <small>{{ $card->summit->year }}</small>
                            </div>
                            
                            <!-- Card Body with User Info -->
                            <div class="card-body text-center p-4">
                                <!-- Profile Image -->
                                <div class="mb-3">
                                    @if($card->user->profile_image)
                                        <img src="{{ Storage::url($card->user->profile_image) }}" 
                                             alt="Profile" 
                                             class="rounded-circle border border-3 border-primary"
                                             style="width: 80px; height: 80px; object-fit: cover;">
                                    @else
                                        <div class="rounded-circle bg-primary d-inline-flex align-items-center justify-content-center border border-3 border-primary"
                                             style="width: 80px; height: 80px;">
                                            <i class="fas fa-user fa-2x text-white"></i>
                                        </div>
                                    @endif
                                </div>
                                
                                <!-- User Details -->
                                <h6 class="mb-1">{{ $card->user->full_name }}</h6>
                                <p class="text-muted small mb-2">{{ $card->user->email }}</p>
                                
                                <!-- Card Number -->
                                <div class="mb-3">
                                    <span class="badge bg-dark fs-6">{{ $card->card_number }}</span>
                                </div>
                                
                                <!-- Branch Info -->
                                <div class="mb-3">
                                    <small class="text-muted d-block">{{ $card->user->branch->name ?? 'N/A' }}</small>
                                    <small class="text-muted">{{ $card->user->branch->zone->conference->name ?? 'N/A' }}</small>
                                </div>
                                
                                <!-- QR Code -->
                                @if($card->qr_code_path && Storage::disk('public')->exists($card->qr_code_path))
                                <div class="mb-3">
                                    <img src="{{ Storage::url($card->qr_code_path) }}" 
                                         alt="QR Code" 
                                         class="img-fluid"
                                         style="max-width: 100px;">
                                </div>
                                @endif
                                
                                <!-- Card Status -->
                                <div class="mb-3">
                                    <span class="badge bg-success">
                                        <i class="fas fa-check-circle me-1"></i>Active
                                    </span>
                                    <small class="text-muted d-block mt-1">
                                        Expires: {{ $card->expires_at->format('M j, Y') }}
                                    </small>
                                </div>
                            </div>
                            
                            <!-- Card Footer with Actions -->
                            <div class="card-footer bg-light text-center" style="border-radius: 0 0 15px 15px;">
                                <div class="btn-group w-100">
                                    <a href="{{ route('id-cards.show', $card) }}" 
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i>View
                                    </a>
                                    <a href="{{ route('id-cards.download', $card) }}" 
                                       class="btn btn-primary btn-sm">
                                        <i class="fas fa-download me-1"></i>Download
                                    </a>
                                    <a href="{{ route('id-cards.preview', $card) }}" 
                                       class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-search me-1"></i>Preview
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@else
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-id-card fa-4x text-muted mb-4"></i>
                <h5 class="text-muted">No ID Cards Yet</h5>
                <p class="text-muted">You don't have any active ID cards. Complete your summit registration and payment to generate your ID card.</p>
                @if($currentSummit)
                    <a href="{{ route('registrations.index') }}" class="btn btn-primary">
                        <i class="fas fa-clipboard-list me-2"></i>View Registrations
                    </a>
                @else
                    <a href="{{ route('landing.index') }}" class="btn btn-primary">
                        <i class="fas fa-mountain me-2"></i>View Summits
                    </a>
                @endif
            </div>
        </div>
    </div>
</div>
@endif

<!-- Information Card -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>ID Card Information</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>How to use your ID Card:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Download and print your ID card</li>
                            <li><i class="fas fa-check text-success me-2"></i>Present it at summit registration</li>
                            <li><i class="fas fa-check text-success me-2"></i>Use QR code for quick verification</li>
                            <li><i class="fas fa-check text-success me-2"></i>Keep it with you during the summit</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Important Notes:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>Payment must be complete to generate ID</li>
                            <li><i class="fas fa-clock text-info me-2"></i>Cards expire 30 days after summit</li>
                            <li><i class="fas fa-shield-alt text-success me-2"></i>QR codes are secure and unique</li>
                            <li><i class="fas fa-print text-primary me-2"></i>Print on standard A4 paper</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.btn-group .btn {
    flex: 1;
}
</style>
@endpush
