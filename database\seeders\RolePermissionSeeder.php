<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // User Management
            'view users',
            'create users',
            'edit users',
            'delete users',

            // Summit Management
            'view summits',
            'create summits',
            'edit summits',
            'delete summits',
            'manage summit activities',
            'manage summit ministers',

            // Registration Management
            'view registrations',
            'create registrations',
            'edit registrations',
            'delete registrations',

            // Payment Management
            'view payments',
            'process payments',
            'view payment reports',
            'manage payment methods',

            // ID Card Management
            'view id cards',
            'generate id cards',
            'scan qr codes',

            // Meal Tracking
            'track meals',
            'view meal reports',

            // Gate Management
            'manage gate entries',
            'view gate reports',

            // Reporting
            'view branch reports',
            'view zone reports',
            'view conference reports',
            'view union reports',
            'view financial reports',
            'view analytics',
            'export reports',

            // System Administration
            'manage system settings',
            'manage roles',
            'manage permissions',
            'view system logs',

            // Organization Management
            'manage universities',
            'manage conferences',
            'manage zones',
            'manage branches',

            // Multi-level reporting
            'view conference reports',
            'view zone reports',
            'view branch reports',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Admin Role - Full access
        $adminRole = Role::create(['name' => 'admin']);
        $adminRole->givePermissionTo(Permission::all());

        // Treasurer Role - Financial and reporting access
        $treasurerRole = Role::create(['name' => 'treasurer']);
        $treasurerRole->givePermissionTo([
            'view users',
            'view summits',
            'view registrations',
            'view payments',
            'process payments',
            'view payment reports',
            'view branch reports',
            'view zone reports',
            'view conference reports',
            'view union reports',
            'view financial reports',
            'export reports',
            'view analytics', // Add analytics permission
            'manage payment methods',
        ]);

        // Conference Admin Role - Conference level management
        $conferenceAdminRole = Role::create(['name' => 'conference_admin']);
        $conferenceAdminRole->givePermissionTo([
            'view users',
            'edit users',
            'view summits',
            'view registrations',
            'view payments',
            'view payment reports',
            'view conference reports',
            'view zone reports',
            'view branch reports',
            'manage zones',
            'manage branches',
            'view analytics',
            'export reports',
        ]);

        // Zone Admin Role - Zone level management
        $zoneAdminRole = Role::create(['name' => 'zone_admin']);
        $zoneAdminRole->givePermissionTo([
            'view users',
            'edit users',
            'view summits',
            'view registrations',
            'view payments',
            'view payment reports',
            'view zone reports',
            'view branch reports',
            'manage branches',
            'view analytics',
            'export reports',
        ]);

        // Branch Admin Role - Branch level management
        $branchAdminRole = Role::create(['name' => 'branch_admin']);
        $branchAdminRole->givePermissionTo([
            'view users',
            'edit users',
            'view summits',
            'view registrations',
            'view payments',
            'view payment reports',
            'view branch reports',
            'view analytics',
            'export reports',
        ]);

        // User Role - Basic user access
        $userRole = Role::create(['name' => 'user']);
        $userRole->givePermissionTo([
            'view summits',
            'create registrations',
            'view payments',
            'view id cards',
        ]);

        // Moderator Role - Event management
        $moderatorRole = Role::create(['name' => 'moderator']);
        $moderatorRole->givePermissionTo([
            'view users',
            'view summits',
            'view registrations',
            'view id cards',
            'generate id cards',
            'scan qr codes',
            'track meals',
            'view meal reports',
            'manage gate entries',
            'view gate reports',
        ]);
    }
}
