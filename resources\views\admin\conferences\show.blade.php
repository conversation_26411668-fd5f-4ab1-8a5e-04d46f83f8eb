@extends('layouts.admin')

@section('title', 'Conference Details')
@section('page-title', 'Conference Details')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">{{ $conference->name }}</h2>
                <p class="text-muted">Conference details and statistics.</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('admin.conferences.edit', $conference) }}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>Edit Conference
                </a>
                <a href="{{ route('admin.conferences.index') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Conferences
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Conference Information -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-globe me-2"></i>Conference Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Name:</strong></td>
                                <td>{{ $conference->name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Code:</strong></td>
                                <td><span class="badge bg-primary">{{ $conference->code }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Region:</strong></td>
                                <td>{{ $conference->region ?? 'Not specified' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    <span class="badge bg-{{ $conference->is_active ? 'success' : 'secondary' }}">
                                        {{ $conference->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Total Zones:</strong></td>
                                <td><span class="badge bg-info">{{ $conference->zones->count() }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Total Branches:</strong></td>
                                <td><span class="badge bg-success">{{ $conference->zones->sum(function($zone) { return $zone->branches->count(); }) }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Created:</strong></td>
                                <td>{{ $conference->created_at->format('F j, Y') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Last Updated:</strong></td>
                                <td>{{ $conference->updated_at->format('F j, Y') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                @if($conference->description)
                <div class="mt-3">
                    <h6>Description</h6>
                    <p class="text-muted">{{ $conference->description }}</p>
                </div>
                @endif
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Quick Stats</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Active Zones</span>
                    <span class="badge bg-success">{{ $conference->zones->where('is_active', true)->count() }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Inactive Zones</span>
                    <span class="badge bg-secondary">{{ $conference->zones->where('is_active', false)->count() }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Active Branches</span>
                    <span class="badge bg-success">{{ $conference->zones->sum(function($zone) { return $zone->branches->where('is_active', true)->count(); }) }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Inactive Branches</span>
                    <span class="badge bg-secondary">{{ $conference->zones->sum(function($zone) { return $zone->branches->where('is_active', false)->count(); }) }}</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Zones -->
@if($conference->zones->count() > 0)
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-map me-2"></i>Zones ({{ $conference->zones->count() }})</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Zone Name</th>
                                <th>Code</th>
                                <th>Branches</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($conference->zones as $zone)
                            <tr>
                                <td>
                                    <div class="fw-bold">{{ $zone->name }}</div>
                                    @if($zone->description)
                                        <small class="text-muted">{{ Str::limit($zone->description, 40) }}</small>
                                    @endif
                                </td>
                                <td><span class="badge bg-primary">{{ $zone->code }}</span></td>
                                <td>
                                    <span class="badge bg-info">{{ $zone->branches->count() }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $zone->is_active ? 'success' : 'secondary' }}">
                                        {{ $zone->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('admin.zones.show', $zone) }}" 
                                           class="btn btn-outline-primary" title="View Zone">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.zones.edit', $zone) }}" 
                                           class="btn btn-outline-secondary" title="Edit Zone">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endif
@endsection
