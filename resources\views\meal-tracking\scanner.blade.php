@extends('layouts.app')

@section('title', 'Meal QR Code Scanner')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-qrcode me-2"></i>
                        Meal QR Code Scanner - {{ $summit->title }}
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Scanner Interface -->
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">QR Code Scanner</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Camera Preview -->
                                    <div id="qr-reader" style="width: 100%; height: 300px; border: 2px dashed #ddd; border-radius: 8px; position: relative;">
                                        <div class="d-flex align-items-center justify-content-center h-100">
                                            <div class="text-center">
                                                <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                                                <p class="text-muted">Click "Start Scanner" to begin scanning QR codes</p>
                                                <button type="button" id="start-scanner" class="btn btn-primary">
                                                    <i class="fas fa-play me-2"></i>Start Scanner
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Scanner Controls -->
                                    <div class="mt-3">
                                        <div class="row">
                                            <div class="col-6">
                                                <button type="button" id="stop-scanner" class="btn btn-danger w-100" style="display: none;">
                                                    <i class="fas fa-stop me-2"></i>Stop Scanner
                                                </button>
                                            </div>
                                            <div class="col-6">
                                                <button type="button" id="switch-camera" class="btn btn-secondary w-100" style="display: none;">
                                                    <i class="fas fa-sync-alt me-2"></i>Switch Camera
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-6">
                            <!-- Manual Entry Form -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Manual Entry</h5>
                                </div>
                                <div class="card-body">
                                    <form id="meal-tracking-form">
                                        @csrf
                                        <div class="mb-3">
                                            <label for="card_number" class="form-label">Card Number</label>
                                            <input type="text" class="form-control" id="card_number" name="card_number" 
                                                   placeholder="Enter card number or scan QR code" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="meal_type" class="form-label">Meal Type</label>
                                            <select class="form-select" id="meal_type" name="meal_type" required>
                                                <option value="">Select meal type</option>
                                                <option value="breakfast">🍳 Breakfast</option>
                                                <option value="lunch">🍽️ Lunch</option>
                                                <option value="dinner">🍽️ Dinner</option>
                                                <option value="snack">🍪 Snack</option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="activity_id" class="form-label">Activity (Optional)</label>
                                            <select class="form-select" id="activity_id" name="activity_id">
                                                <option value="">Auto-detect current activity</option>
                                                @foreach($activities as $activity)
                                                    <option value="{{ $activity->id }}">
                                                        {{ $activity->title }} - {{ $activity->start_time->format('M j, H:i') }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="notes" class="form-label">Notes (Optional)</label>
                                            <textarea class="form-control" id="notes" name="notes" rows="2" 
                                                      placeholder="Additional notes..."></textarea>
                                        </div>
                                        
                                        <button type="submit" class="btn btn-success w-100">
                                            <i class="fas fa-check me-2"></i>Track Meal
                                        </button>
                                    </form>
                                </div>
                            </div>
                            
                            <!-- Recent Scans -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="mb-0">Recent Scans</h5>
                                </div>
                                <div class="card-body">
                                    <div id="recent-scans">
                                        <p class="text-muted text-center">No recent scans</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Modal -->
<div class="modal fade" id="resultModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resultModalTitle">Scan Result</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="resultModalBody">
                <!-- Result content will be inserted here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    #qr-reader {
        background: #f8f9fa;
    }
    
    #qr-reader video {
        width: 100% !important;
        height: 100% !important;
        object-fit: cover;
        border-radius: 6px;
    }
    
    .recent-scan-item {
        border-left: 4px solid #28a745;
        padding: 10px;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-radius: 4px;
    }
    
    .recent-scan-item.error {
        border-left-color: #dc3545;
        background: #fff5f5;
    }
    
    .scan-time {
        font-size: 0.8em;
        color: #6c757d;
    }
</style>
@endpush

@push('scripts')
<!-- QR Code Scanner Library -->
<script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>

<script>
let html5QrCode = null;
let isScanning = false;

$(document).ready(function() {
    // Initialize scanner
    $('#start-scanner').click(function() {
        startScanner();
    });
    
    $('#stop-scanner').click(function() {
        stopScanner();
    });
    
    // Handle form submission
    $('#meal-tracking-form').submit(function(e) {
        e.preventDefault();
        trackMeal();
    });
});

function startScanner() {
    if (isScanning) return;
    
    html5QrCode = new Html5Qrcode("qr-reader");
    
    Html5Qrcode.getCameras().then(devices => {
        if (devices && devices.length) {
            const cameraId = devices[0].id;
            
            html5QrCode.start(
                cameraId,
                {
                    fps: 10,
                    qrbox: { width: 250, height: 250 }
                },
                (decodedText, decodedResult) => {
                    // QR code successfully scanned
                    handleQrCodeScan(decodedText);
                },
                (errorMessage) => {
                    // Ignore scanning errors (they're frequent)
                }
            ).then(() => {
                isScanning = true;
                $('#start-scanner').hide();
                $('#stop-scanner, #switch-camera').show();
            }).catch(err => {
                console.error('Error starting scanner:', err);
                showAlert('Error starting camera. Please check permissions.', 'danger');
            });
        } else {
            showAlert('No cameras found on this device.', 'warning');
        }
    }).catch(err => {
        console.error('Error getting cameras:', err);
        showAlert('Error accessing cameras.', 'danger');
    });
}

function stopScanner() {
    if (html5QrCode && isScanning) {
        html5QrCode.stop().then(() => {
            isScanning = false;
            $('#start-scanner').show();
            $('#stop-scanner, #switch-camera').hide();
            
            // Reset the scanner div
            $('#qr-reader').html(`
                <div class="d-flex align-items-center justify-content-center h-100">
                    <div class="text-center">
                        <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Click "Start Scanner" to begin scanning QR codes</p>
                        <button type="button" id="start-scanner" class="btn btn-primary">
                            <i class="fas fa-play me-2"></i>Start Scanner
                        </button>
                    </div>
                </div>
            `);
            
            // Re-bind the start button
            $('#start-scanner').click(function() {
                startScanner();
            });
        });
    }
}

function handleQrCodeScan(qrData) {
    try {
        // Try to parse as JSON (ID card QR code)
        const data = JSON.parse(qrData);
        if (data.card_number) {
            $('#card_number').val(data.card_number);
            showAlert(`Card scanned: ${data.name || 'Unknown'}`, 'success');
        }
    } catch (e) {
        // If not JSON, treat as plain card number
        $('#card_number').val(qrData);
        showAlert('QR code scanned successfully', 'success');
    }
    
    // Auto-submit if meal type is selected
    if ($('#meal_type').val()) {
        trackMeal();
    }
}

function trackMeal() {
    const formData = new FormData($('#meal-tracking-form')[0]);
    
    $.ajax({
        url: '{{ route("meal-tracking.scan") }}',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                showResult('Success!', response.message, 'success', response.data);
                addRecentScan(response.data, true);
                $('#meal-tracking-form')[0].reset();
            }
        },
        error: function(xhr) {
            const response = xhr.responseJSON;
            showResult('Error!', response.message || 'An error occurred', 'error');
            addRecentScan({ error: response.message }, false);
        }
    });
}

function showResult(title, message, type, data = null) {
    $('#resultModalTitle').text(title);
    
    let content = `<p>${message}</p>`;
    if (data && type === 'success') {
        content += `
            <div class="mt-3">
                <strong>Details:</strong><br>
                <small>
                    User: ${data.user_name}<br>
                    Meal: ${data.meal_type}<br>
                    Time: ${data.scanned_at}<br>
                    Summit: ${data.summit_title}
                </small>
            </div>
        `;
    }
    
    $('#resultModalBody').html(content);
    $('#resultModal').modal('show');
}

function addRecentScan(data, success) {
    const time = new Date().toLocaleTimeString();
    const scanItem = $(`
        <div class="recent-scan-item ${success ? '' : 'error'}">
            <div class="d-flex justify-content-between">
                <div>
                    ${success ? 
                        `<strong>${data.user_name}</strong> - ${data.meal_type}` : 
                        `<strong>Error:</strong> ${data.error}`
                    }
                </div>
                <div class="scan-time">${time}</div>
            </div>
        </div>
    `);
    
    const recentScans = $('#recent-scans');
    if (recentScans.find('p').length) {
        recentScans.empty();
    }
    
    recentScans.prepend(scanItem);
    
    // Keep only last 5 scans
    recentScans.find('.recent-scan-item:gt(4)').remove();
}

function showAlert(message, type) {
    // You can implement a toast notification here
    console.log(`${type.toUpperCase()}: ${message}`);
}
</script>
@endpush
