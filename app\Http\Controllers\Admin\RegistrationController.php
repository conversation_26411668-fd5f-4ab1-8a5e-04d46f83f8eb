<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SummitRegistration;
use App\Models\Summit;
use App\Models\User;
use Illuminate\Http\Request;

class RegistrationController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * Display a listing of registrations.
     */
    public function index(Request $request)
    {
        $query = SummitRegistration::with(['user', 'summit']);

        // Filter by summit
        if ($request->filled('summit_id')) {
            $query->where('summit_id', $request->summit_id);
        }

        // Filter by payment status
        if ($request->filled('payment_status')) {
            if ($request->payment_status === 'paid') {
                $query->where('payment_complete', true);
            } elseif ($request->payment_status === 'pending') {
                $query->where('payment_complete', false);
            }
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%");
            })->orWhere('registration_number', 'like', "%{$search}%");
        }

        $registrations = $query->orderBy('registered_at', 'desc')->paginate(15);
        $summits = Summit::orderBy('year', 'desc')->get();

        return view('admin.registrations.index', compact('registrations', 'summits'));
    }

    /**
     * Display the specified registration.
     */
    public function show(SummitRegistration $registration)
    {
        $registration->load([
            'user.branch.zone.conference',
            'summit',
            'payments' => function ($query) {
                $query->orderBy('created_at', 'desc');
            }
        ]);

        return view('admin.registrations.show', compact('registration'));
    }

    /**
     * Cancel a registration.
     */
    public function cancel(SummitRegistration $registration)
    {
        // Use the model method to check if admin can cancel
        if (!$registration->canBeCancelledByAdmin()) {
            return redirect()->back()
                ->with('error', 'Registration is already cancelled.');
        }

        // Cancel any pending/failed payments for this registration
        $registration->payments()
            ->whereIn('status', ['pending', 'processing', 'failed'])
            ->update(['status' => 'cancelled']);

        // Update registration status
        $registration->update([
            'status' => 'cancelled'
        ]);

        return redirect()->back()
            ->with('success', 'Registration has been cancelled successfully. Any pending or failed payments have also been cancelled.');
    }
}
