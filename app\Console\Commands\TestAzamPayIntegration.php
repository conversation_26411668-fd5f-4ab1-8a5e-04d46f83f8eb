<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AzamPayService;
use App\Models\SummitRegistration;
use App\Models\User;
use App\Models\Summit;

class TestAzamPayIntegration extends Command
{
    protected $signature = 'azampay:test {--dry-run : Run without making actual API calls}';
    protected $description = 'Test AzamPay integration with sandbox credentials';

    public function handle()
    {
        $this->info('🧪 Testing AzamPay Integration...');
        $this->newLine();

        // Check configuration
        $this->checkConfiguration();

        if ($this->option('dry-run')) {
            $this->warn('🔍 DRY RUN MODE - No actual API calls will be made');
            $this->newLine();
        }

        // Test authentication
        $this->testAuthentication();

        // Test payment methods
        $this->testPaymentMethods();

        $this->newLine();
        $this->info('✅ AzamPay integration test completed!');
    }

    private function checkConfiguration()
    {
        $this->info('📋 Checking Configuration...');

        $requiredConfigs = [
            'AZAMPAY_BASE_URL' => config('services.azampay.base_url'),
            'AZAMPAY_APP_NAME' => config('services.azampay.app_name'),
            'AZAMPAY_CLIENT_ID' => config('services.azampay.client_id'),
            'AZAMPAY_CLIENT_SECRET' => config('services.azampay.client_secret'),
            'AZAMPAY_MERCHANT_MOBILE' => config('services.azampay.merchant_mobile'),
        ];

        foreach ($requiredConfigs as $key => $value) {
            if (empty($value)) {
                $this->error("❌ Missing configuration: {$key}");
            } else {
                $this->line("✅ {$key}: " . (strlen($value) > 20 ? substr($value, 0, 20) . '...' : $value));
            }
        }

        $this->newLine();
    }

    private function testAuthentication()
    {
        $this->info('🔐 Testing Authentication...');

        if ($this->option('dry-run')) {
            $this->line('📝 Would test: POST /AppRegistration/GenerateToken');
            $this->line('📝 Request body: {"appName": "...", "clientId": "...", "clientSecret": "..."}');
            $this->info('✅ Authentication test skipped (dry-run)');
            $this->newLine();
            return;
        }

        try {
            $azamPayService = new AzamPayService();
            
            // Use reflection to test private method
            $reflection = new \ReflectionClass($azamPayService);
            $method = $reflection->getMethod('getAuthToken');
            $method->setAccessible(true);
            
            $token = $method->invoke($azamPayService);
            
            if ($token) {
                $this->info('✅ Authentication successful');
                $this->line('🔑 Token received: ' . substr($token, 0, 20) . '...');
            } else {
                $this->error('❌ Authentication failed');
            }
        } catch (\Exception $e) {
            $this->error('❌ Authentication error: ' . $e->getMessage());
        }

        $this->newLine();
    }

    private function testPaymentMethods()
    {
        $this->info('💳 Testing Payment Methods...');

        // Get test data
        $user = User::first();
        $summit = Summit::first();

        if (!$user || !$summit) {
            $this->error('❌ No test data found. Please ensure you have users and summits in the database.');
            return;
        }

        // Get or create test registration
        $registration = SummitRegistration::where('user_id', $user->id)
            ->where('summit_id', $summit->id)
            ->first();

        if (!$registration) {
            $registration = SummitRegistration::create([
                'user_id' => $user->id,
                'summit_id' => $summit->id,
                'registration_number' => 'TEST_' . time(),
                'registered_at' => now(),
                'total_amount' => 50000,
                'paid_amount' => 0,
                'balance' => 50000,
                'status' => 'pending'
            ]);
            $this->line("📝 Created test registration: {$registration->registration_number}");
        } else {
            $this->line("📝 Using existing registration: {$registration->registration_number}");
        }

        if ($this->option('dry-run')) {
            $this->testMobileMoneyDryRun($registration);
            $this->testBankTransferDryRun($registration);
        } else {
            $this->testMobileMoneyLive($registration);
            $this->testBankTransferLive($registration);
        }

        // Don't delete existing registrations, only test ones
        if (str_starts_with($registration->registration_number, 'TEST_')) {
            $registration->delete();
            $this->line('🧹 Cleaned up test registration');
        }
    }

    private function testMobileMoneyDryRun($registration)
    {
        $this->info('📱 Testing Mobile Money (Dry Run)...');
        $this->line('📝 Would test: POST /azampay/mno/checkout');
        $this->line('📝 Request body:');
        $this->line('   {');
        $this->line('     "accountNumber": "*********",');
        $this->line('     "amount": "50000",');
        $this->line('     "currency": "TZS",');
        $this->line('     "externalId": "' . $registration->registration_number . '",');
        $this->line('     "provider": "Vodacom"');
        $this->line('   }');
        $this->info('✅ Mobile money test skipped (dry-run)');
        $this->newLine();
    }

    private function testBankTransferDryRun($registration)
    {
        $this->info('🏦 Testing Bank Transfer (Dry Run)...');
        $this->line('📝 Would test: POST /azampay/bank/checkout');
        $this->line('📝 Request body:');
        $this->line('   {');
        $this->line('     "amount": "50000",');
        $this->line('     "currencyCode": "TZS",');
        $this->line('     "provider": "CRDB",');
        $this->line('     "referenceId": "' . $registration->registration_number . '"');
        $this->line('   }');
        $this->info('✅ Bank transfer test skipped (dry-run)');
        $this->newLine();
    }

    private function testMobileMoneyLive($registration)
    {
        $this->info('📱 Testing Mobile Money (Live)...');

        try {
            $azamPayService = new AzamPayService();
            $result = $azamPayService->initiateMobileMoneyPayment(
                $registration,
                1000, // Small test amount
                '*********', // Test phone number
                'Vodacom'
            );

            if ($result['success']) {
                $this->info('✅ Mobile money payment initiated successfully');
                $this->line('💳 Payment ID: ' . $result['payment']->id);
            } else {
                $this->error('❌ Mobile money payment failed: ' . $result['message']);
            }
        } catch (\Exception $e) {
            $this->error('❌ Mobile money error: ' . $e->getMessage());
        }

        $this->newLine();
    }

    private function testBankTransferLive($registration)
    {
        $this->info('🏦 Testing Bank Transfer (Live)...');

        try {
            $azamPayService = new AzamPayService();
            $result = $azamPayService->initiateBankPayment(
                $registration,
                1000, // Small test amount
                'CRDB'
            );

            if ($result['success']) {
                $this->info('✅ Bank transfer payment initiated successfully');
                $this->line('💳 Payment ID: ' . $result['payment']->id);
                if (isset($result['checkout_url'])) {
                    $this->line('🔗 Checkout URL: ' . $result['checkout_url']);
                }
            } else {
                $this->error('❌ Bank transfer payment failed: ' . $result['message']);
            }
        } catch (\Exception $e) {
            $this->error('❌ Bank transfer error: ' . $e->getMessage());
        }

        $this->newLine();
    }
}
