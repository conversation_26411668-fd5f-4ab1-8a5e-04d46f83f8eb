# AzamPay API Endpoints - VERIFY WITH AZAMPAY DOCUMENTATION

## Current Implementation (MAY BE INCORRECT)
```php
// Authentication
POST /azampay/auth/login

// Mobile Money Checkout  
POST /azampay/mno/checkout

// Bank Checkout
POST /azampay/bank/checkout

// Payment Status Check
GET /azampay/gw/checkout/status
```

## REQUIRED: Get Correct Endpoints from AzamPay
Contact AzamPay support to get the correct API endpoints:

1. **Authentication Endpoint**
2. **Mobile Money Checkout Endpoint** 
3. **Bank Transfer Checkout Endpoint**
4. **Payment Status Check Endpoint**
5. **Webhook Callback Format**

## Update These Files After Getting Correct Endpoints:
- `app/Services/AzamPayService.php` (lines 35, 75, 125, 175)
- Test all endpoints in sandbox before production

## AzamPay Contact Information:
- Website: https://azampay.co.tz
- Email: <EMAIL>
- Phone: +255 XXX XXX XXX (get from their website)
