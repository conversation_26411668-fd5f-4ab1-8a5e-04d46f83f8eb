<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\SummitRegistration;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SelcomPaymentService
{
    private $apiKey;
    private $secretKey;
    private $baseUrl;
    private $vendorType;
    private $msisdn;

    public function __construct()
    {
        $this->apiKey = config('services.selcom.api_key');
        $this->secretKey = config('services.selcom.secret_key');
        $this->baseUrl = config('services.selcom.base_url');
        $this->vendorType = config('services.selcom.vendor_type');
        $this->msisdn = config('services.selcom.msisdn');
    }

    /**
     * Initialize a payment request
     */
    public function initiatePayment(SummitRegistration $registration, float $amount, string $phoneNumber, string $mnoProvider = 'vodacom')
    {
        $paymentReference = 'SUMMIT_' . $registration->id . '_' . time();

        // Format phone number to international format
        $formattedPhone = '+255' . $phoneNumber;

        // Create payment record
        $payment = Payment::create([
            'user_id' => $registration->user_id,
            'summit_registration_id' => $registration->id,
            'payment_reference' => $paymentReference,
            'amount' => $amount,
            'payment_method' => 'selcom',
            'status' => 'pending',
            'gateway_data' => [
                'mno_provider' => $mnoProvider,
                'phone_number' => $formattedPhone,
            ],
        ]);

        $payload = [
            'vendor' => $this->msisdn,
            'order_id' => $paymentReference,
            'buyer_email' => $registration->user->email,
            'buyer_name' => $registration->user->full_name,
            'buyer_phone' => $formattedPhone,
            'amount' => $amount,
            'currency' => 'TZS',
            'webhook' => route('payments.webhook'),
            'cancel_url' => route('payments.cancel', $payment),
            'success_url' => route('payments.success', $payment),
            'payment_methods' => [$this->mapMnoProvider($mnoProvider)],
            'billing_address' => [
                'country_code' => 'TZ',
                'city' => 'Dar es Salaam',
                'state_or_region' => 'Dar es Salaam',
                'postal_or_zip_code' => '00000',
                'line_1' => $registration->user->address ?? 'N/A',
            ],
        ];

        try {
            $response = Http::withHeaders([
                'Authorization' => 'SELCOM ' . base64_encode($this->apiKey . ':' . $this->secretKey),
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/checkout/create-order', $payload);

            if ($response->successful()) {
                $responseData = $response->json();
                
                $payment->update([
                    'transaction_id' => $responseData['order_id'] ?? null,
                    'gateway_data' => $responseData,
                    'status' => 'processing',
                ]);

                return [
                    'success' => true,
                    'payment' => $payment,
                    'checkout_url' => $responseData['payment_url'] ?? null,
                    'data' => $responseData,
                ];
            } else {
                $payment->update([
                    'status' => 'failed',
                    'gateway_response' => $response->body(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Payment initialization failed',
                    'error' => $response->body(),
                ];
            }
        } catch (\Exception $e) {
            Log::error('Selcom payment error: ' . $e->getMessage());
            
            $payment->update([
                'status' => 'failed',
                'gateway_response' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Payment service unavailable',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Handle payment webhook
     */
    public function handleWebhook(array $data)
    {
        try {
            $paymentReference = $data['order_id'] ?? null;
            $status = $data['payment_status'] ?? null;
            
            if (!$paymentReference) {
                return ['success' => false, 'message' => 'Invalid payment reference'];
            }

            $payment = Payment::where('payment_reference', $paymentReference)->first();
            
            if (!$payment) {
                return ['success' => false, 'message' => 'Payment not found'];
            }

            // Update payment status
            $payment->update([
                'status' => $this->mapSelcomStatus($status),
                'gateway_data' => $data,
                'paid_at' => $status === 'COMPLETED' ? now() : null,
                'receipt_number' => $data['receipt_number'] ?? null,
            ]);

            // Update registration if payment is completed
            if ($status === 'COMPLETED') {
                $registration = $payment->summitRegistration;
                $registration->paid_amount += $payment->amount;
                $registration->balance = $registration->total_amount - $registration->paid_amount;
                $registration->payment_complete = $registration->balance <= 0;
                $registration->save();
            }

            return ['success' => true, 'payment' => $payment];
            
        } catch (\Exception $e) {
            Log::error('Webhook processing error: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Check payment status
     */
    public function checkPaymentStatus(Payment $payment)
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'SELCOM ' . base64_encode($this->apiKey . ':' . $this->secretKey),
            ])->get($this->baseUrl . '/checkout/order-status', [
                'order_id' => $payment->payment_reference,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $status = $data['payment_status'] ?? null;
                
                $payment->update([
                    'status' => $this->mapSelcomStatus($status),
                    'gateway_data' => $data,
                    'paid_at' => $status === 'COMPLETED' ? now() : null,
                ]);

                return ['success' => true, 'status' => $status, 'data' => $data];
            }

            return ['success' => false, 'message' => 'Status check failed'];
            
        } catch (\Exception $e) {
            Log::error('Status check error: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Map Selcom status to our internal status
     */
    private function mapSelcomStatus($selcomStatus)
    {
        return match($selcomStatus) {
            'COMPLETED' => 'completed',
            'PENDING' => 'processing',
            'FAILED' => 'failed',
            'CANCELLED' => 'cancelled',
            default => 'pending',
        };
    }

    /**
     * Map MNO provider to Selcom payment method
     */
    private function mapMnoProvider($provider)
    {
        return match($provider) {
            'vodacom' => 'MPESA',
            'tigo' => 'TIGOPESA',
            'airtel' => 'AIRTELMONEY',
            'halopesa' => 'HALOPESA',
            default => 'MPESA',
        };
    }

    /**
     * Generate payment reference
     */
    public function generatePaymentReference(SummitRegistration $registration)
    {
        return 'SUMMIT_' . $registration->id . '_' . Str::random(8) . '_' . time();
    }
}
