<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class Zone extends Model
{
    protected $fillable = [
        'name',
        'code',
        'conference_id',
        'description',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    /**
     * Get the conference that owns the zone.
     */
    public function conference(): BelongsTo
    {
        return $this->belongsTo(Conference::class);
    }

    /**
     * Get the branches for the zone.
     */
    public function branches(): HasMany
    {
        return $this->hasMany(Branch::class);
    }

    /**
     * Get all users in the zone through branches.
     */
    public function users(): HasManyThrough
    {
        return $this->hasManyThrough(
            User::class,
            Branch::class,
            'zone_id', // Foreign key on branches table
            'branch_id', // Foreign key on users table
            'id', // Local key on zones table
            'id' // Local key on branches table
        );
    }
}
