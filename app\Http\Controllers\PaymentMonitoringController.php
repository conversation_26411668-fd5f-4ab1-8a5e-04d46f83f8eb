<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PaymentMonitoringController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin|treasurer']);
    }

    /**
     * Real-time payment monitoring dashboard
     */
    public function dashboard()
    {
        $stats = [
            'today_payments' => Payment::whereDate('created_at', today())->count(),
            'today_revenue' => Payment::whereDate('created_at', today())->where('status', 'completed')->sum('amount'),
            'pending_payments' => Payment::where('status', 'processing')->count(),
            'failed_payments_today' => Payment::whereDate('created_at', today())->where('status', 'failed')->count(),
            'success_rate_today' => $this->calculateSuccessRate(today()),
        ];

        $recentPayments = Payment::with(['user', 'summitRegistration.summit'])
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        $paymentTrends = $this->getPaymentTrends();
        $gatewayPerformance = $this->getGatewayPerformance();

        return view('admin.payment-monitoring', compact(
            'stats', 
            'recentPayments', 
            'paymentTrends', 
            'gatewayPerformance'
        ));
    }

    /**
     * Get payments requiring manual verification
     */
    public function pendingVerification()
    {
        $pendingPayments = Payment::where('status', 'pending_verification')
            ->with(['user', 'summitRegistration.summit'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.pending-verification', compact('pendingPayments'));
    }

    /**
     * Verify manual payment
     */
    public function verifyPayment(Request $request, Payment $payment)
    {
        $request->validate([
            'action' => 'required|in:approve,reject',
            'notes' => 'nullable|string|max:500'
        ]);

        if ($request->action === 'approve') {
            $payment->update([
                'status' => 'completed',
                'paid_at' => now(),
                'admin_notes' => $request->notes
            ]);

            // Update registration
            $registration = $payment->summitRegistration;
            $registration->paid_amount += $payment->amount;
            $registration->balance = $registration->total_amount - $registration->paid_amount;
            $registration->payment_complete = $registration->balance <= 0;
            $registration->save();

            return back()->with('success', 'Payment approved successfully');
        } else {
            $payment->update([
                'status' => 'failed',
                'admin_notes' => $request->notes ?? 'Payment verification failed'
            ]);

            return back()->with('success', 'Payment rejected');
        }
    }

    /**
     * Calculate success rate for a given date
     */
    private function calculateSuccessRate(Carbon $date)
    {
        $total = Payment::whereDate('created_at', $date)->count();
        $successful = Payment::whereDate('created_at', $date)->where('status', 'completed')->count();
        
        return $total > 0 ? round(($successful / $total) * 100, 2) : 0;
    }

    /**
     * Get payment trends for the last 7 days
     */
    private function getPaymentTrends()
    {
        return Payment::selectRaw('DATE(created_at) as date, COUNT(*) as count, SUM(amount) as total')
            ->where('created_at', '>=', now()->subDays(7))
            ->groupBy('date')
            ->orderBy('date')
            ->get();
    }

    /**
     * Get gateway performance comparison
     */
    private function getGatewayPerformance()
    {
        return Payment::selectRaw('
                gateway_provider,
                COUNT(*) as total_transactions,
                SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as successful,
                SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed,
                AVG(amount) as avg_amount
            ')
            ->whereDate('created_at', '>=', now()->subDays(30))
            ->groupBy('gateway_provider')
            ->get()
            ->map(function ($item) {
                $item->success_rate = $item->total_transactions > 0 
                    ? round(($item->successful / $item->total_transactions) * 100, 2) 
                    : 0;
                return $item;
            });
    }

    /**
     * Export payment data for reconciliation
     */
    public function exportReconciliation(Request $request)
    {
        $request->validate([
            'from_date' => 'required|date',
            'to_date' => 'required|date|after_or_equal:from_date',
            'gateway' => 'nullable|in:azampay,manual,selcom'
        ]);

        $query = Payment::with(['user', 'summitRegistration.summit'])
            ->whereBetween('created_at', [$request->from_date, $request->to_date]);

        if ($request->gateway) {
            $query->where('gateway_provider', $request->gateway);
        }

        $payments = $query->get();

        $filename = 'payment_reconciliation_' . $request->from_date . '_to_' . $request->to_date . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($payments) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'Payment Reference',
                'Transaction ID',
                'User Name',
                'Summit',
                'Amount',
                'Payment Method',
                'Gateway Provider',
                'Status',
                'Created At',
                'Paid At',
                'Notes'
            ]);

            // CSV data
            foreach ($payments as $payment) {
                fputcsv($file, [
                    $payment->payment_reference,
                    $payment->transaction_id,
                    $payment->user->full_name,
                    $payment->summitRegistration->summit->title,
                    $payment->amount,
                    $payment->payment_method,
                    $payment->gateway_provider,
                    $payment->status,
                    $payment->created_at->format('Y-m-d H:i:s'),
                    $payment->paid_at?->format('Y-m-d H:i:s'),
                    $payment->admin_notes
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
