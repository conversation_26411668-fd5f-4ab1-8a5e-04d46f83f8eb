@extends(auth()->user()->hasRole('admin') ? 'layouts.admin' : 'layouts.user')

@section('title', 'Summit Reports')
@if(auth()->user()->hasRole('admin'))
@section('page-title', 'Summit Reports')
@endif

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">Summit Reports</h2>
                <p class="text-muted">Summit performance metrics and analytics.</p>
            </div>
            <a href="{{ route('admin.reports.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Reports
            </a>
        </div>
    </div>
</div>

<!-- Filter Form -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.reports.summits') }}">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="year" class="form-label">Year</label>
                            <select name="year" id="year" class="form-select">
                                <option value="">All Years</option>
                                @foreach($years as $year)
                                    <option value="{{ $year }}" {{ request('year') == $year ? 'selected' : '' }}>
                                        {{ $year }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="status" class="form-label">Status</label>
                            <select name="status" id="status" class="form-select">
                                <option value="">All Status</option>
                                <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                <option value="current" {{ request('status') == 'current' ? 'selected' : '' }}>Current</option>
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-2"></i>Apply Filters
                            </button>
                            <a href="{{ route('admin.reports.summits') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Clear Filters
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="modern-stats-card blue h-100">
            <div class="stat-icon">
                <i class="fas fa-mountain"></i>
            </div>
            <h3 class="stat-number">{{ number_format($summitStats['total_summits']) }}</h3>
            <p class="stat-label">Total Summits</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="modern-stats-card green h-100">
            <div class="stat-icon">
                <i class="fas fa-play-circle"></i>
            </div>
            <h3 class="stat-number">{{ number_format($summitStats['active_summits']) }}</h3>
            <p class="stat-label">Active Summits</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="modern-stats-card orange h-100">
            <div class="stat-icon">
                <i class="fas fa-calendar-plus"></i>
            </div>
            <h3 class="stat-number">{{ number_format($summitStats['upcoming_summits']) }}</h3>
            <p class="stat-label">Upcoming</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="modern-stats-card purple h-100">
            <div class="stat-icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <h3 class="stat-number">TSh {{ number_format($summitStats['total_revenue_all_summits']) }}</h3>
            <p class="stat-label">Total Revenue</p>
        </div>
    </div>
</div>

<!-- Summits Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>Summit Details</h5>
            </div>
            <div class="card-body">
                @if($summits->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Summit</th>
                                <th>Year</th>
                                <th>Dates</th>
                                <th>Venue</th>
                                <th>Registrations</th>
                                <th>Activities</th>
                                <th>Ministers</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($summits as $summit)
                            <tr>
                                <td>
                                    <div>
                                        <div class="fw-bold">{{ $summit->title }}</div>
                                        <small class="text-muted">{{ Str::limit($summit->theme, 30) }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ $summit->year }}</span>
                                </td>
                                <td>
                                    <div>
                                        <div>{{ $summit->start_date->format('M j') }} - {{ $summit->end_date->format('M j, Y') }}</div>
                                        <small class="text-muted">
                                            @if($summit->start_date > now())
                                                <span class="text-info">Upcoming</span>
                                            @elseif($summit->end_date < now())
                                                <span class="text-muted">Past</span>
                                            @else
                                                <span class="text-success">Ongoing</span>
                                            @endif
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-bold">{{ $summit->venue }}</div>
                                        <small class="text-muted">TSh {{ number_format($summit->registration_fee) }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <h5 class="mb-0 text-primary">{{ number_format($summit->registrations_count) }}</h5>
                                        <small class="text-muted">registrations</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <h5 class="mb-0 text-success">{{ number_format($summit->activities_count) }}</h5>
                                        <small class="text-muted">activities</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <h5 class="mb-0 text-warning">{{ number_format($summit->ministers_count) }}</h5>
                                        <small class="text-muted">ministers</small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        @if($summit->is_current)
                                            <span class="badge bg-success">Current</span>
                                        @endif
                                        @if($summit->is_active)
                                            <span class="badge bg-primary">Active</span>
                                        @else
                                            <span class="badge bg-secondary">Inactive</span>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('admin.summits.show', $summit) }}" class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.summits.edit', $summit) }}" class="btn btn-outline-secondary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $summits->withQueryString()->links() }}
                </div>
                @else
                <div class="text-center py-4">
                    <i class="fas fa-mountain fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No summits found</h5>
                    <p class="text-muted">Try adjusting your filters to see more results.</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
