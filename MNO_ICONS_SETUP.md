# MNO Provider Icons Setup Guide

## 📱 **Required PNG Icons**

You need to add the following PNG icons to the `public/images/mno/` directory:

### **Icon Files Needed:**
```
public/images/mno/
├── mpesa.png      (M-Pesa/Vodacom logo)
├── tigo.png       (Tigo Pesa logo)
├── airtel.png     (Airtel Money logo)
├── halopesa.png   (HaloPesa logo)
└── azampesa.png   (Azam Pesa logo)
```

### **Icon Specifications:**
- **Format**: PNG with transparent background
- **Size**: 200x80px (or similar aspect ratio)
- **Quality**: High resolution for crisp display
- **Background**: Transparent or white

## 🔍 **Where to Get Official Icons:**

### **1. M-<PERSON>esa (Vodacom)**
- **Official Source**: Vodacom Tanzania brand guidelines
- **Alternative**: Search "M-Pesa logo PNG transparent"
- **Colors**: Green (#00A651) and white

### **2. Tigo Pesa**
- **Official Source**: Tigo Tanzania brand assets
- **Alternative**: Search "Tigo logo PNG transparent"
- **Colors**: Blue (#0066CC) and white

### **3. Airtel Money**
- **Official Source**: Airtel Tanzania brand guidelines
- **Alternative**: Search "Airtel logo PNG transparent"
- **Colors**: Red (#E31E24) and white

### **4. HaloPesa**
- **Official Source**: Halotel Tanzania website
- **Alternative**: Search "HaloPesa logo PNG"
- **Colors**: Orange/Yellow and white

### **5. Azam Pesa**
- **Official Source**: Azam Media brand assets
- **Alternative**: Search "Azam Pesa logo PNG"
- **Colors**: Blue and white

## 📥 **How to Add Icons:**

### **Method 1: Download and Place**
1. Download the PNG icons from official sources
2. Rename them according to the list above
3. Place them in `public/images/mno/` directory
4. Refresh the payment page

### **Method 2: Create Placeholder Icons**
If you can't find official icons immediately, you can create simple text-based icons:

```html
<!-- Temporary solution until real icons are added -->
<div class="provider-icon-text">
    <strong>M-PESA</strong>
</div>
```

## 🎨 **Fallback System**

The current implementation includes a fallback system:
- **Primary**: PNG icon from `/images/mno/`
- **Fallback**: FontAwesome mobile icon with brand colors
- **Error Handling**: Automatic fallback if PNG fails to load

## ✅ **Testing Icons**

After adding icons:
1. Clear browser cache (Ctrl+F5)
2. Visit the payment page
3. Check that all provider icons display correctly
4. Test on mobile devices for responsive display

## 🔧 **Current Status**

- ✅ **Icon structure**: Ready in payment form
- ✅ **Fallback system**: FontAwesome icons as backup
- ⚠️ **PNG files**: Need to be added to `/public/images/mno/`
- ✅ **Responsive design**: Icons work on all screen sizes

## 🚀 **Next Steps**

1. **Add PNG icons** to the directory
2. **Test payment form** with real icons
3. **Verify mobile responsiveness**
4. **Update icons** if needed for better quality

**Once icons are added, the payment form will display professional MNO provider logos!**
