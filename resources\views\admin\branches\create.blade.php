@extends('layouts.admin')

@section('title', 'Add Branch')
@section('page-title', 'Add Branch')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">Add Branch</h2>
                <p class="text-muted">Create a new branch in the system.</p>
            </div>
            <a href="{{ route('admin.branches.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Branches
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-code-branch me-2"></i>Branch Information</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.branches.store') }}">
                    @csrf
                    
                    <div class="mb-3">
                        <label for="zone_id" class="form-label">Zone <span class="text-danger">*</span></label>
                        <select name="zone_id" id="zone_id" class="form-select @error('zone_id') is-invalid @enderror" required>
                            <option value="">Select Zone</option>
                            @foreach($zones as $zone)
                                <option value="{{ $zone->id }}" {{ old('zone_id') == $zone->id ? 'selected' : '' }}>
                                    {{ $zone->name }} ({{ $zone->conference->name }})
                                </option>
                            @endforeach
                        </select>
                        @error('zone_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="university_id" class="form-label">University</label>
                        <select name="university_id" id="university_id" class="form-select @error('university_id') is-invalid @enderror">
                            <option value="">Select University (Optional)</option>
                            @foreach($universities as $university)
                                <option value="{{ $university->id }}" {{ old('university_id') == $university->id ? 'selected' : '' }}>
                                    {{ $university->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('university_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="name" class="form-label">Branch Name <span class="text-danger">*</span></label>
                                <input type="text" name="name" id="name" class="form-control @error('name') is-invalid @enderror" 
                                       value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="code" class="form-label">Branch Code <span class="text-danger">*</span></label>
                                <input type="text" name="code" id="code" class="form-control @error('code') is-invalid @enderror" 
                                       value="{{ old('code') }}" placeholder="e.g., UDSM-DSM" required>
                                @error('code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea name="description" id="description" class="form-control @error('description') is-invalid @enderror" 
                                  rows="4" placeholder="Brief description of the branch...">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" name="is_active" id="is_active" class="form-check-input" 
                                   value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                            <label for="is_active" class="form-check-label">
                                Active Branch
                            </label>
                        </div>
                        <small class="text-muted">Inactive branches won't be available for selection.</small>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Branch
                        </button>
                        <a href="{{ route('admin.branches.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Guidelines</h5>
            </div>
            <div class="card-body">
                <h6>Zone</h6>
                <p class="text-muted small">Select the zone this branch belongs to.</p>
                
                <h6>University</h6>
                <p class="text-muted small">Optionally associate this branch with a specific university.</p>
                
                <h6>Branch Code</h6>
                <p class="text-muted small">Use a unique code that identifies the branch, often combining university and location codes.</p>
                
                <h6>Status</h6>
                <p class="text-muted small">Only active branches will be available for user registration.</p>
            </div>
        </div>
    </div>
</div>
@endsection
