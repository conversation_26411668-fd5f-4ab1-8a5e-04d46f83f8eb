<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->unsignedBigInteger('admin_conference_id')->nullable()->after('branch_id');
            $table->unsignedBigInteger('admin_zone_id')->nullable()->after('admin_conference_id');
            $table->unsignedBigInteger('admin_branch_id')->nullable()->after('admin_zone_id');
            
            $table->foreign('admin_conference_id')->references('id')->on('conferences')->onDelete('set null');
            $table->foreign('admin_zone_id')->references('id')->on('zones')->onDelete('set null');
            $table->foreign('admin_branch_id')->references('id')->on('branches')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['admin_conference_id']);
            $table->dropForeign(['admin_zone_id']);
            $table->dropForeign(['admin_branch_id']);
            
            $table->dropColumn(['admin_conference_id', 'admin_zone_id', 'admin_branch_id']);
        });
    }
};
