@extends('layouts.user')

@section('title', 'Make Payment')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">Make Payment</h2>
                <p class="text-muted">{{ $registration->summit->title }} ({{ $registration->summit->year }})</p>
            </div>
            <div>
                <a href="{{ route('registrations.show', $registration) }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Registration
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Payment Summary -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Payment Details</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('payments.store', $registration) }}" id="paymentForm">
                    @csrf
                    
                    <!-- Payment Amount -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="amount" class="form-label">Payment Amount (TZS) <span class="text-danger">*</span></label>
                            <input type="number" 
                                   class="form-control @error('amount') is-invalid @enderror" 
                                   id="amount" 
                                   name="amount" 
                                   min="1000" 
                                   max="{{ $registration->balance }}" 
                                   value="{{ old('amount', $registration->balance) }}" 
                                   required>
                            <div class="form-text">
                                Minimum: TZS 1,000 | Maximum: TZS {{ number_format($registration->balance, 0) }}
                            </div>
                            @error('amount')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Outstanding Balance</label>
                            <div class="form-control-plaintext">
                                <strong class="text-danger">TZS {{ number_format($registration->balance, 2) }}</strong>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Method Selection -->
                    <div class="mb-4">
                        <label class="form-label">Choose Payment Method</label>
                        <div class="row">
                            <!-- Selcom Mobile Money -->
                            <div class="col-lg-6 mb-3">
                                <input type="radio" class="btn-check" name="payment_method" id="selcom" value="selcom" checked>
                                <label class="btn btn-outline-primary w-100 h-100" for="selcom">
                                    <div class="text-center p-3">
                                        <i class="fas fa-mobile-alt fa-2x mb-2"></i>
                                        <h6>Mobile Money (Instant)</h6>
                                        <small class="text-muted">M-Pesa, Tigo Pesa, Airtel Money, Halopesa</small>
                                        <div class="badge bg-success mt-2">Recommended</div>
                                    </div>
                                </label>
                            </div>

                            <!-- Bank Transfer -->
                            <div class="col-lg-6 mb-3">
                                <input type="radio" class="btn-check" name="payment_method" id="bank_transfer" value="bank_transfer">
                                <label class="btn btn-outline-success w-100 h-100" for="bank_transfer">
                                    <div class="text-center p-3">
                                        <i class="fas fa-university fa-2x mb-2"></i>
                                        <h6>Bank Transfer</h6>
                                        <small class="text-muted">Transfer to our bank account</small>
                                        <div class="badge bg-warning mt-2">24-48 hrs</div>
                                    </div>
                                </label>
                            </div>

                            <!-- Cash Payment -->
                            <div class="col-lg-6 mb-3">
                                <input type="radio" class="btn-check" name="payment_method" id="cash" value="cash">
                                <label class="btn btn-outline-warning w-100 h-100" for="cash">
                                    <div class="text-center p-3">
                                        <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                                        <h6>Cash Payment</h6>
                                        <small class="text-muted">Pay at our office</small>
                                        <div class="badge bg-info mt-2">In-person</div>
                                    </div>
                                </label>
                            </div>

                            <!-- Direct Mobile Money -->
                            <div class="col-lg-6 mb-3">
                                <input type="radio" class="btn-check" name="payment_method" id="mobile_money_direct" value="mobile_money_direct">
                                <label class="btn btn-outline-info w-100 h-100" for="mobile_money_direct">
                                    <div class="text-center p-3">
                                        <i class="fas fa-phone fa-2x mb-2"></i>
                                        <h6>Direct Mobile Money</h6>
                                        <small class="text-muted">Send directly to our accounts</small>
                                        <div class="badge bg-secondary mt-2">Manual verify</div>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile Network Operator Selection (for Selcom) -->
                    <div class="mb-4" id="selcom-options">
                        <label class="form-label">Select Mobile Money Provider <span class="text-danger">*</span></label>
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <div class="card payment-method-card" data-provider="vodacom">
                                    <div class="card-body text-center">
                                        <div class="provider-logo mb-2">
                                            <i class="fas fa-mobile-alt fa-2x text-danger"></i>
                                        </div>
                                        <h6 class="card-title">M-Pesa</h6>
                                        <small class="text-muted">Vodacom</small>
                                        <input type="radio" name="mno_provider" value="vodacom" class="d-none provider-radio" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card payment-method-card" data-provider="tigo">
                                    <div class="card-body text-center">
                                        <div class="provider-logo mb-2">
                                            <i class="fas fa-mobile-alt fa-2x text-primary"></i>
                                        </div>
                                        <h6 class="card-title">Tigo Pesa</h6>
                                        <small class="text-muted">Tigo</small>
                                        <input type="radio" name="mno_provider" value="tigo" class="d-none provider-radio" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card payment-method-card" data-provider="airtel">
                                    <div class="card-body text-center">
                                        <div class="provider-logo mb-2">
                                            <i class="fas fa-mobile-alt fa-2x text-warning"></i>
                                        </div>
                                        <h6 class="card-title">Airtel Money</h6>
                                        <small class="text-muted">Airtel</small>
                                        <input type="radio" name="mno_provider" value="airtel" class="d-none provider-radio" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card payment-method-card" data-provider="halopesa">
                                    <div class="card-body text-center">
                                        <div class="provider-logo mb-2">
                                            <i class="fas fa-mobile-alt fa-2x text-success"></i>
                                        </div>
                                        <h6 class="card-title">HaloPesa</h6>
                                        <small class="text-muted">Halotel</small>
                                        <input type="radio" name="mno_provider" value="halopesa" class="d-none provider-radio" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @error('mno_provider')
                            <div class="text-danger small">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Phone Number -->
                    <div class="mb-4">
                        <label for="phone_number" class="form-label">Mobile Phone Number <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">+255</span>
                            <input type="tel" 
                                   class="form-control @error('phone_number') is-invalid @enderror" 
                                   id="phone_number" 
                                   name="phone_number" 
                                   placeholder="712345678" 
                                   value="{{ old('phone_number', substr(auth()->user()->phone ?? '', -9)) }}" 
                                   pattern="[67]\d{8}" 
                                   maxlength="9"
                                   required>
                        </div>
                        <div class="form-text">
                            Enter your mobile number without the country code (e.g., 712345678)
                        </div>
                        @error('phone_number')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Hidden payment method field -->
                    <input type="hidden" name="payment_method" value="selcom">

                    <!-- Payment Instructions -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Payment Instructions</h6>
                        <ol class="mb-0">
                            <li>Select your mobile money provider above</li>
                            <li>Enter your mobile phone number</li>
                            <li>Click "Proceed to Payment" below</li>
                            <li>You will receive a USSD prompt on your phone</li>
                            <li>Enter your mobile money PIN to complete the payment</li>
                            <li>You will receive a confirmation SMS upon successful payment</li>
                        </ol>
                    </div>

                    <!-- Bank Transfer Form -->
                    <div id="bank-transfer-form" style="display: none;">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Bank Transfer Instructions</h6>
                            <p class="mb-2">Transfer money to our bank account:</p>
                            <ul class="mb-2">
                                <li><strong>Bank:</strong> CRDB Bank</li>
                                <li><strong>Account Name:</strong> TUCASA STU</li>
                                <li><strong>Account Number:</strong> 0150-*********</li>
                                <li><strong>Reference:</strong> {{ $registration->registration_number }}</li>
                            </ul>
                            <small class="text-muted">After transfer, enter details below for verification.</small>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Bank Name</label>
                                <input type="text" class="form-control" name="bank_name" placeholder="e.g., CRDB Bank">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Your Account Number</label>
                                <input type="text" class="form-control" name="account_number" placeholder="Your account number">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Transfer Reference</label>
                                <input type="text" class="form-control" name="reference_number" placeholder="Bank reference number">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Transfer Date</label>
                                <input type="date" class="form-control" name="transfer_date" value="{{ date('Y-m-d') }}">
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success btn-lg" id="submitBtn">
                            <i class="fas fa-credit-card me-2"></i>
                            <span id="submit-text">Proceed to Payment</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Payment Summary Sidebar -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>Payment Summary</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Summit:</strong></td>
                        <td>{{ $registration->summit->title }}</td>
                    </tr>
                    <tr>
                        <td><strong>Year:</strong></td>
                        <td>{{ $registration->summit->year }}</td>
                    </tr>
                    <tr>
                        <td><strong>Registration Fee:</strong></td>
                        <td>TZS {{ number_format($registration->total_amount, 2) }}</td>
                    </tr>
                    <tr>
                        <td><strong>Amount Paid:</strong></td>
                        <td class="text-success">TZS {{ number_format($registration->paid_amount, 2) }}</td>
                    </tr>
                    <tr class="border-top">
                        <td><strong>Outstanding Balance:</strong></td>
                        <td><strong class="text-danger">TZS {{ number_format($registration->balance, 2) }}</strong></td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Security Notice -->
        <div class="card mt-3">
            <div class="card-body">
                <h6><i class="fas fa-shield-alt text-success me-2"></i>Secure Payment</h6>
                <p class="small text-muted mb-0">
                    Your payment is processed securely through Selcom Payment Gateway. 
                    We do not store your mobile money PIN or any sensitive payment information.
                </p>
            </div>
        </div>
    </div>
</div>

<style>
.payment-method-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
}

.payment-method-card:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.payment-method-card.selected {
    border-color: #28a745;
    background-color: #f8fff9;
}

.payment-method-card.selected .card-title {
    color: #28a745;
}

#submitBtn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentCards = document.querySelectorAll('.payment-method-card');
    const providerRadios = document.querySelectorAll('.provider-radio');
    const submitBtn = document.getElementById('submitBtn');
    const paymentForm = document.getElementById('paymentForm');
    const submitText = document.getElementById('submit-text');

    // Handle payment method selection
    document.querySelectorAll('input[name="payment_method"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const method = this.value;

            // Hide all method-specific forms
            document.getElementById('selcom-options').style.display = 'none';
            const bankForm = document.getElementById('bank-transfer-form');
            if (bankForm) bankForm.style.display = 'none';

            // Update submit button text
            switch(method) {
                case 'selcom':
                    document.getElementById('selcom-options').style.display = 'block';
                    submitText.textContent = 'Proceed to Payment';
                    break;
                case 'bank_transfer':
                    if (bankForm) bankForm.style.display = 'block';
                    submitText.textContent = 'Submit Bank Transfer Details';
                    break;
                case 'cash':
                    submitText.textContent = 'Submit Cash Payment Details';
                    break;
                case 'mobile_money_direct':
                    submitText.textContent = 'Submit Mobile Money Details';
                    break;
            }
        });
    });

    // Initialize with default selection
    const defaultMethod = document.querySelector('input[name="payment_method"]:checked');
    if (defaultMethod) {
        defaultMethod.dispatchEvent(new Event('change'));
    }

    // Handle payment method selection
    paymentCards.forEach(card => {
        card.addEventListener('click', function() {
            const provider = this.dataset.provider;
            const radio = this.querySelector('.provider-radio');
            
            // Remove selected class from all cards
            paymentCards.forEach(c => c.classList.remove('selected'));
            
            // Add selected class to clicked card
            this.classList.add('selected');
            
            // Check the radio button
            radio.checked = true;
        });
    });

    // Handle form submission
    paymentForm.addEventListener('submit', function(e) {
        const selectedProvider = document.querySelector('.provider-radio:checked');
        
        if (!selectedProvider) {
            e.preventDefault();
            alert('Please select a mobile money provider');
            return;
        }

        // Disable submit button to prevent double submission
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    });

    // Phone number formatting
    const phoneInput = document.getElementById('phone_number');
    phoneInput.addEventListener('input', function() {
        // Remove any non-digit characters
        this.value = this.value.replace(/\D/g, '');
        
        // Limit to 9 digits
        if (this.value.length > 9) {
            this.value = this.value.slice(0, 9);
        }
    });
});
</script>
@endsection
