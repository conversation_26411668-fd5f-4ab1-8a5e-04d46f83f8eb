@extends('layouts.app')

@section('title', 'Treasurer Dashboard')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">💰 Treasurer Dashboard</h2>
                    <p class="text-muted mb-0">Financial overview and payment management</p>
                </div>
                <div>
                    <span class="badge bg-success fs-6">{{ Auth::user()->roles->first()->name ?? 'Treasurer' }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100" style="background: #e6f4ea; border: none;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: rgba(40, 167, 69, 0.1);">
                                <i class="fas fa-money-bill-wave fa-2x text-success"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Total Revenue</h6>
                            <h3 class="mb-0">{{ number_format($stats['total_revenue'] ?? 0, 0) }} TZS</h3>
                            <small class="text-success">
                                <i class="fas fa-arrow-up"></i> All time
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100" style="background: #fff7e6; border: none;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: rgba(255, 193, 7, 0.1);">
                                <i class="fas fa-credit-card fa-2x text-warning"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Completed Payments</h6>
                            <h3 class="mb-0">{{ $stats['completed_payments'] ?? 0 }}</h3>
                            <small class="text-warning">
                                <i class="fas fa-check-circle"></i> Successful
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100" style="background: #e6f0ff; border: none;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: rgba(0, 123, 255, 0.1);">
                                <i class="fas fa-clock fa-2x text-primary"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Pending Payments</h6>
                            <h3 class="mb-0">{{ $stats['pending_payments'] ?? 0 }}</h3>
                            <small class="text-primary">
                                <i class="fas fa-hourglass-half"></i> Processing
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100" style="background: #ffe6e6; border: none;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: rgba(220, 53, 69, 0.1);">
                                <i class="fas fa-times-circle fa-2x text-danger"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Failed Payments</h6>
                            <h3 class="mb-0">{{ $stats['failed_payments'] ?? 0 }}</h3>
                            <small class="text-danger">
                                <i class="fas fa-exclamation-triangle"></i> Requires attention
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Payments and Branch Performance -->
    <div class="row">
        <!-- Recent Payments -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Recent Payments
                    </h5>
                    <a href="{{ route('admin.payments.index') }}" class="btn btn-sm btn-outline-primary">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    @if($recentPayments && $recentPayments->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentPayments as $payment)
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ $payment->user->full_name }}</strong><br>
                                                <small class="text-muted">{{ $payment->user->email }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <strong>{{ number_format($payment->amount, 0) }} TZS</strong>
                                        </td>
                                        <td>
                                            @if($payment->status === 'completed')
                                                <span class="badge bg-success">Completed</span>
                                            @elseif($payment->status === 'pending')
                                                <span class="badge bg-warning">Pending</span>
                                            @elseif($payment->status === 'failed')
                                                <span class="badge bg-danger">Failed</span>
                                            @else
                                                <span class="badge bg-secondary">{{ ucfirst($payment->status) }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small>{{ $payment->created_at->format('M j, Y H:i') }}</small>
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.payments.show', $payment) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No recent payments found.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Payment by Branch -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Payments by Branch
                    </h5>
                </div>
                <div class="card-body">
                    @if($paymentsByBranch && $paymentsByBranch->count() > 0)
                        @foreach($paymentsByBranch as $branch)
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong>{{ Str::limit($branch->name, 20) }}</strong><br>
                                <small class="text-muted">{{ $branch->count }} payments</small>
                            </div>
                            <div class="text-end">
                                <strong>{{ number_format($branch->total, 0) }} TZS</strong>
                            </div>
                        </div>
                        @if(!$loop->last)
                            <hr class="my-2">
                        @endif
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No payment data available.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tools me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('treasurer.payments.index') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-list me-2"></i>View All Payments
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('treasurer.payment-reports') }}" class="btn btn-outline-success w-100">
                                <i class="fas fa-chart-bar me-2"></i>Payment Reports
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('treasurer.registrations.index') }}" class="btn btn-outline-info w-100">
                                <i class="fas fa-users me-2"></i>View Registrations
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('admin.reports.analytics') }}" class="btn btn-outline-warning w-100">
                                <i class="fas fa-analytics me-2"></i>Analytics
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .card {
        transition: transform 0.2s, box-shadow 0.2s;
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    .table th {
        border-top: none;
        font-weight: 600;
        color: #6c757d;
        font-size: 0.875rem;
    }
    
    .badge {
        font-size: 0.75rem;
    }
</style>
@endpush
