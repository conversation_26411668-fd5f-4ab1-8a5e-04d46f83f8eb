@extends('layouts.admin')

@section('title', 'Add Summit')
@section('page-title', 'Add Summit')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">Add Summit</h2>
                <p class="text-muted">Create a new summit event in the system.</p>
            </div>
            <a href="{{ route('admin.summits.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Summits
            </a>
        </div>
    </div>
</div>

<form method="POST" action="{{ route('admin.summits.store') }}" enctype="multipart/form-data">
    @csrf
    
    <div class="row">
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="payment-details-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-mountain me-2"></i>Basic Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="title" class="form-label">Summit Title <span class="text-danger">*</span></label>
                        <input type="text" name="title" id="title" class="form-control @error('title') is-invalid @enderror" 
                               value="{{ old('title') }}" placeholder="e.g., Annual Youth Summit 2024" required>
                        @error('title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="theme" class="form-label">Summit Theme <span class="text-danger">*</span></label>
                        <input type="text" name="theme" id="theme" class="form-control @error('theme') is-invalid @enderror" 
                               value="{{ old('theme') }}" placeholder="e.g., Building Tomorrow's Leaders" required>
                        @error('theme')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                        <textarea name="description" id="description" class="form-control @error('description') is-invalid @enderror" 
                                  rows="4" placeholder="Detailed description of the summit..." required>{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="year" class="form-label">Year <span class="text-danger">*</span></label>
                                <select name="year" id="year" class="form-select @error('year') is-invalid @enderror" required>
                                    <option value="">Select Year</option>
                                    @for($i = date('Y'); $i <= date('Y') + 5; $i++)
                                        <option value="{{ $i }}" {{ old('year', date('Y')) == $i ? 'selected' : '' }}>{{ $i }}</option>
                                    @endfor
                                </select>
                                @error('year')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="registration_fee" class="form-label">Registration Fee (TZS) <span class="text-danger">*</span></label>
                                <input type="number" name="registration_fee" id="registration_fee" 
                                       class="form-control @error('registration_fee') is-invalid @enderror" 
                                       value="{{ old('registration_fee') }}" min="0" step="0.01" placeholder="50000" required>
                                @error('registration_fee')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Date & Time Information -->
            <div class="payment-details-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-calendar me-2"></i>Date & Time Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_date" class="form-label">Start Date <span class="text-danger">*</span></label>
                                <input type="date" name="start_date" id="start_date" 
                                       class="form-control @error('start_date') is-invalid @enderror" 
                                       value="{{ old('start_date') }}" required>
                                @error('start_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end_date" class="form-label">End Date <span class="text-danger">*</span></label>
                                <input type="date" name="end_date" id="end_date" 
                                       class="form-control @error('end_date') is-invalid @enderror" 
                                       value="{{ old('end_date') }}" required>
                                @error('end_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="registration_deadline" class="form-label">Registration Deadline <span class="text-danger">*</span></label>
                        <input type="date" name="registration_deadline" id="registration_deadline" 
                               class="form-control @error('registration_deadline') is-invalid @enderror" 
                               value="{{ old('registration_deadline') }}" required>
                        @error('registration_deadline')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">Registration must close before the summit start date.</small>
                    </div>
                </div>
            </div>

            <!-- Venue Information -->
            <div class="payment-details-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>Venue Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="venue" class="form-label">Venue Name <span class="text-danger">*</span></label>
                        <input type="text" name="venue" id="venue" class="form-control @error('venue') is-invalid @enderror" 
                               value="{{ old('venue') }}" placeholder="e.g., Mlimani Conference Center" required>
                        @error('venue')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="venue_address" class="form-label">Venue Address <span class="text-danger">*</span></label>
                        <textarea name="venue_address" id="venue_address" class="form-control @error('venue_address') is-invalid @enderror" 
                                  rows="3" placeholder="Full venue address with directions..." required>{{ old('venue_address') }}</textarea>
                        @error('venue_address')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Media & Images -->
            <div class="payment-details-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-images me-2"></i>Media & Images</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="poster_image" class="form-label">Poster Image</label>
                        <input type="file" name="poster_image" id="poster_image" 
                               class="form-control @error('poster_image') is-invalid @enderror" 
                               accept="image/*">
                        @error('poster_image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">Maximum file size: 5MB. Supported formats: JPEG, PNG, JPG, GIF</small>
                    </div>

                    <div class="mb-3">
                        <label for="gallery_images" class="form-label">Gallery Images</label>
                        <input type="file" name="gallery_images[]" id="gallery_images" 
                               class="form-control @error('gallery_images.*') is-invalid @enderror" 
                               accept="image/*" multiple>
                        @error('gallery_images.*')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">You can select multiple images. Maximum 5MB per image.</small>
                    </div>
                </div>
            </div>

            <!-- Status Settings -->
            <div class="payment-details-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Status Settings</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input type="checkbox" name="is_active" id="is_active" class="form-check-input" 
                                       value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                <label for="is_active" class="form-check-label">
                                    <strong>Active Summit</strong>
                                </label>
                                <div class="text-muted small">Active summits are visible to users for registration.</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input type="checkbox" name="is_current" id="is_current" class="form-check-input" 
                                       value="1" {{ old('is_current') ? 'checked' : '' }}>
                                <label for="is_current" class="form-check-label">
                                    <strong>Current Summit</strong>
                                </label>
                                <div class="text-muted small">Mark as the current/featured summit on the homepage.</div>
                            </div>
                        </div>
                    </div>

                    <div class="payment-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Summit
                        </button>
                        <a href="{{ route('admin.summits.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="payment-details-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Guidelines</h5>
                </div>
                <div class="card-body">
                    <h6>Summit Title & Theme</h6>
                    <p class="text-muted small">Choose a compelling title and theme that reflects the summit's purpose and goals.</p>
                    
                    <h6>Date Planning</h6>
                    <p class="text-muted small">Ensure registration deadline is before the start date. Allow sufficient time for planning and promotion.</p>
                    
                    <h6>Registration Fee</h6>
                    <p class="text-muted small">Set an appropriate fee that covers costs while remaining accessible to participants.</p>
                    
                    <h6>Venue Details</h6>
                    <p class="text-muted small">Provide complete venue information including address and directions for easy access.</p>
                    
                    <h6>Images</h6>
                    <p class="text-muted small">Upload high-quality images for better presentation. Poster image will be featured prominently.</p>
                    
                    <h6>Status Settings</h6>
                    <p class="text-muted small">Only one summit should be marked as "current" at a time. This will be featured on the homepage.</p>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set minimum date for start_date to tomorrow
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');
    const registrationDeadline = document.getElementById('registration_deadline');
    
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];
    
    startDate.min = tomorrowStr;
    
    // Update end date minimum when start date changes
    startDate.addEventListener('change', function() {
        endDate.min = this.value;
        registrationDeadline.max = this.value;
        
        // Clear end date if it's before start date
        if (endDate.value && endDate.value <= this.value) {
            endDate.value = '';
        }
        
        // Clear registration deadline if it's after start date
        if (registrationDeadline.value && registrationDeadline.value >= this.value) {
            registrationDeadline.value = '';
        }
    });
    
    // Set registration deadline maximum to start date
    registrationDeadline.addEventListener('change', function() {
        if (startDate.value && this.value >= startDate.value) {
            alert('Registration deadline must be before the summit start date.');
            this.value = '';
        }
    });
});
</script>
@endsection
