<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\IdCardService;
use Illuminate\Support\Facades\Storage;

class IdCardTest extends TestCase
{
    protected $idCardService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->idCardService = new IdCardService();

        // Fake the storage for testing
        Storage::fake('public');
    }

    public function test_it_can_generate_qr_code_with_proper_size()
    {
        // Generate QR code data
        $cardNumber = 'TEST-' . time();
        $qrData = json_encode([
            'card_number' => $cardNumber,
            'user_id' => 1,
            'summit_id' => 1,
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Generate QR code
        $qrCodePath = $this->idCardService->generateQrCodeFromData($qrData, $cardNumber);

        // Assert QR code file was created
        $this->assertTrue(Storage::disk('public')->exists($qrCodePath));

        // Check if it's a PNG file (not fallback text file)
        $this->assertStringEndsWith('.png', $qrCodePath);

        // Verify file content is not empty
        $fileContent = Storage::disk('public')->get($qrCodePath);
        $this->assertNotEmpty($fileContent);

        // Verify it's actually a PNG file by checking magic bytes
        $this->assertStringStartsWith("\x89PNG", $fileContent);
    }

    public function test_qr_code_data_structure_is_valid()
    {
        $cardNumber = 'TEST-' . time();
        $testData = [
            'card_number' => $cardNumber,
            'user_id' => 1,
            'summit_id' => 1,
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'branch' => 'Test Branch',
            'conference' => 'Test Conference',
            'issued_at' => now()->toISOString(),
            'verification_url' => 'https://example.com/verify/' . $cardNumber
        ];

        $qrData = json_encode($testData);
        $decodedData = json_decode($qrData, true);

        // Assert required fields are present
        $this->assertArrayHasKey('card_number', $decodedData);
        $this->assertArrayHasKey('user_id', $decodedData);
        $this->assertArrayHasKey('summit_id', $decodedData);
        $this->assertArrayHasKey('name', $decodedData);
        $this->assertArrayHasKey('email', $decodedData);
        $this->assertArrayHasKey('verification_url', $decodedData);

        // Assert values are correct
        $this->assertEquals($cardNumber, $decodedData['card_number']);
        $this->assertEquals(1, $decodedData['user_id']);
        $this->assertEquals(1, $decodedData['summit_id']);
        $this->assertEquals('Test User', $decodedData['name']);
        $this->assertEquals('<EMAIL>', $decodedData['email']);

        // Assert verification URL contains card number
        $this->assertStringContainsString($cardNumber, $decodedData['verification_url']);
    }

    public function test_qr_code_size_is_adequate_for_scanning()
    {
        $cardNumber = 'TEST-SIZE-' . time();
        $qrData = json_encode(['test' => 'data', 'card_number' => $cardNumber]);

        // Generate QR code
        $qrCodePath = $this->idCardService->generateQrCodeFromData($qrData, $cardNumber);

        // Get file content and check size
        $fileContent = Storage::disk('public')->get($qrCodePath);
        $fileSize = strlen($fileContent);

        // QR code should be reasonably large (at least 5KB for 800px size)
        $this->assertGreaterThan(5000, $fileSize, 'QR code file should be large enough for good scanning quality');

        // Verify it's a PNG file
        $this->assertStringStartsWith("\x89PNG", $fileContent);
    }
}
