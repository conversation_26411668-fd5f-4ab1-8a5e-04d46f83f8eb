@extends('layouts.admin')

@section('title', 'Branch Details')
@section('page-title', 'Branch Details')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">{{ $branch->name }}</h2>
                <p class="text-muted">Branch details and statistics.</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('admin.branches.edit', $branch) }}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>Edit Branch
                </a>
                <a href="{{ route('admin.branches.index') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Branches
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Branch Information -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-code-branch me-2"></i>Branch Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Name:</strong></td>
                                <td>{{ $branch->name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Code:</strong></td>
                                <td><span class="badge bg-primary">{{ $branch->code }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Zone:</strong></td>
                                <td>{{ $branch->zone->name ?? 'N/A' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Conference:</strong></td>
                                <td>{{ $branch->zone->conference->name ?? 'N/A' }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>University:</strong></td>
                                <td>{{ $branch->university->name ?? 'Not specified' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Total Users:</strong></td>
                                <td><span class="badge bg-info">{{ $branch->users->count() }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    <span class="badge bg-{{ $branch->is_active ? 'success' : 'secondary' }}">
                                        {{ $branch->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Created:</strong></td>
                                <td>{{ $branch->created_at->format('F j, Y') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                @if($branch->description)
                <div class="mt-3">
                    <h6>Description</h6>
                    <p class="text-muted">{{ $branch->description }}</p>
                </div>
                @endif
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Quick Stats</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Active Users</span>
                    <span class="badge bg-success">{{ $branch->users->where('is_active', true)->count() }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Inactive Users</span>
                    <span class="badge bg-secondary">{{ $branch->users->where('is_active', false)->count() }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Last Updated</span>
                    <span class="text-muted">{{ $branch->updated_at->format('M j, Y') }}</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Users -->
@if($branch->users->count() > 0)
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-users me-2"></i>Branch Users ({{ $branch->users->count() }})</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Email</th>
                                <th>University</th>
                                <th>Course</th>
                                <th>Status</th>
                                <th>Joined</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($branch->users->take(10) as $user)
                            <tr>
                                <td>
                                    <div class="fw-bold">{{ $user->full_name }}</div>
                                    @if($user->student_id)
                                        <small class="text-muted">ID: {{ $user->student_id }}</small>
                                    @endif
                                </td>
                                <td>{{ $user->email }}</td>
                                <td>{{ $user->university->name ?? 'N/A' }}</td>
                                <td>{{ $user->course ?? 'N/A' }}</td>
                                <td>
                                    <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }}">
                                        {{ $user->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td>{{ $user->created_at->format('M j, Y') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('admin.users.show', $user) }}" 
                                           class="btn btn-outline-primary" title="View User">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.users.edit', $user) }}" 
                                           class="btn btn-outline-secondary" title="Edit User">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                @if($branch->users->count() > 10)
                <div class="text-center mt-3">
                    <a href="{{ route('admin.users.index', ['branch_id' => $branch->id]) }}" class="btn btn-outline-primary">
                        <i class="fas fa-users me-2"></i>View All Users ({{ $branch->users->count() }})
                    </a>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endif
@endsection
