<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Conference;
use App\Models\Zone;
use App\Models\Branch;
use App\Models\University;
use App\Models\Summit;
use App\Models\SummitRegistration;
use App\Models\Payment;
use Carbon\Carbon;

class DemoDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get Kevin Mranda
        $kevin = User::where('email', '<EMAIL>')->first();
        
        if (!$kevin) {
            $this->command->error('<PERSON> user not found!');
            return;
        }

        // Create demo conference if not exists
        $conference = Conference::firstOrCreate([
            'name' => 'Tanzania Universities Christian Association'
        ], [
            'code' => 'TUCASA',
            'description' => 'Tanzania Universities Christian Association bringing together Christian students from universities across Tanzania.',
            'is_active' => true
        ]);

        // Create demo zone if not exists
        $zone = Zone::firstOrCreate([
            'name' => 'Central Zone',
            'conference_id' => $conference->id
        ], [
            'code' => 'CZ',
            'description' => 'Central Zone covering universities in the central region of Tanzania.',
            'is_active' => true
        ]);

        // Create demo university if not exists
        $university = University::firstOrCreate([
            'name' => 'University of Dar es Salaam'
        ], [
            'code' => 'UDSM',
            'location' => 'Dar es Salaam',
            'website' => 'https://www.udsm.ac.tz',
            'is_active' => true
        ]);

        // Create demo branch if not exists
        $branch = Branch::firstOrCreate([
            'name' => 'UDSM Main Campus',
            'zone_id' => $zone->id
        ], [
            'code' => 'UDSM-MAIN',
            'university_id' => $university->id,
            'description' => 'Main campus branch of University of Dar es Salaam.',
            'is_active' => true
        ]);

        // Update Kevin's profile with branch and university
        $kevin->update([
            'branch_id' => $branch->id,
            'university_id' => $university->id,
            'student_id' => 'UDSM/2021/04567',
            'year_of_study' => 3,
            'course' => 'Computer Science and Engineering',
            'phone' => '+255 123 456 789'
        ]);

        // Assign user role to Kevin if not already assigned
        if (!$kevin->hasRole('user')) {
            $kevin->assignRole('user');
        }

        // Create demo summit
        $summit = Summit::firstOrCreate([
            'title' => 'TUCASA Annual Summit 2026'
        ], [
            'year' => 2024,
            'theme' => 'Faith, Unity, and Academic Excellence',
            'description' => 'Join us for the annual TUCASA summit bringing together Christian students from universities across Tanzania for fellowship, learning, and spiritual growth.',
            'venue' => 'Mlimani Conference Center',
            'venue_address' => 'University of Dar es Salaam, Dar es Salaam, Tanzania',
            'start_date' => Carbon::create(2026, 12, 15),
            'end_date' => Carbon::create(2026, 12, 18),
            'registration_fee' => 50000, // TSh 50,000
            'is_active' => true,
            'is_current' => true,
            'registration_deadline' => Carbon::create(2026, 12, 10)
        ]);

        // Create summit registration for Kevin
        $registration = SummitRegistration::firstOrCreate([
            'user_id' => $kevin->id,
            'summit_id' => $summit->id
        ], [
            'registration_number' => 'TUCASA-2026-' . str_pad($kevin->id, 4, '0', STR_PAD_LEFT),
            'status' => 'confirmed',
            'total_amount' => $summit->registration_fee,
            'paid_amount' => $summit->registration_fee,
            'balance' => 0,
            'payment_complete' => true,
            'registered_at' => now()
        ]);

        // Create payment record for Kevin
        $payment = Payment::firstOrCreate([
            'user_id' => $kevin->id,
            'summit_registration_id' => $registration->id
        ], [
            'payment_reference' => 'PAY-' . strtoupper(uniqid()),
            'amount' => $summit->registration_fee,
            'payment_method' => 'mobile_money',
            'transaction_id' => 'TXN-' . strtoupper(uniqid()),
            'status' => 'completed',
            'paid_at' => now(),
            'receipt_number' => 'RCP-' . rand(100000, 999999),
            'gateway_data' => json_encode([
                'phone' => '+255 123 456 789',
                'provider' => 'M-Pesa',
                'reference' => 'MP' . rand(100000, 999999)
            ])
        ]);

        $this->command->info('Demo data created successfully!');
        $this->command->info('Kevin Mranda details:');
        $this->command->info('- User ID: ' . $kevin->id);
        $this->command->info('- Email: ' . $kevin->email);
        $this->command->info('- Branch: ' . $branch->name);
        $this->command->info('- University: ' . $university->name);
        $this->command->info('- Summit: ' . $summit->title);
        $this->command->info('- Registration: ' . $registration->registration_number);
        $this->command->info('- Payment: ' . $payment->payment_reference . ' (TSh ' . number_format($payment->amount) . ')');
        $this->command->info('- Payment Status: ' . $payment->status);
    }
}
