<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Summit;
use App\Models\Conference;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PaymentController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * Display a listing of payments.
     */
    public function index(Request $request)
    {
        $query = Payment::with(['user.branch.zone.conference', 'summitRegistration.summit']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        if ($request->filled('summit_id')) {
            $query->whereHas('summitRegistration', function ($q) use ($request) {
                $q->where('summit_id', $request->summit_id);
            });
        }

        if ($request->filled('conference_id')) {
            $query->whereHas('user.branch.zone', function ($q) use ($request) {
                $q->where('conference_id', $request->conference_id);
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('payment_reference', 'like', "%{$search}%")
                  ->orWhere('transaction_id', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('first_name', 'like', "%{$search}%")
                               ->orWhere('last_name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        $payments = $query->orderBy('created_at', 'desc')->paginate(20);

        // Create separate query for statistics to avoid conflicts
        $statsQuery = Payment::with(['user.branch.zone.conference', 'summitRegistration.summit']);

        // Apply same filters to stats query
        if ($request->filled('status')) {
            $statsQuery->where('status', $request->status);
        }

        if ($request->filled('payment_method')) {
            $statsQuery->where('payment_method', $request->payment_method);
        }

        if ($request->filled('summit_id')) {
            $statsQuery->whereHas('summitRegistration', function ($q) use ($request) {
                $q->where('summit_id', $request->summit_id);
            });
        }

        if ($request->filled('conference_id')) {
            $statsQuery->whereHas('user.branch.zone', function ($q) use ($request) {
                $q->where('conference_id', $request->conference_id);
            });
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $statsQuery->where(function ($q) use ($search) {
                $q->where('payment_reference', 'like', "%{$search}%")
                  ->orWhere('transaction_id', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('first_name', 'like', "%{$search}%")
                               ->orWhere('last_name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Statistics using filtered data
        $allFilteredPayments = $statsQuery->get();
        $stats = [
            'total_payments' => $allFilteredPayments->count(),
            'completed_payments' => $allFilteredPayments->where('status', 'completed')->count(),
            'pending_payments' => $allFilteredPayments->where('status', 'pending')->count(),
            'failed_payments' => $allFilteredPayments->where('status', 'failed')->count(),
            'total_amount' => $allFilteredPayments->where('status', 'completed')->sum('amount'),
            'pending_amount' => $allFilteredPayments->where('status', 'pending')->sum('amount'),
        ];

        // Get filter options
        $summits = Summit::orderBy('year', 'desc')->get();
        $conferences = Conference::where('is_active', true)->get();
        $paymentMethods = Payment::distinct()->pluck('payment_method')->filter();

        return view('admin.payments.index', compact(
            'payments',
            'stats',
            'summits',
            'conferences',
            'paymentMethods'
        ));
    }

    /**
     * Display the specified payment.
     */
    public function show(Payment $payment)
    {
        $payment->load(['user.branch.zone.conference', 'summitRegistration.summit']);

        return view('admin.payments.show', compact('payment'));
    }

    /**
     * Update the specified payment.
     */
    public function update(Request $request, Payment $payment)
    {
        $validated = $request->validate([
            'status' => 'required|in:pending,completed,failed,cancelled',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $payment->update($validated);

        // If marking as completed, update the registration
        if ($validated['status'] === 'completed' && $payment->status !== 'completed') {
            $payment->update(['paid_at' => now()]);

            $registration = $payment->summitRegistration;
            $registration->paid_amount += $payment->amount;
            $registration->balance = $registration->total_amount - $registration->paid_amount;
            $registration->payment_complete = $registration->balance <= 0;
            $registration->save();
        }

        return redirect()->route('admin.payments.show', $payment)
            ->with('success', 'Payment updated successfully.');
    }

    /**
     * Approve a payment.
     */
    public function approve(Payment $payment)
    {
        if ($payment->status !== 'pending') {
            return back()->with('error', 'Only pending payments can be approved.');
        }

        $payment->update([
            'status' => 'completed',
            'paid_at' => now(),
        ]);

        // Update registration
        $registration = $payment->summitRegistration;
        $registration->paid_amount += $payment->amount;
        $registration->balance = $registration->total_amount - $registration->paid_amount;
        $registration->payment_complete = $registration->balance <= 0;
        $registration->save();

        return back()->with('success', 'Payment approved successfully.');
    }

    /**
     * Reject a payment.
     */
    public function reject(Request $request, Payment $payment)
    {
        if ($payment->status !== 'pending') {
            return back()->with('error', 'Only pending payments can be rejected.');
        }

        $validated = $request->validate([
            'rejection_reason' => 'required|string|max:500',
        ]);

        $payment->update([
            'status' => 'failed',
            'admin_notes' => $validated['rejection_reason'],
        ]);

        return back()->with('success', 'Payment rejected successfully.');
    }

    /**
     * Remove the specified payment.
     */
    public function destroy(Payment $payment)
    {
        if ($payment->status === 'completed') {
            return back()->with('error', 'Cannot delete completed payments.');
        }

        $payment->delete();

        return redirect()->route('admin.payments.index')
            ->with('success', 'Payment deleted successfully.');
    }
}
