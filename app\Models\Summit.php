<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Summit extends Model
{
    protected $fillable = [
        'title',
        'theme',
        'description',
        'year',
        'start_date',
        'end_date',
        'venue',
        'venue_address',
        'registration_fee',
        'registration_deadline',
        'poster_image',
        'gallery_images',
        'is_active',
        'is_current'
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'registration_deadline' => 'date',
        'registration_fee' => 'decimal:2',
        'gallery_images' => 'array',
        'is_active' => 'boolean',
        'is_current' => 'boolean'
    ];

    /**
     * Get the activities for the summit.
     */
    public function activities(): HasMany
    {
        return $this->hasMany(SummitActivity::class);
    }

    /**
     * Get the ministers for the summit.
     */
    public function ministers(): HasMany
    {
        return $this->hasMany(SummitMinister::class);
    }

    /**
     * Get the registrations for the summit.
     */
    public function registrations(): Has<PERSON><PERSON>
    {
        return $this->hasMany(SummitRegistration::class);
    }

    /**
     * Get the ID cards for the summit.
     */
    public function idCards(): HasMany
    {
        return $this->hasMany(IdCard::class);
    }

    /**
     * Get the meal trackings for the summit.
     */
    public function mealTrackings(): HasMany
    {
        return $this->hasMany(MealTracking::class);
    }

    /**
     * Get the gate entries for the summit.
     */
    public function gateEntries(): HasMany
    {
        return $this->hasMany(GateEntry::class);
    }

    /**
     * Scope a query to only include current summits.
     */
    public function scopeCurrent($query)
    {
        return $query->where('is_current', true);
    }

    /**
     * Scope a query to only include active summits.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
