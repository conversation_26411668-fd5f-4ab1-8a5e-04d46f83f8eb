@extends('layouts.user')

@section('title', 'Payment Successful')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Success Message -->
        <div class="text-center mb-4">
            <div class="success-icon mb-3">
                <i class="fas fa-check-circle fa-5x text-success"></i>
            </div>
            <h1 class="text-success">Payment Successful!</h1>
            <p class="lead text-muted">Your payment has been processed successfully.</p>
        </div>

        <!-- Payment Details Card -->
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>Payment Receipt</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Payment Reference:</strong></td>
                                <td><code>{{ $payment->payment_reference }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>Amount Paid:</strong></td>
                                <td><strong class="text-success">TZS {{ number_format($payment->amount, 0) }}</strong></td>
                            </tr>
                            <tr>
                                <td><strong>Payment Method:</strong></td>
                                <td>{{ ucfirst(str_replace('_', ' ', $payment->payment_method)) }}</td>
                            </tr>
                            @if($payment->receipt_number)
                            <tr>
                                <td><strong>Receipt Number:</strong></td>
                                <td><code>{{ $payment->receipt_number }}</code></td>
                            </tr>
                            @endif
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Payment Date:</strong></td>
                                <td>{{ $payment->paid_at ? $payment->paid_at->format('F j, Y g:i A') : $payment->created_at->format('F j, Y g:i A') }}</td>
                            </tr>
                            @if($payment->transaction_id)
                            <tr>
                                <td><strong>Transaction ID:</strong></td>
                                <td><code>{{ $payment->transaction_id }}</code></td>
                            </tr>
                            @endif
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td><span class="badge bg-success">Completed</span></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summit Registration Summary -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-mountain me-2"></i>Summit Registration Summary</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Summit:</strong></td>
                                <td>{{ $payment->summitRegistration->summit->title }}</td>
                            </tr>
                            <tr>
                                <td><strong>Year:</strong></td>
                                <td>{{ $payment->summitRegistration->summit->year }}</td>
                            </tr>
                            <tr>
                                <td><strong>Dates:</strong></td>
                                <td>{{ $payment->summitRegistration->summit->start_date->format('M j') }} - {{ $payment->summitRegistration->summit->end_date->format('M j, Y') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Venue:</strong></td>
                                <td>{{ $payment->summitRegistration->summit->venue }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Registration Number:</strong></td>
                                <td><code>{{ $payment->summitRegistration->registration_number }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>Total Registration Fee:</strong></td>
                                <td>TZS {{ number_format($payment->summitRegistration->total_amount, 0) }}</td>
                            </tr>
                            <tr>
                                <td><strong>Total Amount Paid:</strong></td>
                                <td class="text-success"><strong>TZS {{ number_format($payment->summitRegistration->paid_amount, 0) }}</strong></td>
                            </tr>
                            <tr>
                                <td><strong>Remaining Balance:</strong></td>
                                <td>
                                    @if($payment->summitRegistration->balance > 0)
                                        <span class="text-warning">TZS {{ number_format($payment->summitRegistration->balance, 2) }}</span>
                                    @else
                                        <span class="text-success"><strong>TZS 0.00 (Fully Paid)</strong></span>
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                @if($payment->summitRegistration->payment_complete)
                <div class="alert alert-success mt-3">
                    <h6><i class="fas fa-check-circle me-2"></i>Registration Complete!</h6>
                    <p class="mb-0">Your summit registration is now fully paid and confirmed. You can now generate your ID card.</p>
                </div>
                @elseif($payment->summitRegistration->balance > 0)
                <div class="alert alert-warning mt-3">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Partial Payment</h6>
                    <p class="mb-0">You still have a remaining balance of <strong>TZS {{ number_format($payment->summitRegistration->balance, 2) }}</strong>. Complete your payment to access all summit benefits.</p>
                </div>
                @endif
            </div>
        </div>

        <!-- Next Steps -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-arrow-right me-2"></i>What's Next?</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    @if($payment->summitRegistration->payment_complete)
                    <div class="col-md-6 mb-3">
                        <div class="d-grid">
                            <a href="{{ route('id-cards.index') }}" class="btn btn-success btn-lg">
                                <i class="fas fa-id-card me-2"></i>Generate ID Card
                            </a>
                        </div>
                        <small class="text-muted">Get your official summit ID card</small>
                    </div>
                    @else
                    <div class="col-md-6 mb-3">
                        <div class="d-grid">
                            <a href="{{ route('payments.create', $payment->summitRegistration) }}" class="btn btn-warning btn-lg">
                                <i class="fas fa-credit-card me-2"></i>Complete Payment
                            </a>
                        </div>
                        <small class="text-muted">Pay remaining balance</small>
                    </div>
                    @endif
                    
                    <div class="col-md-6 mb-3">
                        <div class="d-grid">
                            <a href="{{ route('registrations.show', $payment->summitRegistration) }}" class="btn btn-primary btn-lg">
                                <i class="fas fa-eye me-2"></i>View Registration
                            </a>
                        </div>
                        <small class="text-muted">See your registration details</small>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6 mb-3">
                        <div class="d-grid">
                            <a href="{{ route('landing.summit', $payment->summitRegistration->summit) }}" class="btn btn-outline-info">
                                <i class="fas fa-mountain me-2"></i>Summit Details
                            </a>
                        </div>
                        <small class="text-muted">Learn more about the summit</small>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="d-grid">
                            <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </div>
                        <small class="text-muted">Return to your dashboard</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Important Information -->
        <div class="card mt-4">
            <div class="card-body">
                <h6><i class="fas fa-info-circle text-info me-2"></i>Important Information</h6>
                <ul class="mb-0">
                    <li>Keep your payment reference number for your records</li>
                    <li>You should receive an SMS confirmation shortly</li>
                    <li>Your payment receipt is available in your dashboard</li>
                    @if($payment->summitRegistration->payment_complete)
                    <li>You can now generate and download your summit ID card</li>
                    @endif
                    <li>Contact support if you have any questions about your payment</li>
                </ul>
            </div>
        </div>

        <!-- Print Receipt Button -->
        <div class="text-center mt-4 mb-4">
            <button onclick="window.print()" class="btn btn-outline-primary">
                <i class="fas fa-print me-2"></i>Print Receipt
            </button>
        </div>
    </div>
</div>

<style>
.success-icon {
    animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@media print {
    .btn, .card-header, nav, footer {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .success-icon {
        animation: none;
    }
}
</style>

<script>
// Confetti effect for successful payment
document.addEventListener('DOMContentLoaded', function() {
    // Simple confetti effect (you can replace with a more sophisticated library)
    setTimeout(function() {
        // Add some celebration effect here if desired
        console.log('Payment successful! 🎉');
    }, 500);
});
</script>
@endsection
