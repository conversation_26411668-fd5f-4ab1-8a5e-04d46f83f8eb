@extends('layouts.auth')

@section('title', 'Reset Password - TUCASA Summit Management')

@section('content')
<div class="auth-container">
    <div class="auth-card">
        <!-- Header -->
        <div class="auth-header">
            <div class="auth-logo">
                <div class="auth-logo-icon">T</div>
                <div class="auth-logo-text">TUCASA Account</div>
            </div>
            <h1 class="auth-title">Reset Password</h1>
            <p class="auth-subtitle">Enter your new password below</p>
        </div>

        <!-- Form -->
        <form method="POST" action="{{ route('password.update') }}" class="auth-form">
            @csrf
            <input type="hidden" name="token" value="{{ $token }}">

            <!-- Email Field -->
            <div class="form-group">
                <label for="email" class="form-label">Email Address</label>
                <input
                    id="email"
                    type="email"
                    class="form-input @error('email') error @enderror"
                    name="email"
                    value="{{ $email ?? old('email') }}"
                    required
                    autocomplete="email"
                    autofocus
                    placeholder="<EMAIL>"
                >
                @error('email')
                    <div class="error-message">
                        <span class="material-icons" style="font-size: 16px;">error</span>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Password Field -->
            <div class="form-group">
                <label for="password" class="form-label">New Password</label>
                <div class="password-input-container">
                    <input
                        id="password"
                        type="password"
                        class="form-input @error('password') error @enderror"
                        name="password"
                        required
                        autocomplete="new-password"
                        placeholder="••••••••"
                    >
                    <button type="button" class="password-toggle" onclick="togglePassword('password')">
                        <span class="material-icons" id="password-icon">visibility</span>
                    </button>
                </div>
                @error('password')
                    <div class="error-message">
                        <span class="material-icons" style="font-size: 16px;">error</span>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Confirm Password Field -->
            <div class="form-group">
                <label for="password-confirm" class="form-label">Confirm Password</label>
                <div class="password-input-container">
                    <input
                        id="password-confirm"
                        type="password"
                        class="form-input"
                        name="password_confirmation"
                        required
                        autocomplete="new-password"
                        placeholder="••••••••"
                    >
                    <button type="button" class="password-toggle" onclick="togglePassword('password-confirm')">
                        <span class="material-icons" id="password-confirm-icon">visibility</span>
                    </button>
                </div>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="auth-button">Reset Password</button>
        </form>

        <!-- Footer -->
        <div class="auth-footer">
            Remember your password? <a href="{{ route('login') }}">Sign in here</a>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const passwordInput = document.getElementById(fieldId);
    const passwordIcon = document.getElementById(fieldId + '-icon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordIcon.textContent = 'visibility_off';
    } else {
        passwordInput.type = 'password';
        passwordIcon.textContent = 'visibility';
    }
}
</script>
@endsection
