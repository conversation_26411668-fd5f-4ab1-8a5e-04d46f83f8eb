<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreUserRequest;
use App\Http\Requests\UpdateUserRequest;
use App\Models\User;
use App\Models\Branch;
use App\Models\University;
use App\Models\Conference;
use App\Models\Zone;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin|conference_admin|zone_admin|branch_admin']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = User::with(['branch.zone.conference', 'university', 'roles']);
        $user = Auth::user();

        // Apply scope filtering based on admin level
        if ($user->hasRole('conference_admin') && $user->admin_conference_id) {
            // Conference admin can only see users in their conference
            $query->whereHas('branch.zone', function($q) use ($user) {
                $q->where('conference_id', $user->admin_conference_id);
            });
        } elseif ($user->hasRole('zone_admin') && $user->admin_zone_id) {
            // Zone admin can only see users in their zone
            $query->whereHas('branch', function($q) use ($user) {
                $q->where('zone_id', $user->admin_zone_id);
            });
        } elseif ($user->hasRole('branch_admin') && $user->admin_branch_id) {
            // Branch admin can only see users in their branch
            $query->where('branch_id', $user->admin_branch_id);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('student_id', 'like', "%{$search}%");
            });
        }

        // Filter by branch (respect admin scope)
        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        // Filter by university
        if ($request->filled('university_id')) {
            $query->where('university_id', $request->university_id);
        }

        // Filter by role
        if ($request->filled('role')) {
            $query->role($request->role);
        }

        $users = $query->paginate(15);

        // Filter branches and universities based on admin scope
        $branches = $this->getAvailableBranches($user);
        $universities = University::all();
        $roles = Role::all();

        return view('admin.users.index', compact('users', 'branches', 'universities', 'roles'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $branches = Branch::with('zone.conference')->get();
        $universities = University::all();
        $roles = Role::all();

        return view('admin.users.create', compact('branches', 'universities', 'roles'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreUserRequest $request)
    {
        $validated = $request->validated();
        $validated['password'] = Hash::make($validated['password']);

        // Handle profile image upload
        if ($request->hasFile('profile_image')) {
            $validated['profile_image'] = $request->file('profile_image')->store('profile-images', 'public');
        }

        $user = User::create($validated);

        // Assign role
        if ($request->filled('role')) {
            $user->assignRole($request->role);
        }

        return redirect()->route('admin.users.index')
            ->with('success', 'User created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        $user->load(['branch.zone.conference', 'university', 'roles', 'summitRegistrations.summit']);

        return view('admin.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        $branches = Branch::with('zone.conference')->get();
        $universities = University::all();
        $roles = Role::all();

        return view('admin.users.edit', compact('user', 'branches', 'universities', 'roles'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateUserRequest $request, User $user)
    {
        $validated = $request->validated();

        // Handle password update
        if ($request->filled('password')) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        // Handle profile image upload
        if ($request->hasFile('profile_image')) {
            // Delete old image
            if ($user->profile_image) {
                Storage::disk('public')->delete($user->profile_image);
            }
            $validated['profile_image'] = $request->file('profile_image')->store('profile-images', 'public');
        }

        $user->update($validated);

        // Update role
        if ($request->filled('role')) {
            $user->syncRoles([$request->role]);
        }

        return redirect()->route('admin.users.index')
            ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        // Delete profile image
        if ($user->profile_image) {
            Storage::disk('public')->delete($user->profile_image);
        }

        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }

    /**
     * Show role management interface for a user
     */
    public function manageRoles(User $user)
    {
        $user->load(['roles', 'adminConference', 'adminZone', 'adminBranch']);

        // Get all roles except 'user' role (since everyone should have user role by default)
        // and exclude roles the user already has (except user role)
        $currentRoleNames = $user->roles->pluck('name')->reject(function ($roleName) {
            return $roleName === 'user';
        });

        $availableRoles = Role::whereNotIn('name', $currentRoleNames)
            ->where('name', '!=', 'user') // Don't show user role as it's default
            ->get();

        $conferences = Conference::all();
        $zones = Zone::with('conference')->get();
        $branches = Branch::with('zone.conference')->get();

        return view('admin.users.manage-roles', compact(
            'user', 'availableRoles', 'conferences', 'zones', 'branches'
        ));
    }

    /**
     * Assign role to user
     */
    public function assignRole(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'role' => 'required|exists:roles,name',
            'admin_conference_id' => 'nullable|exists:conferences,id',
            'admin_zone_id' => 'nullable|exists:zones,id',
            'admin_branch_id' => 'nullable|exists:branches,id',
        ]);

        $user = User::findOrFail($request->user_id);
        $role = $request->role;

        DB::transaction(function () use ($user, $role, $request) {
            // Remove existing admin roles but keep user role
            $currentRoles = $user->roles->pluck('name')->toArray();
            $adminRoles = ['admin', 'treasurer', 'conference_admin', 'zone_admin', 'branch_admin'];
            $rolesToRemove = array_intersect($currentRoles, $adminRoles);

            foreach ($rolesToRemove as $roleToRemove) {
                $user->removeRole($roleToRemove);
            }

            // Clear existing admin assignments
            $user->update([
                'admin_conference_id' => null,
                'admin_zone_id' => null,
                'admin_branch_id' => null,
            ]);

            // Assign new role (user role will remain)
            $user->assignRole($role);

            // Set admin level if applicable
            if (in_array($role, ['conference_admin', 'zone_admin', 'branch_admin'])) {
                $updateData = [];

                if ($role === 'conference_admin' && $request->admin_conference_id) {
                    $updateData['admin_conference_id'] = $request->admin_conference_id;
                } elseif ($role === 'zone_admin' && $request->admin_zone_id) {
                    $updateData['admin_zone_id'] = $request->admin_zone_id;
                } elseif ($role === 'branch_admin' && $request->admin_branch_id) {
                    $updateData['admin_branch_id'] = $request->admin_branch_id;
                }

                if (!empty($updateData)) {
                    $user->update($updateData);
                }
            }
        });

        return back()->with('success', "Role '{$role}' assigned to {$user->full_name} successfully.");
    }

    /**
     * Remove role from user
     */
    public function removeRole(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'role' => 'required|exists:roles,name',
        ]);

        $user = User::findOrFail($request->user_id);
        $role = $request->role;

        DB::transaction(function () use ($user, $role) {
            // Don't allow removing user role
            if ($role === 'user') {
                throw new \Exception('Cannot remove user role');
            }

            $user->removeRole($role);

            // Clear admin level assignments if removing admin roles
            if (in_array($role, ['conference_admin', 'zone_admin', 'branch_admin'])) {
                $user->update([
                    'admin_conference_id' => null,
                    'admin_zone_id' => null,
                    'admin_branch_id' => null,
                ]);
            }
        });

        return back()->with('success', "Role '{$role}' removed from {$user->full_name} successfully.");
    }

    /**
     * Get available branches based on admin scope
     */
    private function getAvailableBranches($user)
    {
        if ($user->hasRole('admin')) {
            // Super admin can see all branches
            return Branch::with('zone.conference')->get();
        } elseif ($user->hasRole('conference_admin') && $user->admin_conference_id) {
            // Conference admin can see branches in their conference
            return Branch::with('zone.conference')
                ->whereHas('zone', function($q) use ($user) {
                    $q->where('conference_id', $user->admin_conference_id);
                })->get();
        } elseif ($user->hasRole('zone_admin') && $user->admin_zone_id) {
            // Zone admin can see branches in their zone
            return Branch::with('zone.conference')
                ->where('zone_id', $user->admin_zone_id)->get();
        } elseif ($user->hasRole('branch_admin') && $user->admin_branch_id) {
            // Branch admin can only see their own branch
            return Branch::with('zone.conference')
                ->where('id', $user->admin_branch_id)->get();
        }

        return collect(); // Return empty collection for other roles
    }
}
