<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\URL;

class ForceHttpsForAssets
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // Force HTTPS for ngrok URLs
        if ($request->header('x-forwarded-proto') === 'https' || 
            str_contains($request->getHost(), 'ngrok') ||
            str_contains(config('app.url'), 'ngrok')) {
            URL::forceScheme('https');
        }

        return $next($request);
    }
}
