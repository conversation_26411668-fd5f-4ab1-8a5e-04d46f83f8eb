<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\SummitRegistration;
use App\Services\AzamPayService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AzamPayController extends Controller
{
    protected $azamPayService;

    public function __construct(AzamPayService $azamPayService)
    {
        $this->middleware('auth')->except(['webhook', 'success', 'cancel']);
        $this->azamPayService = $azamPayService;
    }

    /**
     * Show payment form with AzamPay options
     */
    public function create(SummitRegistration $registration)
    {
        $this->authorize('view', $registration);

        if ($registration->payment_complete) {
            return redirect()->route('dashboard')
                ->with('info', 'Payment for this summit is already complete.');
        }

        $mobileProviders = $this->azamPayService->getAvailableMobileProviders();
        $banks = $this->azamPayService->getAvailableBanks();

        return view('payments.azampay-create', compact('registration', 'mobileProviders', 'banks'));
    }

    /**
     * Process mobile money payment
     */
    public function processMobilePayment(Request $request, SummitRegistration $registration)
    {
        $this->authorize('view', $registration);

        $validated = $request->validate([
            'amount' => 'required|numeric|min:1000|max:' . $registration->balance,
            'phone_number' => 'required|string|regex:/^[67]\d{8}$/',
            'provider' => 'required|in:Mpesa,Tigo,Airtel,Halopesa,Azampesa',
        ]);

        if ($registration->payment_complete) {
            return redirect()->route('dashboard')
                ->with('error', 'Payment for this summit is already complete.');
        }

        $result = $this->azamPayService->initiateMobileMoneyPayment(
            $registration,
            $validated['amount'],
            $validated['phone_number'],
            $validated['provider']
        );

        if ($result['success']) {
            return redirect()->route('payments.azampay.status', $result['payment'])
                ->with('success', $result['message']);
        } else {
            return back()->withErrors(['payment' => $result['message']])->withInput();
        }
    }

    /**
     * Process bank payment
     */
    public function processBankPayment(Request $request, SummitRegistration $registration)
    {
        $this->authorize('view', $registration);

        $validated = $request->validate([
            'amount' => 'required|numeric|min:1000|max:' . $registration->balance,
            'bank_code' => 'required|in:CRDB,NMB,NBC,EXIM,DTB,STANBIC,ABSA',
        ]);

        if ($registration->payment_complete) {
            return redirect()->route('dashboard')
                ->with('error', 'Payment for this summit is already complete.');
        }

        $result = $this->azamPayService->initiateBankPayment(
            $registration,
            $validated['amount'],
            $validated['bank_code']
        );

        if ($result['success']) {
            if (isset($result['checkout_url'])) {
                // Redirect to bank's payment page
                return redirect($result['checkout_url']);
            } else {
                return redirect()->route('payments.show', $result['payment'])
                    ->with('success', $result['message']);
            }
        } else {
            return back()->withErrors(['payment' => $result['message']])->withInput();
        }
    }

    /**
     * Show payment details
     */
    public function show(Payment $payment)
    {
        $this->authorize('view', $payment->summitRegistration);

        return view('payments.show', compact('payment'));
    }

    /**
     * Process manual payment methods (cash, direct mobile money)
     */
    public function processManualPayment(Request $request, SummitRegistration $registration)
    {
        $this->authorize('view', $registration);

        $validated = $request->validate([
            'amount' => 'required|numeric|min:1000|max:' . $registration->balance,
            'payment_method' => 'required|in:cash,mobile_money_direct',
            // Cash payment fields
            'received_by' => 'required_if:payment_method,cash|string|max:255',
            'receipt_number' => 'required_if:payment_method,cash|string|max:255',
            'payment_location' => 'nullable|string|max:255',
            'payment_date' => 'required_if:payment_method,cash|date',
            // Direct mobile money fields
            'mno_provider' => 'required_if:payment_method,mobile_money_direct|in:Vodacom,Tigo,Airtel,Halopesa',
            'phone_number_direct' => 'required_if:payment_method,mobile_money_direct|string|regex:/^[67]\d{8}$/',
            'transaction_id' => 'required_if:payment_method,mobile_money_direct|string|max:255',
            'confirmation_code' => 'nullable|string|max:255',
        ]);

        if ($registration->payment_complete) {
            return redirect()->route('dashboard')
                ->with('error', 'Payment for this summit is already complete.');
        }

        $paymentReference = strtoupper($validated['payment_method']) . '_' . $registration->id . '_' . time();

        $gatewayData = [];
        if ($validated['payment_method'] === 'cash') {
            $gatewayData = [
                'received_by' => $validated['received_by'],
                'receipt_number' => $validated['receipt_number'],
                'payment_location' => $validated['payment_location'] ?? 'TUCASA STU Office',
                'payment_date' => $validated['payment_date'],
            ];
        } else {
            $gatewayData = [
                'mno_provider' => $validated['mno_provider'],
                'phone_number' => $validated['phone_number_direct'],
                'transaction_id' => $validated['transaction_id'],
                'confirmation_code' => $validated['confirmation_code'] ?? null,
            ];
        }

        $payment = Payment::create([
            'user_id' => $registration->user_id,
            'summit_registration_id' => $registration->id,
            'payment_reference' => $paymentReference,
            'amount' => $validated['amount'],
            'payment_method' => $validated['payment_method'],
            'gateway_provider' => 'manual',
            'status' => 'pending_verification',
            'gateway_data' => $gatewayData,
        ]);

        $message = $validated['payment_method'] === 'cash' 
            ? 'Cash payment recorded. Payment will be verified by admin.'
            : 'Mobile money payment recorded. Payment will be verified by admin within 24 hours.';

        return redirect()->route('payments.azampay.status', $payment)
            ->with('success', $message);
    }

    /**
     * Show payment status
     */
    public function status(Payment $payment)
    {
        $this->authorize('view', $payment);

        // Check status for AzamPay payments
        if (in_array($payment->payment_method, ['azampay_mobile', 'azampay_bank']) && $payment->status === 'processing') {
            $this->azamPayService->checkPaymentStatus($payment);
            $payment->refresh();
        }

        return view('payments.azampay-status', compact('payment'));
    }

    /**
     * Handle AzamPay webhook
     */
    public function webhook(Request $request)
    {
        // Verify webhook signature for security
        $signature = $request->header('X-Azampay-Signature');
        if (!$this->verifyWebhookSignature($request->getContent(), $signature)) {
            Log::warning('Invalid webhook signature', ['ip' => $request->ip()]);
            return response()->json(['status' => 'error', 'message' => 'Invalid signature'], 401);
        }

        Log::info('AzamPay webhook received', $request->all());

        $result = $this->azamPayService->handleWebhook($request->all());

        if ($result['success']) {
            return response()->json(['status' => 'success', 'message' => 'Webhook processed']);
        } else {
            return response()->json(['status' => 'error', 'message' => $result['message']], 400);
        }
    }

    /**
     * Verify webhook signature
     */
    private function verifyWebhookSignature($payload, $signature)
    {
        if (!$signature) {
            return false;
        }

        $expectedSignature = hash_hmac('sha256', $payload, config('services.azampay.webhook_secret'));
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Handle successful payment return
     */
    public function success(Payment $payment)
    {
        // Check payment status
        $this->azamPayService->checkPaymentStatus($payment);
        $payment->refresh();

        if ($payment->status === 'completed') {
            return redirect()->route('dashboard')
                ->with('success', 'Payment completed successfully! Your registration is now active.');
        } else {
            return redirect()->route('payments.azampay.status', $payment)
                ->with('info', 'Payment is being processed. Please wait for confirmation.');
        }
    }

    /**
     * Handle cancelled payment return
     */
    public function cancel(Payment $payment)
    {
        $payment->update(['status' => 'cancelled']);

        return redirect()->route('payments.create', $payment->summitRegistration)
            ->with('warning', 'Payment was cancelled. You can try again.');
    }

    /**
     * Check payment status via AJAX
     */
    public function checkStatus(Payment $payment)
    {
        $this->authorize('view', $payment);

        if (in_array($payment->payment_method, ['azampay_mobile', 'azampay_bank'])) {
            $result = $this->azamPayService->checkPaymentStatus($payment);
            $payment->refresh();

            return response()->json([
                'status' => $payment->status,
                'message' => $this->getStatusMessage($payment->status),
                'payment' => $payment->only(['id', 'status', 'amount', 'payment_method', 'created_at'])
            ]);
        }

        return response()->json([
            'status' => $payment->status,
            'message' => $this->getStatusMessage($payment->status),
            'payment' => $payment->only(['id', 'status', 'amount', 'payment_method', 'created_at'])
        ]);
    }

    /**
     * Get user-friendly status message
     */
    private function getStatusMessage($status)
    {
        return match($status) {
            'pending' => 'Payment is being initialized...',
            'processing' => 'Payment is being processed. Please complete on your device.',
            'completed' => 'Payment completed successfully!',
            'failed' => 'Payment failed. Please try again.',
            'cancelled' => 'Payment was cancelled.',
            'pending_verification' => 'Payment submitted for verification by admin.',
            default => 'Unknown payment status.',
        };
    }
}
