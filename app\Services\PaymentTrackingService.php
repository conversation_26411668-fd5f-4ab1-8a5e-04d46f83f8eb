<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\SummitRegistration;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PaymentTrackingService
{
    /**
     * Get payment method statistics
     */
    public function getPaymentMethodStats(Carbon $from = null, Carbon $to = null): array
    {
        $query = Payment::where('status', 'completed');
        
        if ($from) $query->where('created_at', '>=', $from);
        if ($to) $query->where('created_at', '<=', $to);

        $stats = $query->select('payment_method', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total'))
            ->groupBy('payment_method')
            ->get();

        $result = [];
        foreach ($stats as $stat) {
            $result[$stat->payment_method] = [
                'count' => $stat->count,
                'total' => $stat->total,
                'average' => $stat->count > 0 ? $stat->total / $stat->count : 0,
                'percentage' => 0 // Will be calculated below
            ];
        }

        // Calculate percentages
        $totalAmount = array_sum(array_column($result, 'total'));
        foreach ($result as $method => &$data) {
            $data['percentage'] = $totalAmount > 0 ? ($data['total'] / $totalAmount) * 100 : 0;
        }

        return $result;
    }

    /**
     * Get daily payment breakdown by method
     */
    public function getDailyBreakdown(Carbon $date = null): array
    {
        $date = $date ?? today();
        
        return Payment::where('status', 'completed')
            ->whereDate('created_at', $date)
            ->select('payment_method', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total'))
            ->groupBy('payment_method')
            ->get()
            ->keyBy('payment_method')
            ->toArray();
    }

    /**
     * Get payment method trends over time
     */
    public function getPaymentMethodTrends(int $days = 30): array
    {
        $trends = Payment::where('status', 'completed')
            ->where('created_at', '>=', now()->subDays($days))
            ->select(
                'payment_method',
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(amount) as total')
            )
            ->groupBy('payment_method', 'date')
            ->orderBy('date')
            ->get();

        $result = [];
        foreach ($trends as $trend) {
            $result[$trend->payment_method][$trend->date] = [
                'count' => $trend->count,
                'total' => $trend->total
            ];
        }

        return $result;
    }

    /**
     * Get reconciliation report for manual payment methods
     */
    public function getReconciliationReport(Carbon $from, Carbon $to): array
    {
        $manualMethods = ['bank_transfer', 'cash', 'mobile_money', 'direct_mobile'];
        
        $payments = Payment::whereIn('payment_method', $manualMethods)
            ->whereBetween('created_at', [$from, $to])
            ->with(['user', 'summitRegistration.summit'])
            ->get();

        $report = [];
        foreach ($manualMethods as $method) {
            $methodPayments = $payments->where('payment_method', $method);
            
            $report[$method] = [
                'total_amount' => $methodPayments->where('status', 'completed')->sum('amount'),
                'pending_amount' => $methodPayments->where('status', 'pending_verification')->sum('amount'),
                'failed_amount' => $methodPayments->where('status', 'failed')->sum('amount'),
                'completed_count' => $methodPayments->where('status', 'completed')->count(),
                'pending_count' => $methodPayments->where('status', 'pending_verification')->count(),
                'failed_count' => $methodPayments->where('status', 'failed')->count(),
                'payments' => $methodPayments->values()
            ];
        }

        return $report;
    }

    /**
     * Get gateway performance comparison
     */
    public function getGatewayPerformance(): array
    {
        $gateways = [
            'selcom' => ['selcom'],
            'azampay' => ['azampay_mobile', 'azampay_bank'],
            'manual' => ['bank_transfer', 'cash', 'mobile_money', 'direct_mobile']
        ];

        $performance = [];
        foreach ($gateways as $gateway => $methods) {
            $payments = Payment::whereIn('payment_method', $methods);
            
            $total = $payments->count();
            $completed = $payments->where('status', 'completed')->count();
            $failed = $payments->where('status', 'failed')->count();
            
            $performance[$gateway] = [
                'total_transactions' => $total,
                'success_rate' => $total > 0 ? ($completed / $total) * 100 : 0,
                'failure_rate' => $total > 0 ? ($failed / $total) * 100 : 0,
                'total_amount' => $payments->where('status', 'completed')->sum('amount'),
                'average_amount' => $completed > 0 ? $payments->where('status', 'completed')->avg('amount') : 0
            ];
        }

        return $performance;
    }

    /**
     * Get payment method recommendations based on performance
     */
    public function getPaymentMethodRecommendations(): array
    {
        $stats = $this->getPaymentMethodStats();
        $performance = $this->getGatewayPerformance();

        $recommendations = [];

        // Analyze each method
        foreach ($stats as $method => $data) {
            $recommendation = [
                'method' => $method,
                'usage_percentage' => $data['percentage'],
                'average_amount' => $data['average'],
                'total_revenue' => $data['total'],
                'recommendation' => 'maintain',
                'reasons' => []
            ];

            // High usage methods
            if ($data['percentage'] > 50) {
                $recommendation['recommendation'] = 'promote';
                $recommendation['reasons'][] = 'High user adoption';
            }

            // Low usage methods
            if ($data['percentage'] < 5 && $data['count'] > 0) {
                $recommendation['recommendation'] = 'review';
                $recommendation['reasons'][] = 'Low adoption rate';
            }

            // High value methods
            if ($data['average'] > 100000) {
                $recommendation['reasons'][] = 'High-value transactions';
            }

            $recommendations[] = $recommendation;
        }

        return $recommendations;
    }

    /**
     * Generate financial reconciliation summary
     */
    public function generateReconciliationSummary(Carbon $from, Carbon $to): array
    {
        $summary = [
            'period' => [
                'from' => $from->format('Y-m-d'),
                'to' => $to->format('Y-m-d'),
                'days' => $from->diffInDays($to) + 1
            ],
            'totals' => [
                'all_payments' => 0,
                'completed_payments' => 0,
                'pending_verification' => 0,
                'failed_payments' => 0
            ],
            'by_method' => [],
            'verification_required' => [],
            'discrepancies' => []
        ];

        // Get all payments in period
        $payments = Payment::whereBetween('created_at', [$from, $to])->get();

        // Calculate totals
        $summary['totals']['all_payments'] = $payments->sum('amount');
        $summary['totals']['completed_payments'] = $payments->where('status', 'completed')->sum('amount');
        $summary['totals']['pending_verification'] = $payments->where('status', 'pending_verification')->sum('amount');
        $summary['totals']['failed_payments'] = $payments->where('status', 'failed')->sum('amount');

        // Break down by method
        foreach ($payments->groupBy('payment_method') as $method => $methodPayments) {
            $summary['by_method'][$method] = [
                'total_amount' => $methodPayments->sum('amount'),
                'completed_amount' => $methodPayments->where('status', 'completed')->sum('amount'),
                'pending_amount' => $methodPayments->where('status', 'pending_verification')->sum('amount'),
                'count' => $methodPayments->count()
            ];
        }

        // Payments requiring verification
        $summary['verification_required'] = $payments
            ->where('status', 'pending_verification')
            ->map(function ($payment) {
                return [
                    'id' => $payment->id,
                    'method' => $payment->payment_method,
                    'amount' => $payment->amount,
                    'user' => $payment->user->full_name,
                    'days_pending' => $payment->created_at->diffInDays(now())
                ];
            })
            ->values()
            ->toArray();

        return $summary;
    }
}
