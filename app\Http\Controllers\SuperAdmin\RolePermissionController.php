<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class RolePermissionController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * Display role management dashboard
     */
    public function index()
    {
        $roles = Role::withCount(['users', 'permissions'])->get();
        $permissions = Permission::withCount('roles')->get();
        $totalUsers = User::count();
        $usersWithRoles = User::whereHas('roles')->count();
        
        $roleStats = [
            'total_roles' => $roles->count(),
            'total_permissions' => $permissions->count(),
            'total_users' => $totalUsers,
            'users_with_roles' => $usersWithRoles,
            'users_without_roles' => $totalUsers - $usersWithRoles,
        ];

        return view('superadmin.roles.index', compact('roles', 'permissions', 'roleStats'));
    }

    /**
     * Show role details and users
     */
    public function showRole(Role $role)
    {
        $role->load(['permissions', 'users.branch.zone.conference']);
        $availablePermissions = Permission::whereNotIn('id', $role->permissions->pluck('id'))->get();
        
        return view('superadmin.roles.show', compact('role', 'availablePermissions'));
    }

    /**
     * Show user role assignment interface
     */
    public function userRoles()
    {
        $users = User::with(['roles', 'branch.zone.conference'])
            ->when(request('search'), function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('first_name', 'like', "%{$search}%")
                      ->orWhere('last_name', 'like', "%{$search}%");
                });
            })
            ->when(request('role'), function ($query, $role) {
                if ($role === 'no_role') {
                    $query->doesntHave('roles');
                } else {
                    $query->whereHas('roles', function ($q) use ($role) {
                        $q->where('name', $role);
                    });
                }
            })
            ->when(request('status'), function ($query, $status) {
                $query->where('is_active', $status === 'active');
            })
            ->paginate(20);

        $roles = Role::all();
        
        return view('superadmin.roles.user-roles', compact('users', 'roles'));
    }

    /**
     * Assign role to user
     */
    public function assignRole(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'role' => 'required|exists:roles,name',
            'admin_conference_id' => 'nullable|exists:conferences,id',
            'admin_zone_id' => 'nullable|exists:zones,id',
            'admin_branch_id' => 'nullable|exists:branches,id',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $user = User::findOrFail($request->user_id);
        $role = $request->role;

        DB::transaction(function () use ($user, $role, $request) {
            // Remove existing roles
            $user->syncRoles([]);
            
            // Clear admin level assignments
            $user->update([
                'admin_conference_id' => null,
                'admin_zone_id' => null,
                'admin_branch_id' => null,
            ]);

            // Assign new role
            $user->assignRole($role);

            // Set admin level if applicable
            if (in_array($role, ['conference_admin', 'zone_admin', 'branch_admin'])) {
                $updateData = [];
                
                if ($role === 'conference_admin' && $request->admin_conference_id) {
                    $updateData['admin_conference_id'] = $request->admin_conference_id;
                } elseif ($role === 'zone_admin' && $request->admin_zone_id) {
                    $updateData['admin_zone_id'] = $request->admin_zone_id;
                } elseif ($role === 'branch_admin' && $request->admin_branch_id) {
                    $updateData['admin_branch_id'] = $request->admin_branch_id;
                }
                
                if (!empty($updateData)) {
                    $user->update($updateData);
                }
            }
        });

        return back()->with('success', "Role '{$role}' assigned to {$user->name} successfully.");
    }

    /**
     * Remove role from user
     */
    public function removeRole(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'role' => 'required|exists:roles,name',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $user = User::findOrFail($request->user_id);
        $role = $request->role;

        DB::transaction(function () use ($user, $role) {
            $user->removeRole($role);
            
            // Clear admin level assignments if removing admin roles
            if (in_array($role, ['conference_admin', 'zone_admin', 'branch_admin'])) {
                $user->update([
                    'admin_conference_id' => null,
                    'admin_zone_id' => null,
                    'admin_branch_id' => null,
                ]);
            }
        });

        return back()->with('success', "Role '{$role}' removed from {$user->name} successfully.");
    }

    /**
     * Bulk assign roles
     */
    public function bulkAssignRoles(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'role' => 'required|exists:roles,name',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $users = User::whereIn('id', $request->user_ids)->get();
        $role = $request->role;
        $successCount = 0;

        DB::transaction(function () use ($users, $role, &$successCount) {
            foreach ($users as $user) {
                $user->syncRoles([$role]);
                $successCount++;
            }
        });

        return back()->with('success', "Role '{$role}' assigned to {$successCount} users successfully.");
    }

    /**
     * Add permission to role
     */
    public function addPermissionToRole(Request $request, Role $role)
    {
        $validator = Validator::make($request->all(), [
            'permission' => 'required|exists:permissions,name',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $permission = $request->permission;
        
        if (!$role->hasPermissionTo($permission)) {
            $role->givePermissionTo($permission);
            return back()->with('success', "Permission '{$permission}' added to role '{$role->name}' successfully.");
        }

        return back()->with('warning', "Role '{$role->name}' already has permission '{$permission}'.");
    }

    /**
     * Remove permission from role
     */
    public function removePermissionFromRole(Request $request, Role $role)
    {
        $validator = Validator::make($request->all(), [
            'permission' => 'required|exists:permissions,name',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $permission = $request->permission;
        
        if ($role->hasPermissionTo($permission)) {
            $role->revokePermissionTo($permission);
            return back()->with('success', "Permission '{$permission}' removed from role '{$role->name}' successfully.");
        }

        return back()->with('warning', "Role '{$role->name}' doesn't have permission '{$permission}'.");
    }

    /**
     * Create new role
     */
    public function createRole(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:roles,name',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        DB::transaction(function () use ($request) {
            $role = Role::create(['name' => $request->name]);
            
            if ($request->permissions) {
                $role->givePermissionTo($request->permissions);
            }
        });

        return back()->with('success', "Role '{$request->name}' created successfully.");
    }

    /**
     * Delete role
     */
    public function deleteRole(Role $role)
    {
        if ($role->users()->count() > 0) {
            return back()->with('error', "Cannot delete role '{$role->name}' because it has assigned users.");
        }

        $roleName = $role->name;
        $role->delete();

        return back()->with('success', "Role '{$roleName}' deleted successfully.");
    }

    /**
     * Get role assignment data for AJAX
     */
    public function getRoleData(Request $request)
    {
        $data = [];
        
        if ($request->has('conferences')) {
            $data['conferences'] = \App\Models\Conference::select('id', 'name')->get();
        }
        
        if ($request->has('zones') && $request->conference_id) {
            $data['zones'] = \App\Models\Zone::where('conference_id', $request->conference_id)
                ->select('id', 'name')->get();
        }
        
        if ($request->has('branches') && $request->zone_id) {
            $data['branches'] = \App\Models\Branch::where('zone_id', $request->zone_id)
                ->select('id', 'name')->get();
        }

        return response()->json($data);
    }
}
