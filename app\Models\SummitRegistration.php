<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SummitRegistration extends Model
{
    protected $fillable = [
        'user_id',
        'summit_id',
        'registration_number',
        'status',
        'total_amount',
        'paid_amount',
        'balance',
        'payment_complete',
        'registered_at',
        'special_requirements'
    ];

    protected $casts = [
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'balance' => 'decimal:2',
        'payment_complete' => 'boolean',
        'registered_at' => 'datetime'
    ];

    /**
     * Get the user that owns the registration.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the summit that owns the registration.
     */
    public function summit(): BelongsTo
    {
        return $this->belongsTo(Summit::class);
    }

    /**
     * Get the payments for the registration.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Calculate the balance amount.
     */
    public function calculateBalance(): float
    {
        return $this->total_amount - $this->paid_amount;
    }

    /**
     * Check if payment is complete.
     */
    public function isPaymentComplete(): bool
    {
        return $this->paid_amount >= $this->total_amount;
    }

    /**
     * Check if registration has any successful payments.
     */
    public function hasSuccessfulPayments(): bool
    {
        return $this->payments()->where('status', 'completed')->exists();
    }

    /**
     * Check if registration can be cancelled by user.
     */
    public function canBeCancelledByUser(): bool
    {
        // Cannot cancel if already cancelled
        if ($this->status === 'cancelled') {
            return false;
        }

        // Cannot cancel if has successful payments
        if ($this->hasSuccessfulPayments()) {
            return false;
        }

        // Cannot cancel after registration deadline
        if ($this->summit->registration_deadline < now()) {
            return false;
        }

        return true;
    }

    /**
     * Check if registration can be cancelled by admin.
     */
    public function canBeCancelledByAdmin(): bool
    {
        // Admin can cancel any registration except already cancelled ones
        return $this->status !== 'cancelled';
    }

    /**
     * Check if user can register for a summit.
     */
    public static function canUserRegisterForSummit($userId, $summitId): bool
    {
        // Check if user already has an active registration for this summit
        return !self::where('user_id', $userId)
            ->where('summit_id', $summitId)
            ->whereIn('status', ['pending', 'confirmed'])
            ->exists();
    }

    /**
     * Get user's active registration for a summit.
     */
    public static function getUserActiveRegistration($userId, $summitId)
    {
        return self::where('user_id', $userId)
            ->where('summit_id', $summitId)
            ->whereIn('status', ['pending', 'confirmed'])
            ->first();
    }
}
