@extends('layouts.admin')

@section('title', 'Minister Details')
@section('page-title', 'Minister Details')
@section('page-subtitle', 'View minister information')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>{{ $minister->name }}
                    </h5>
                    <div>
                        <a href="{{ route('admin.ministers.edit', $minister) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit me-2"></i>Edit
                        </a>
                        <form action="{{ route('admin.ministers.destroy', $minister) }}" method="POST" class="d-inline"
                              onsubmit="return confirm('Are you sure you want to delete this minister?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-sm">
                                <i class="fas fa-trash me-2"></i>Delete
                            </button>
                        </form>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center">
                            @if($minister->image)
                                <img src="{{ Storage::url($minister->image) }}" 
                                     alt="{{ $minister->name }}" 
                                     class="rounded-circle mb-3" 
                                     style="width: 150px; height: 150px; object-fit: cover;">
                            @else
                                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mb-3 mx-auto" 
                                     style="width: 150px; height: 150px;">
                                    <i class="fas fa-user fa-4x text-white"></i>
                                </div>
                            @endif
                            <div>
                                <span class="badge {{ $minister->is_active ? 'bg-success' : 'bg-secondary' }} fs-6">
                                    {{ $minister->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="30%">Full Name:</th>
                                    <td>{{ $minister->name }}</td>
                                </tr>
                                <tr>
                                    <th>Title:</th>
                                    <td>{{ $minister->title }}</td>
                                </tr>
                                @if($minister->position)
                                <tr>
                                    <th>Position:</th>
                                    <td>{{ $minister->position }}</td>
                                </tr>
                                @endif
                                @if($minister->email)
                                <tr>
                                    <th>Email:</th>
                                    <td>
                                        <a href="mailto:{{ $minister->email }}">{{ $minister->email }}</a>
                                    </td>
                                </tr>
                                @endif
                                @if($minister->phone)
                                <tr>
                                    <th>Phone:</th>
                                    <td>
                                        <a href="tel:{{ $minister->phone }}">{{ $minister->phone }}</a>
                                    </td>
                                </tr>
                                @endif
                                <tr>
                                    <th>Display Order:</th>
                                    <td>{{ $minister->order }}</td>
                                </tr>
                                <tr>
                                    <th>Created:</th>
                                    <td>{{ $minister->created_at->format('M d, Y \a\t g:i A') }}</td>
                                </tr>
                                <tr>
                                    <th>Last Updated:</th>
                                    <td>{{ $minister->updated_at->format('M d, Y \a\t g:i A') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if($minister->bio)
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6>Biography</h6>
                            <div class="bg-light p-3 rounded">
                                {!! nl2br(e($minister->bio)) !!}
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-cog me-2"></i>Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.ministers.edit', $minister) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>Edit Minister
                        </a>
                        <a href="{{ route('admin.ministers.index') }}" class="btn btn-secondary">
                            <i class="fas fa-list me-2"></i>All Ministers
                        </a>
                        <a href="{{ route('admin.ministers.create') }}" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>Add New Minister
                        </a>
                        <hr>
                        <form action="{{ route('admin.ministers.destroy', $minister) }}" method="POST"
                              onsubmit="return confirm('Are you sure you want to delete this minister? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="fas fa-trash me-2"></i>Delete Minister
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Information
                    </h6>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <i class="fas fa-eye me-2"></i>
                                {{ $minister->is_active ? 'This minister is visible on the landing page' : 'This minister is hidden from the landing page' }}
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-sort-numeric-up me-2"></i>
                                Display order determines the sequence on the landing page
                            </li>
                            <li>
                                <i class="fas fa-image me-2"></i>
                                {{ $minister->image ? 'Profile image is uploaded' : 'No profile image uploaded' }}
                            </li>
                        </ul>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
