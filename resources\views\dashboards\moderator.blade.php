@extends('layouts.app')

@section('title', 'Moderator Dashboard')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">🎯 Moderator Dashboard</h2>
                    <p class="text-muted mb-0">Event management and participant oversight</p>
                </div>
                <div>
                    <span class="badge bg-info fs-6">{{ Auth::user()->roles->first()->name ?? 'Moderator' }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100" style="background: #e6f4ea; border: none;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: rgba(40, 167, 69, 0.1);">
                                <i class="fas fa-users fa-2x text-success"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Total Registrations</h6>
                            <h3 class="mb-0">{{ $stats['total_registrations'] ?? 0 }}</h3>
                            <small class="text-success">
                                <i class="fas fa-user-plus"></i> All time
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100" style="background: #fff7e6; border: none;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: rgba(255, 193, 7, 0.1);">
                                <i class="fas fa-check-circle fa-2x text-warning"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Paid Registrations</h6>
                            <h3 class="mb-0">{{ $stats['paid_registrations'] ?? 0 }}</h3>
                            <small class="text-warning">
                                <i class="fas fa-credit-card"></i> Completed
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100" style="background: #e6f0ff; border: none;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: rgba(0, 123, 255, 0.1);">
                                <i class="fas fa-id-card fa-2x text-primary"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">ID Cards Generated</h6>
                            <h3 class="mb-0">{{ $stats['id_cards_generated'] ?? 0 }}</h3>
                            <small class="text-primary">
                                <i class="fas fa-qrcode"></i> Active
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100" style="background: #f0e6ff; border: none;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: rgba(111, 66, 193, 0.1);">
                                <i class="fas fa-calendar-check fa-2x" style="color: #6f42c1;"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Upcoming Activities</h6>
                            <h3 class="mb-0">{{ $upcomingActivities ? $upcomingActivities->count() : 0 }}</h3>
                            <small style="color: #6f42c1;">
                                <i class="fas fa-clock"></i> Scheduled
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Registrations and Upcoming Activities -->
    <div class="row">
        <!-- Recent Registrations -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>Recent Registrations
                    </h5>
                    <a href="{{ route('admin.registrations.index') }}" class="btn btn-sm btn-outline-primary">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    @if($recentRegistrations && $recentRegistrations->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Participant</th>
                                        <th>University</th>
                                        <th>Payment Status</th>
                                        <th>Registered</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentRegistrations as $registration)
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ $registration->user->full_name }}</strong><br>
                                                <small class="text-muted">{{ $registration->user->email }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <small>{{ $registration->user->university->name ?? 'N/A' }}</small>
                                        </td>
                                        <td>
                                            @if($registration->payment_complete)
                                                <span class="badge bg-success">Paid</span>
                                            @else
                                                <span class="badge bg-warning">Pending</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small>{{ $registration->registered_at->format('M j, Y') }}</small>
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.registrations.show', $registration) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No recent registrations found.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Upcoming Activities -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>Upcoming Activities
                    </h5>
                </div>
                <div class="card-body">
                    @if($upcomingActivities && $upcomingActivities->count() > 0)
                        @foreach($upcomingActivities as $activity)
                        <div class="d-flex align-items-start mb-3">
                            <div class="flex-shrink-0">
                                <div class="rounded-circle p-2" style="background: rgba(0, 123, 255, 0.1);">
                                    <i class="fas fa-calendar text-primary"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">{{ $activity->title }}</h6>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    {{ $activity->start_time->format('M j, H:i') }}
                                </small><br>
                                <small class="text-muted">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    {{ $activity->venue }}
                                </small>
                            </div>
                        </div>
                        @if(!$loop->last)
                            <hr class="my-2">
                        @endif
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No upcoming activities.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tools me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('admin.registrations.index') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-users me-2"></i>Manage Registrations
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('admin.id-cards.index') }}" class="btn btn-outline-success w-100">
                                <i class="fas fa-id-card me-2"></i>ID Card Management
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('meal-tracking.scanner') }}" class="btn btn-outline-info w-100">
                                <i class="fas fa-qrcode me-2"></i>QR Scanner
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('meal-tracking.dashboard') }}" class="btn btn-outline-warning w-100">
                                <i class="fas fa-utensils me-2"></i>Meal Tracking
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .card {
        transition: transform 0.2s, box-shadow 0.2s;
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    .table th {
        border-top: none;
        font-weight: 600;
        color: #6c757d;
        font-size: 0.875rem;
    }
    
    .badge {
        font-size: 0.75rem;
    }
</style>
@endpush
