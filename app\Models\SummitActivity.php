<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SummitActivity extends Model
{
    protected $fillable = [
        'summit_id',
        'title',
        'description',
        'type',
        'start_time',
        'end_time',
        'venue',
        'facilitator',
        'is_mandatory'
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'is_mandatory' => 'boolean'
    ];

    /**
     * Get the summit that owns the activity.
     */
    public function summit(): BelongsTo
    {
        return $this->belongsTo(Summit::class);
    }

    /**
     * Get the meal trackings for the activity.
     */
    public function mealTrackings(): HasMany
    {
        return $this->hasMany(MealTracking::class);
    }
}
