<?php $__env->startSection('title', 'ID Cards Management'); ?>
<?php $__env->startSection('page-title', 'ID Cards Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">ID Cards Management</h2>
                <p class="text-muted">Manage and monitor summit ID cards.</p>
            </div>
            <div class="d-flex gap-2">
                <a href="<?php echo e(route('admin.id-cards.export')); ?>" class="btn btn-success">
                    <i class="fas fa-download me-2"></i>Export CSV
                </a>
                <a href="<?php echo e(route('admin.id-cards.statistics')); ?>" class="btn btn-info">
                    <i class="fas fa-chart-bar me-2"></i>Statistics
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="modern-stats-card blue h-100">
            <div class="stat-icon">
                <i class="fas fa-id-card"></i>
            </div>
            <h3 class="stat-number"><?php echo e(number_format($statistics['total_cards'])); ?></h3>
            <p class="stat-label">Total Cards</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="modern-stats-card green h-100">
            <div class="stat-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h3 class="stat-number"><?php echo e(number_format($statistics['active_cards'])); ?></h3>
            <p class="stat-label">Active Cards</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="modern-stats-card orange h-100">
            <div class="stat-icon">
                <i class="fas fa-clock"></i>
            </div>
            <h3 class="stat-number"><?php echo e(number_format($statistics['expired_cards'])); ?></h3>
            <p class="stat-label">Expired Cards</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="modern-stats-card purple h-100">
            <div class="stat-icon">
                <i class="fas fa-calendar"></i>
            </div>
            <h3 class="stat-number"><?php echo e(number_format($statistics['cards_this_month'])); ?></h3>
            <p class="stat-label">This Month</p>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="payment-filter-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
            </div>
            <div class="card-body">
                <form method="GET" action="<?php echo e(route('admin.id-cards.index')); ?>">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" name="search" id="search" class="form-control" 
                                       value="<?php echo e(request('search')); ?>" placeholder="Card number, name, email...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="summit_id" class="form-label">Summit</label>
                                <select name="summit_id" id="summit_id" class="form-select">
                                    <option value="">All Summits</option>
                                    <?php $__currentLoopData = $summits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $summit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($summit->id); ?>" <?php echo e(request('summit_id') == $summit->id ? 'selected' : ''); ?>>
                                            <?php echo e($summit->title); ?> (<?php echo e($summit->year); ?>)
                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select name="status" id="status" class="form-select">
                                    <option value="">All Status</option>
                                    <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                                    <option value="expired" <?php echo e(request('status') == 'expired' ? 'selected' : ''); ?>>Expired</option>
                                    <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>Filter
                                    </button>
                                    <a href="<?php echo e(route('admin.id-cards.index')); ?>" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>Clear
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- ID Cards Table -->
<div class="row">
    <div class="col-12">
        <div class="payment-table-card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-table me-2"></i>ID Cards (<?php echo e($idCards->total()); ?>)</h5>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="bulkAction('deactivate')" disabled id="bulkActionBtn">
                            <i class="fas fa-ban me-2"></i>Bulk Deactivate
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php if($idCards->count() > 0): ?>
                <form id="bulkForm" method="POST" action="<?php echo e(route('admin.id-cards.bulk-action')); ?>">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="action" id="bulkAction">
                    
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th>Card Number</th>
                                    <th>User</th>
                                    <th>Summit</th>
                                    <th>Branch</th>
                                    <th>Status</th>
                                    <th>Issued</th>
                                    <th>Expires</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $idCards; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $card): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" name="card_ids[]" value="<?php echo e($card->id); ?>" class="form-check-input card-checkbox">
                                    </td>
                                    <td>
                                        <span class="badge bg-dark"><?php echo e($card->card_number); ?></span>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-bold"><?php echo e($card->user->full_name); ?></div>
                                            <small class="text-muted"><?php echo e($card->user->email); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-bold"><?php echo e($card->summit->title); ?></div>
                                            <small class="text-muted"><?php echo e($card->summit->year); ?></small>
                                        </div>
                                    </td>
                                    <td><?php echo e($card->user->branch->name ?? 'N/A'); ?></td>
                                    <td>
                                        <?php if($card->is_active && $card->expires_at > now()): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php elseif($card->expires_at < now()): ?>
                                            <span class="badge bg-warning">Expired</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($card->issued_at->format('M j, Y')); ?></td>
                                    <td><?php echo e($card->expires_at->format('M j, Y')); ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?php echo e(route('admin.id-cards.show', $card)); ?>"
                                               class="btn btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if($card->is_active): ?>
                                                <form method="POST" action="<?php echo e(route('admin.id-cards.deactivate', $card)); ?>" class="d-inline">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('PATCH'); ?>
                                                    <button type="submit" class="btn btn-outline-warning" title="Deactivate"
                                                            onclick="return confirm('Deactivate this ID card?')">
                                                        <i class="fas fa-ban"></i>
                                                    </button>
                                                </form>
                                            <?php else: ?>
                                                <form method="POST" action="<?php echo e(route('admin.id-cards.reactivate', $card)); ?>" class="d-inline">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('PATCH'); ?>
                                                    <button type="submit" class="btn btn-outline-success" title="Reactivate">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </form>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($idCards->appends(request()->query())->links()); ?>

                </div>
                <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-id-card fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No ID Cards Found</h5>
                    <p class="text-muted">No ID cards match your current filters.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.card-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateBulkActionButton();
});

// Update bulk action button state
document.querySelectorAll('.card-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateBulkActionButton);
});

function updateBulkActionButton() {
    const checkedBoxes = document.querySelectorAll('.card-checkbox:checked');
    const bulkBtn = document.getElementById('bulkActionBtn');
    
    if (checkedBoxes.length > 0) {
        bulkBtn.disabled = false;
        bulkBtn.textContent = `Bulk Action (${checkedBoxes.length})`;
    } else {
        bulkBtn.disabled = true;
        bulkBtn.textContent = 'Bulk Action';
    }
}

function bulkAction(action) {
    const checkedBoxes = document.querySelectorAll('.card-checkbox:checked');
    if (checkedBoxes.length === 0) {
        alert('Please select at least one ID card.');
        return;
    }
    
    if (confirm(`Are you sure you want to ${action} ${checkedBoxes.length} ID card(s)?`)) {
        document.getElementById('bulkAction').value = action;
        document.getElementById('bulkForm').submit();
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\summit-management-system\resources\views/admin/id-cards/index.blade.php ENDPATH**/ ?>