@extends('layouts.user')

@section('title', 'My Payments')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <h2 class="mb-0">My Payments</h2>
        <p class="text-muted">View your payment history and transaction details.</p>
    </div>
</div>

@if($payments->count() > 0)
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Payment History</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Reference</th>
                                <th>Summit</th>
                                <th>Amount</th>
                                <th>Method</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($payments as $payment)
                            <tr>
                                <td>
                                    <div>
                                        <div class="fw-bold">#{{ $payment->payment_reference }}</div>
                                        @if($payment->transaction_id)
                                            <small class="text-muted">{{ $payment->transaction_id }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-bold">{{ $payment->summitRegistration->summit->title }}</div>
                                        <small class="text-muted">{{ $payment->summitRegistration->summit->year }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-bold text-success">TSh {{ number_format($payment->amount) }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ ucfirst($payment->payment_method) }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $payment->status === 'completed' ? 'success' : ($payment->status === 'pending' ? 'warning' : 'danger') }}">
                                        {{ ucfirst($payment->status) }}
                                    </span>
                                </td>
                                <td>
                                    <div>
                                        <div>{{ $payment->created_at->format('M j, Y') }}</div>
                                        @if($payment->paid_at)
                                            <small class="text-muted">Paid: {{ $payment->paid_at->format('M j, Y') }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <a href="{{ route('payments.show', $payment) }}" 
                                       class="btn btn-outline-primary btn-sm" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $payments->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
@else
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Payments Yet</h5>
                <p class="text-muted">You haven't made any payments yet.</p>
                <a href="{{ route('registrations.index') }}" class="btn btn-primary">
                    <i class="fas fa-clipboard-list me-2"></i>View Registrations
                </a>
            </div>
        </div>
    </div>
</div>
@endif
@endsection
