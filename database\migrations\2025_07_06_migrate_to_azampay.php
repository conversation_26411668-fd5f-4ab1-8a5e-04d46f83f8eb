<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the existing constraint first
        DB::statement("ALTER TABLE payments DROP CONSTRAINT IF EXISTS payments_payment_method_check");

        // Migrate existing data to compatible values
        DB::table('payments')
            ->where('payment_method', 'mobile_money')
            ->update(['payment_method' => 'mobile_money_direct']);

        // Add new fields for AzamPay
        Schema::table('payments', function (Blueprint $table) {
            $table->string('gateway_provider')->nullable()->after('payment_method'); // 'selcom', 'azampay', 'manual'
            $table->json('provider_data')->nullable()->after('gateway_data'); // Provider-specific data
            $table->string('external_reference')->nullable()->after('transaction_id'); // External system reference
            $table->timestamp('gateway_callback_at')->nullable()->after('paid_at'); // When gateway called back
        });

        // Create new constraint with AzamPay methods
        DB::statement("ALTER TABLE payments ADD CONSTRAINT payments_payment_method_check CHECK (payment_method IN ('selcom', 'azampay_mobile', 'azampay_bank', 'bank_transfer', 'cash', 'mobile_money_direct'))");

        // Migrate existing Selcom payments
        DB::table('payments')
            ->where('payment_method', 'selcom')
            ->update([
                'gateway_provider' => 'selcom',
                'updated_at' => now()
            ]);

        // Update other payment methods
        DB::table('payments')
            ->whereIn('payment_method', ['bank_transfer', 'cash', 'mobile_money_direct'])
            ->update([
                'gateway_provider' => 'manual',
                'updated_at' => now()
            ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove new fields
        Schema::table('payments', function (Blueprint $table) {
            $table->dropColumn(['gateway_provider', 'provider_data', 'external_reference', 'gateway_callback_at']);
        });

        // Restore original enum
        DB::statement("ALTER TABLE payments DROP CONSTRAINT payments_payment_method_check");
        DB::statement("ALTER TABLE payments ADD CONSTRAINT payments_payment_method_check CHECK (payment_method IN ('selcom', 'bank_transfer', 'cash', 'mobile_money'))");
    }
};
