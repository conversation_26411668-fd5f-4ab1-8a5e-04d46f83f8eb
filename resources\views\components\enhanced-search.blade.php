@props([
    'type' => 'users',
    'placeholder' => 'Start typing to search...',
    'route' => null,
    'showFilters' => true,
    'filters' => []
])

<div class="enhanced-search-container">
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-search me-2"></i>Enhanced Search & Filter
            </h5>
        </div>
        <div class="card-body">
            <!-- Search Input with Autocomplete -->
            <div class="row mb-3">
                <div class="col-md-8">
                    <div class="position-relative">
                        <input type="text" 
                               id="enhanced-search-input" 
                               class="form-control form-control-lg" 
                               placeholder="{{ $placeholder }}"
                               autocomplete="off"
                               data-search-type="{{ $type }}">
                        <div class="search-spinner d-none">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                        <div class="search-clear-btn d-none">
                            <i class="fas fa-times"></i>
                        </div>
                        
                        <!-- Search Results Dropdown -->
                        <div id="search-results-dropdown" class="search-dropdown d-none">
                            <div class="search-results-container">
                                <!-- Results will be populated here -->
                            </div>
                            <div class="search-footer">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Type at least 2 characters to search
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex gap-2">
                        <button type="button" id="clear-search" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Clear
                        </button>
                        @if($route)
                        <button type="button" id="traditional-search" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Search
                        </button>
                        @endif
                    </div>
                </div>
            </div>

            @if($showFilters && count($filters) > 0)
            <!-- Traditional Filters -->
            <form method="GET" action="{{ $route }}" id="filter-form">
                <div class="row">
                    @foreach($filters as $filter)
                    <div class="col-md-{{ $filter['width'] ?? 3 }}">
                        <div class="mb-3">
                            <label for="{{ $filter['name'] }}" class="form-label">{{ $filter['label'] }}</label>
                            @if($filter['type'] === 'select')
                                <select name="{{ $filter['name'] }}" id="{{ $filter['name'] }}" class="form-select">
                                    <option value="">{{ $filter['placeholder'] ?? 'All' }}</option>
                                    @foreach($filter['options'] as $value => $label)
                                        <option value="{{ $value }}" {{ request($filter['name']) == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            @else
                                <input type="{{ $filter['type'] ?? 'text' }}" 
                                       name="{{ $filter['name'] }}" 
                                       id="{{ $filter['name'] }}" 
                                       class="form-control"
                                       value="{{ request($filter['name']) }}"
                                       placeholder="{{ $filter['placeholder'] ?? '' }}">
                            @endif
                        </div>
                    </div>
                    @endforeach
                    
                    <div class="col-md-12">
                        <div class="d-flex gap-2">
                            <a href="{{ $route }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Clear All Filters
                            </a>
                        </div>
                    </div>
                </div>
            </form>
            @endif
        </div>
    </div>
</div>

<style>
.enhanced-search-container {
    margin-bottom: 1.5rem;
}

.search-spinner {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.search-clear-btn {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.search-clear-btn:hover {
    background-color: #f8f9fa;
    color: #dc3545;
}

.search-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    z-index: 1050;
    max-height: 400px;
    overflow-y: auto;
}

.search-results-container {
    max-height: 350px;
    overflow-y: auto;
}

.search-result-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f8f9fa;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out;
}

.search-result-item:hover,
.search-result-item.active {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-text {
    font-weight: 500;
    color: #212529;
    margin-bottom: 2px;
}

.search-result-subtitle {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 2px;
}

.search-result-meta {
    font-size: 0.75rem;
    color: #adb5bd;
}

.search-footer {
    padding: 8px 16px;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

.no-results {
    padding: 20px;
    text-align: center;
    color: #6c757d;
}

.search-loading {
    padding: 20px;
    text-align: center;
    color: #6c757d;
}

/* Keyboard navigation highlight */
.search-result-item.keyboard-active {
    background-color: #e3f2fd;
    border-left: 3px solid #2196f3;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .search-dropdown {
        max-height: 300px;
    }
    
    .search-result-item {
        padding: 10px 12px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('enhanced-search-input');
    const searchDropdown = document.getElementById('search-results-dropdown');
    const searchSpinner = document.querySelector('.search-spinner');
    const searchClearBtn = document.querySelector('.search-clear-btn');
    const clearButton = document.getElementById('clear-search');
    const traditionalSearchButton = document.getElementById('traditional-search');
    
    let searchTimeout;
    let currentResults = [];
    let selectedIndex = -1;
    let isDropdownVisible = false;

    // Search input event listener
    searchInput.addEventListener('input', function() {
        const query = this.value.trim();

        // Show/hide clear button
        if (query.length > 0) {
            searchClearBtn.classList.remove('d-none');
        } else {
            searchClearBtn.classList.add('d-none');
        }

        clearTimeout(searchTimeout);

        if (query.length < 2) {
            hideDropdown();
            return;
        }

        searchTimeout = setTimeout(() => {
            performSearch(query);
        }, 300); // Debounce for 300ms
    });

    // Clear button in search input
    if (searchClearBtn) {
        searchClearBtn.addEventListener('click', function() {
            searchInput.value = '';
            searchClearBtn.classList.add('d-none');
            hideDropdown();
            searchInput.focus();
        });
    }

    // Keyboard navigation
    searchInput.addEventListener('keydown', function(e) {
        if (!isDropdownVisible) return;
        
        switch(e.key) {
            case 'ArrowDown':
                e.preventDefault();
                selectedIndex = Math.min(selectedIndex + 1, currentResults.length - 1);
                updateSelection();
                break;
            case 'ArrowUp':
                e.preventDefault();
                selectedIndex = Math.max(selectedIndex - 1, -1);
                updateSelection();
                break;
            case 'Enter':
                e.preventDefault();
                if (selectedIndex >= 0 && currentResults[selectedIndex]) {
                    window.location.href = currentResults[selectedIndex].url;
                }
                break;
            case 'Escape':
                hideDropdown();
                break;
        }
    });

    // Clear search
    clearButton.addEventListener('click', function() {
        searchInput.value = '';
        hideDropdown();
        searchInput.focus();
    });

    // Traditional search button
    if (traditionalSearchButton) {
        traditionalSearchButton.addEventListener('click', function() {
            const form = document.getElementById('filter-form');
            const searchValue = searchInput.value.trim();

            // Add search value to form
            let searchField = form.querySelector('input[name="search"]');
            if (!searchField) {
                searchField = document.createElement('input');
                searchField.type = 'hidden';
                searchField.name = 'search';
                form.appendChild(searchField);
            }
            searchField.value = searchValue;

            form.submit();
        });
    }

    // Auto-submit form on filter changes
    const filterForm = document.getElementById('filter-form');
    if (filterForm) {
        const filterInputs = filterForm.querySelectorAll('select, input[type="date"], input[type="text"]:not(#enhanced-search-input)');

        filterInputs.forEach(input => {
            if (input.type === 'text') {
                // For text inputs, use debounced input
                let timeout;
                input.addEventListener('input', function() {
                    clearTimeout(timeout);
                    timeout = setTimeout(() => {
                        filterForm.submit();
                    }, 800); // 800ms delay for text inputs
                });
            } else {
                // For selects and date inputs, submit immediately
                input.addEventListener('change', function() {
                    filterForm.submit();
                });
            }
        });
    }

    // Click outside to close dropdown
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !searchDropdown.contains(e.target)) {
            hideDropdown();
        }
    });

    function performSearch(query) {
        const searchType = searchInput.dataset.searchType;
        
        showSpinner();
        
        fetch(`/api/search?q=${encodeURIComponent(query)}&type=${searchType}&limit=8`)
            .then(response => response.json())
            .then(data => {
                hideSpinner();
                displayResults(data.results, query);
            })
            .catch(error => {
                hideSpinner();
                console.error('Search error:', error);
                showError('Search failed. Please try again.');
            });
    }

    function displayResults(results, query) {
        currentResults = results;
        selectedIndex = -1;
        
        const container = searchDropdown.querySelector('.search-results-container');
        
        if (results.length === 0) {
            container.innerHTML = `
                <div class="no-results">
                    <i class="fas fa-search mb-2"></i>
                    <div>No results found for "${query}"</div>
                    <small class="text-muted">Try different keywords or check spelling</small>
                </div>
            `;
        } else {
            container.innerHTML = results.map((result, index) => `
                <div class="search-result-item" data-index="${index}" onclick="window.location.href='${result.url}'">
                    <div class="search-result-text">${highlightMatch(result.text, query)}</div>
                    <div class="search-result-subtitle">${highlightMatch(result.subtitle, query)}</div>
                    <div class="search-result-meta">${result.meta}</div>
                </div>
            `).join('');
        }
        
        showDropdown();
    }

    function highlightMatch(text, query) {
        if (!text || !query) return text || '';
        
        const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    function updateSelection() {
        const items = searchDropdown.querySelectorAll('.search-result-item');
        
        items.forEach((item, index) => {
            item.classList.remove('keyboard-active');
            if (index === selectedIndex) {
                item.classList.add('keyboard-active');
                item.scrollIntoView({ block: 'nearest' });
            }
        });
    }

    function showDropdown() {
        searchDropdown.classList.remove('d-none');
        isDropdownVisible = true;
    }

    function hideDropdown() {
        searchDropdown.classList.add('d-none');
        isDropdownVisible = false;
        selectedIndex = -1;
    }

    function showSpinner() {
        searchSpinner.classList.remove('d-none');
    }

    function hideSpinner() {
        searchSpinner.classList.add('d-none');
    }

    function showError(message) {
        const container = searchDropdown.querySelector('.search-results-container');
        container.innerHTML = `
            <div class="no-results">
                <i class="fas fa-exclamation-triangle text-warning mb-2"></i>
                <div>${message}</div>
            </div>
        `;
        showDropdown();
    }
});
</script>
