<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('first_name')->after('name');
            $table->string('last_name')->after('first_name');
            $table->string('phone')->nullable()->after('email');
            $table->date('date_of_birth')->nullable()->after('phone');
            $table->enum('gender', ['male', 'female'])->nullable()->after('date_of_birth');
            $table->string('profile_image')->nullable()->after('gender');
            $table->foreignId('branch_id')->nullable()->constrained()->onDelete('set null')->after('profile_image');
            $table->foreignId('university_id')->nullable()->constrained()->onDelete('set null')->after('branch_id');
            $table->string('student_id')->nullable()->after('university_id');
            $table->year('year_of_study')->nullable()->after('student_id');
            $table->string('course')->nullable()->after('year_of_study');
            $table->text('address')->nullable()->after('course');
            $table->string('emergency_contact_name')->nullable()->after('address');
            $table->string('emergency_contact_phone')->nullable()->after('emergency_contact_name');
            $table->boolean('is_active')->default(true)->after('emergency_contact_phone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'first_name', 'last_name', 'phone', 'date_of_birth', 'gender',
                'profile_image', 'branch_id', 'university_id', 'student_id',
                'year_of_study', 'course', 'address', 'emergency_contact_name',
                'emergency_contact_phone', 'is_active'
            ]);
        });
    }
};
