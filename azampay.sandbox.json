{"openapi": "3.0.1", "schemes": ["https"], "info": {"title": "Azampay API", "version": "v1", "description": "\n\n# Introduction\n<p style='text-align: justify !important'>AzamPay is specialized in the development of end-to-end online payment management solutions for companies operating in East Africa. Our range of digital solutions and services are carefully designed not only to streamline your payment and collection processes, but to also allow easy integration with your current Accounting or Enterprise Resource Planning (ERP) systems thus leaving you time to focus on your customers. AzamPay offers bespoke solutions that guarantee optimal business performance and efficiency whether you are transacting locally, regionally, or internationally.</p> \n\n<p style='text-align: justify !important'>We strive to consistently improve our products to better meet the needs of a dynamic East African payments environment. As an AzamPay client, you will be able to leverage your presence across East Africa and extend your services regionally. Remember, we endeavour to follow you throughout your business adventure.</p> \n\n<image src='../../overview.svg' alt='Azampay Overview Image' style='width:100% !important; height:300px; box-sizing: content-box;'/>.\n\n# Base Urls\n Sandbox\n - <strong style='pointer-events:none'>Authenticator Sandbox Base Url: https://authenticator-sandbox.azampay.co.tz</strong>. \n- <strong style='pointer-events:none'>Azampay Sandbox Checkout Base Url: https://sandbox.azampay.co.tz</strong>.  \n\n# Azampay API Flow\nAll Azampay APIs follow two step process:\n - Get token against the application authentication credentials. \n\nFollowing diagram shows the general flow on how to consume the Azampay api.\n\n\n<image src='../../flow-diagrams/checkout-flow.png' alt='Checkout Flow Diagram' style='width:450px !important;'/>\n\n# Authentication\n\nAzampay offers one form of authentication to ensure secure access to your account:\n   - Bearer Auth - an open protocol to allow secure authorization in a simple\nand standard method from web, mobile and desktop applications.\n\n\n\n <p style='text-align: justify !important'></p> \n\n Bearer Token is the JWT token that you get against your application Name, Client Id and Client Secret. For Sandbox Environment, You can get these application credentials from Sandbox portal. For production environment, you will be provided these keys after you submit your business KYC to Azampay from Sandbox portal.</p>\n", "x-logo": {"url": "../../download.png"}, "x-providerName": "azampay.com", "x-unofficialSpec": true}, "produces": ["application/json"], "tags": [{"description": "", "name": "Token Generation"}, {"description": "Checkout api can be used by online web shop or any e-commerce shop /application to request payment for their consumers. To make this transaction secure, every application must use SSL/https and every application must get Token from Azampay authentication api using their credentials. Then general flow is explained in the following diagram.  \n<image src='../../flow-diagrams/checkout-flow.png' alt='Checkout Flow Diagram' style='width:500px !important;'/> \n\n<p style='text-align:center'><strong>Flow Diagram</strong></p>", "name": "Checkout"}, {"description": "<strong>Flow Diagram</strong> \n<image src='../../flow-diagrams/openapi-flow.svg' alt='Open API Flow Diagram' style='width:500px !important;'/> ", "name": "Open API"}, {"name": "Checkout Pages", "description": ""}, {"name": "OTP", "description": "<p>Welcome to the API documentation for AzamPay, the premier payment platform for businesses in Tanzania. With our cutting-edge technology, we offer two essential APIs for businesses looking to manage their financial transactions seamlessly: our money transfer API for transfers from other countries to Tanzania and our disburse API for internal transfers within Tanzania.</p> <p>Our money transfer API offers businesses a reliable, fast, and secure way to receive international payments, with support for various payment methods and currencies. With our disburse API, businesses can easily disburse funds to employees, vendors, or other stakeholders within Tanzania, making payments simple, fast, and reliable.</p>  <p>This API documentation provides all the necessary information for developers to integrate these APIs into their applications easily. It includes details on authentication, endpoints, input parameters, response formats, and error handling, as well as code samples and SDKs to make integration a breeze.</p>    <p>With AzamPay's money transfer API and disburse API, businesses in Tanzania can now manage their finances more effectively, providing a hassle-free payment experience to their customers and stakeholders.</p> <h1 style=\"color: #333333;\" class=\"sc-ftTHYK eElxvP\"><a class=\"sc-csuSiG eEiixI\" href=\"#tag/Disbursement\" aria-label=\"tag/Disbursement\"></a>Disbursement API Flow</h1> <p>Following diagram shows the general flow on how to consume the Disbursement api.\n\n\n<image src='../../flow-diagrams/disbursement-flow.png' alt='Checkout Flow Diagram' style='width:450px !important;'/></p>"}, {"name": "Disbursement", "description": "<p>Welcome to the API documentation for AzamPay, the premier payment platform for businesses in Tanzania. With our cutting-edge technology, we offer two essential APIs for businesses looking to manage their financial transactions seamlessly: our money transfer API for transfers from other countries to Tanzania and our disburse API for internal transfers within Tanzania.</p> <p>Our money transfer API offers businesses a reliable, fast, and secure way to receive international payments, with support for various payment methods and currencies. With our disburse API, businesses can easily disburse funds to employees, vendors, or other stakeholders within Tanzania, making payments simple, fast, and reliable.</p>  <p>This API documentation provides all the necessary information for developers to integrate these APIs into their applications easily. It includes details on authentication, endpoints, input parameters, response formats, and error handling, as well as code samples and SDKs to make integration a breeze.</p>    <p>With AzamPay's money transfer API and disburse API, businesses in Tanzania can now manage their finances more effectively, providing a hassle-free payment experience to their customers and stakeholders.</p> <h1 style=\"color: #333333;\" class=\"sc-ftTHYK eElxvP\"><a class=\"sc-csuSiG eEiixI\" href=\"#tag/Disbursement\" aria-label=\"tag/Disbursement\"></a>Disbursement API Flow</h1> <p>Following diagram shows the general flow on how to consume the Disbursement api.\n\n\n<image src='../../flow-diagrams/disbursement-flow.png' alt='Checkout Flow Diagram' style='width:450px !important;'/></p>"}, {"name": "Bill Pay API", "description": "<h2>Authentication</h2> <p> The API requires that a JWT token is passed in the Authorization header. This token will be generated by our system using the HMAC algorithm and hashed with SHA256. This is a symmetric algorithm, shortened as HS256, that uses a shared secret (to be provided) to sign the token. The generated token will have a maximum validity of 120 seconds. The JWT token will contain <code>userId</code>, expirationTime (<code>exp</code>), and issuedAtTime (<code>iat</code>). It is the merchant's responsibility to validate the token and the hash. <strong>The secret key will be shared privately.</strong> </p> <h2>Authentication Verification</h2> <p>To verify the JWT token, the merchant will need to:</p> <ol> <li>Decode the JWT token using the shared secret key and HS256 algorithm.</li> <li>Validate the <code>userId</code>, expirationTime (<code>exp</code>), and issuedAtTime (<code>iat</code>) claims.</li> <li>Ensure that the expirationTime (<code>exp</code>) is within the 120-second validity period.</li> <li>Confirm that the token has not expired by comparing the expirationTime (<code>exp</code>) with the current time.</li> </ol> <h2>Hash Calculation</h2> <p> The hash will be generated using all request data fields concatenated in a consistent order. The resulting string will then be hashed using SHA256 and signed using the same secret key used for JWT generation. The hash ensures the integrity and authenticity of the request data. </p> <p>Example of hash calculation:</p> <ol> <li>Convert the data object to a minified JSON string.</li> <li>Compute the SHA256 hash of the JSON string.</li> <li>Sign the hash with the secret key using HMAC-SHA256.</li> </ol> <h2>Hash Verification</h2> <p>To verify the hash, the merchant will need to:</p> <ol> <li>Convert the data object to a JSON string.</li> <li>Compute the SHA256 hash of the JSON string.</li> <li>Sign the computed hash with the shared secret key using HMAC-SHA256.</li> <li>Compare the resulting hash with the Hash field provided in the request. If they match, the request is verified; otherwise, it is considered tampered with.</li> </ol>"}], "x-tagGroups": [{"name": "APIs", "tags": ["Token Generation", "Checkout API", "Checkout Pages", "Disbursement", "Bill Pay API"]}], "paths": {"/AppRegistration/GenerateToken": {"post": {"servers": [{"url": "https://authenticator-sandbox.azampay.co.tz", "description": "Sandbox authenticator server"}], "description": "Generate the access token in order to access Azampay public end points.", "x-codeSamples": [{"lang": "Node JS", "source": "const fetch = require('node-fetch');\nconst data = {'appName': 'myApp', 'clientId': '0000-0000', 'clientSecret': 'XRTRFF123ADED$FFAER'};\nfetch('{BaseUrl}/AppRegistration/GenerateToken', {\n method: 'POST',\n body: data, \n})\n.then(res => { /* response */ })\n.catch(err => { /* error */ });"}, {"lang": ".Net", "source": "public class Data {\n public string AppName { get; set; }\n public string ClientId { get; set; }\n public string ClientSecret { get; set; }\n}\nData data = new Data() { AppName = \"App\",ClientId = \"0000\",ClientSecret = \"SKEY\" };\nHttpClient client = new HttpClient();\nclient.BaseAddress = new Uri(\"{BaseUrl}/AppRegistration/\"); \nclient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(\"application/json\"));\nHttpResponseMessage response = client.PostAsJsonAsync(\"GenerateToken\", data).Result; \nif (response.statusCode == 200) { // success } \nelse { // error }"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenGeneration"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenGeneration200Response"}}}}, "423": {"description": "Invalid detail", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenGeneration423Response"}}}}, "500": {"description": "Internal Server Error", "content": {}}}, "summary": "Generate Token For App", "tags": ["Token Generation"]}}, "/api/v1/OpenApi/{paymentProvider}/checkuser": {"post": {"description": "", "parameters": [{"name": "paymentprovider", "in": "path", "required": true, "schema": {"type": "string"}}], "x-codeSamples": [{"lang": "Node JS", "source": "const fetch = require('node-fetch');\nconst paymentProvider = 'Payment Provider';\nconst data = {'version': 1, 'method': null,'data':{'amount': 2000}, 'checksum': null};\nconst  headers = { 'Content-Type': 'application/json', 'Authorization': 'Bearer ' + TOKEN, 'X-API-Key': 'API-KEY'};\nfetch(`{BaseUrl}/api/v1/OpenApi/${paymentProvider}/checkuser`, {\n method: 'POST',\n headers, \nbody: data, \n})\n.then(res => { /* response */})\n.catch(err => {/* error */});"}, {"lang": ".Net", "source": "public class PaymentData { \n public int Amount { get; set; } \n public string? BankCode { get; set; } \n public string? CustomerNumber { get; set; } \n public string? CustomerReferenceNumber { get; set; } \n public string? RequestId { get; set; } \n public string? ServiceType { get; set; } \n public string? TxnReferenceNumber { get; set; }\n}\npublic class Model {\n public int Version { get; set; }\n public string? Method { get; set; }\n public PaymentData Data { get; set; }\n public string? Checksum { get; set; }\n}\nModel data = new Model() { Version = 1, Data = new PaymentData() { Amount = 2000 } };\nHttpClient client = new HttpClient();\nclient.BaseAddress = new Uri(\"{BaseUrl}/api/v1/OpenApi/\"); \nclient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(\"application/json\"));\nhttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(\"Bearer\", TOKEN); \nclient.DefaultRequestHeaders.Add(\"x-api-key\", KEY)\nHttpResponseMessage response = client.PostAsJsonAsync(string.Format(\"{paymentProvider}/checkuser\", PAYMENT-PROVIDER), data).Result; \nif (response.statusCode == 200) { // success } \nelse { // error }"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerNameLookupReponse"}}}}}, "security": [{"Bearer Auth": [], "x-api-key": []}], "summary": "Check User", "tags": ["Open API"]}}, "/api/v1/OpenApi/{paymentProvider}/paybill": {"post": {"description": "", "parameters": [{"name": "paymentprovider", "in": "path", "required": true, "schema": {"type": "string"}}], "x-codeSamples": [{"lang": "Node JS", "source": "const fetch = require('node-fetch');\nconst paymentProvider = 'Payment Provider';\nconst data = {'version': 1, 'method': null,'data':{'amount': 2000}, 'checksum': null};\nconst  headers = { 'Content-Type': 'application/json', 'Authorization': 'Bearer ' + TOKEN, 'X-API-Key': 'API-KEY'};\nfetch(`{BaseUrl}/api/v1/OpenApi/${paymentProvider}/paybill`, {\n method: 'POST',\n headers, \n body: data, \n})\n.then(res => { /* response */})\n.catch(err => {/* error */});"}, {"lang": ".Net", "source": "public class PaymentData { \n public int Amount { get; set; } \n public string? BankCode { get; set; } \n public string? CustomerNumber { get; set; } \n public string? CustomerReferenceNumber { get; set; } \n public string? RequestId { get; set; } \n public string? ServiceType { get; set; } \n public string? TxnReferenceNumber { get; set; }\n}\npublic class Model {\n public int Version { get; set; }\n public string? Method { get; set; }\n public PaymentData Data { get; set; }\n public string? Checksum { get; set; }\n}\nModel data = new Model() { Version = 1, Data = new PaymentData() { Amount = 2000 } };\nHttpClient client = new HttpClient();\nclient.BaseAddress = new Uri(\"{BaseUrl}/api/v1/OpenApi/\"); \nclient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(\"application/json\"));\nhttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(\"Bearer\", TOKEN); \nclient.DefaultRequestHeaders.Add(\"x-api-key\", KEY)\nHttpResponseMessage response = client.PostAsJsonAsync(string.Format(\"{paymentProvider}/paybill\", PAYMENT-PROVIDER), data).Result; \nif (response.statusCode == 200) { // success } \nelse { // error }"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentResponse"}}}}}, "security": [{"Bearer Auth": [], "x-api-key": []}], "summary": "Pay Bill", "tags": ["Open API"]}}, "/api/v1/OpenApi/{paymentProvider}/checkuserNpaybill": {"post": {"description": "description", "parameters": [{"name": "paymentprovider", "in": "path", "required": true, "schema": {"type": "string"}}], "x-codeSamples": [{"lang": "Node JS", "source": "const fetch = require('node-fetch');\nconst paymentProvider = 'Payment Provider';\nconst data = {'version': 1, 'method': null,'data':{'amount': 2000}, 'checksum': null};\nconst  headers = { 'Content-Type': 'application/json', 'Authorization': 'Bearer ' + TOKEN, 'X-API-Key': 'API-KEY'};\nfetch(`{BaseUrl}/api/v1/OpenApi/${paymentProvider}/checkuserNpaybill`, {\n method: 'POST',\n headers, \n body: data, \n})\n.then(res => { /* response */})\n.catch(err => {/* error */});"}, {"lang": ".Net", "source": "public class PaymentData { \n public int Amount { get; set; } \n public string? BankCode { get; set; } \n public string? CustomerNumber { get; set; } \n public string? CustomerReferenceNumber { get; set; } \n public string? RequestId { get; set; } \n public string? ServiceType { get; set; } \n public string? TxnReferenceNumber { get; set; }\n}\npublic class Model {\n public int Version { get; set; }\n public string? Method { get; set; }\n public PaymentData Data { get; set; }\n public string? Checksum { get; set; }\n}\nModel data = new Model() { Version = 1, Data = new PaymentData() { Amount = 2000 } };\nHttpClient client = new HttpClient();\nclient.BaseAddress = new Uri(\"{BaseUrl}/api/v1/OpenApi/\"); \nclient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(\"application/json\"));\nhttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(\"Bearer\", TOKEN); \nclient.DefaultRequestHeaders.Add(\"x-api-key\", KEY)\nHttpResponseMessage response = client.PostAsJsonAsync(string.Format(\"{paymentProvider}/checkuserNpaybill\", PAYMENT-PROVIDER), data).Result; \nif (response.statusCode == 200) { // success } \nelse { // error }"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentResponse"}}}}}, "security": [{"Bearer Auth": [], "x-api-key": []}], "summary": "Check User N Pay Bill", "tags": ["Open API"]}}, "/api/v1/OpenApi/{paymentProvider}/sandbox/checkuser": {"post": {"description": "", "x-codeSamples": [{"lang": "Node JS", "source": "const fetch = require('node-fetch');\nconst paymentProvider = 'Payment Provider';\nconst data = {'version': 1, 'method': null,'data':{'amount': 2000}, 'checksum': null};\nconst  headers = { 'Content-Type': 'application/json'};\nfetch(`{BaseUrl}/api/v1/OpenApi/${paymentProvider}/sandbox/checkuser`, {\n method: 'POST',\n headers, \n body: data, \n})\n.then(res => { /* response */})\n.catch(err => {/* error */});"}, {"lang": ".Net", "source": "public class PaymentData { \n public int Amount { get; set; } \n public string? BankCode { get; set; } \n public string? CustomerNumber { get; set; } \n public string? CustomerReferenceNumber { get; set; } \n public string? RequestId { get; set; } \n public string? ServiceType { get; set; } \n public string? TxnReferenceNumber { get; set; }\n}\npublic class Model {\n public int Version { get; set; }\n public string? Method { get; set; }\n public PaymentData Data { get; set; }\n public string? Checksum { get; set; }\n}\nModel data = new Model() { Version = 1, Data = new PaymentData() { Amount = 2000 } };\nHttpClient client = new HttpClient();\nclient.BaseAddress = new Uri(\"{BaseUrl}/api/v1/OpenApi/\"); \nclient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(\"application/json\"));\nHttpResponseMessage response = client.PostAsJsonAsync(string.Format(\"{paymentProvider}/sandbox/checkuser\", PAYMENT-PROVIDER), data).Result; \nif (response.statusCode == 200) { // success } \nelse { // error }"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequest"}}}}, "responses": {"200": {"description": "Success"}}, "summary": "Check User (Sandbox)", "tags": ["Open API"]}}, "/api/v1/OpenApi/{paymentProvider}/sandbox/paybill": {"post": {"description": "", "x-codeSamples": [{"lang": "Node JS", "source": "const fetch = require('node-fetch');\nconst paymentProvider = 'Payment Provider';\nconst data = {'version': 1, 'method': null,'data':{'amount': 2000}, 'checksum': null};\nconst  headers = { 'Content-Type': 'application/json'};\nfetch(`{BaseUrl}/api/v1/OpenApi/${paymentProvider}/sandbox/paybill`, {\n method: 'POST',\n headers, \n body: data, \n})\n.then(res => { /* response */})\n.catch(err => {/* error */});"}, {"lang": ".Net", "source": "public class PaymentData { \n public int Amount { get; set; } \n public string? BankCode { get; set; } \n public string? CustomerNumber { get; set; } \n public string? CustomerReferenceNumber { get; set; } \n public string? RequestId { get; set; } \n public string? ServiceType { get; set; } \n public string? TxnReferenceNumber { get; set; }\n}\npublic class Model {\n public int Version { get; set; }\n public string? Method { get; set; }\n public PaymentData Data { get; set; }\n public string? Checksum { get; set; }\n}\nModel data = new Model() { Version = 1, Data = new PaymentData() { Amount = 2000 } };\nHttpClient client = new HttpClient();\nclient.BaseAddress = new Uri(\"{BaseUrl}/api/v1/OpenApi/\"); \nclient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(\"application/json\"));\nHttpResponseMessage response = client.PostAsJsonAsync(string.Format(\"{paymentProvider}/sandbox/paybill\", PAYMENT-PROVIDER), data).Result; \nif (response.statusCode == 200) { // success } \nelse { // error }"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequest"}}}}, "responses": {"200": {"description": "Success"}}, "summary": "Pay Bill (Sandbox)", "tags": ["Open API"]}}, "/api/v1/OpenApi/{paymentProvider}/sandbox/paybillAsync": {"post": {"description": "", "x-codeSamples": [{"lang": "Node JS", "source": "const fetch = require('node-fetch');\nconst paymentProvider = 'Payment Provider';\nconst data = {'version': 1, 'method': null,'data':{'amount': 2000}, 'checksum': null};\nconst  headers = { 'Content-Type': 'application/json'};\nfetch(`{BaseUrl}/api/v1/OpenApi/${paymentProvider}/sandbox/paybillAsync`, {\n method: 'POST',\n headers, \n body: data, \n})\n.then(res => { /* response */})\n.catch(err => {/* error */});"}, {"lang": ".Net", "source": "public class PaymentData { \n public int Amount { get; set; } \n public string? BankCode { get; set; } \n public string? CustomerNumber { get; set; } \n public string? CustomerReferenceNumber { get; set; } \n public string? RequestId { get; set; } \n public string? ServiceType { get; set; } \n public string? TxnReferenceNumber { get; set; }\n}\npublic class Model {\n public int Version { get; set; }\n public string? Method { get; set; }\n public PaymentData Data { get; set; }\n public string? Checksum { get; set; }\n}\nModel data = new Model() { Version = 1, Data = new PaymentData() { Amount = 2000 } };\nHttpClient client = new HttpClient();\nclient.BaseAddress = new Uri(\"{BaseUrl}/api/v1/OpenApi/\"); \nclient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(\"application/json\"));\nHttpResponseMessage response = client.PostAsJsonAsync(string.Format(\"{paymentProvider}/sandbox/paybillAsync\", PAYMENT-PROVIDER), data).Result; \nif (response.statusCode == 200) { // success } \nelse { // error }"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequest"}}}}, "responses": {"200": {"description": "Success"}}, "summary": "Pay <PERSON> (Sandbox)", "tags": ["Open API"]}}, "/api/v1/OpenApi/{paymentProvider}/MerchantPaymentAsync": {"post": {"description": "", "x-codeSamples": [{"lang": "Node JS", "source": "const fetch = require('node-fetch');\nconst paymentProvider = 'Payment Provider';\nconst data = {'version': 1, 'method': null,'data': {'amount': 2000}, 'checksum': null};\nconst  headers = { 'Content-Type': 'application/json', 'Authorization': 'Bearer ' + TOKEN, 'X-API-Key': 'API-KEY'};\nfetch(`{BaseUrl}/api/v1/OpenApi/${paymentProvider}/MerchantPaymentAsync`, {\n method: 'POST',\n headers, \n body: data, \n})\n.then(res => { /* response */})\n.catch(err => {/* error */});"}, {"lang": ".Net", "source": "public class PaymentData { \n public int Amount { get; set; } \n public string? BankCode { get; set; } \n public string? CustomerNumber { get; set; } \n public string? CustomerReferenceNumber { get; set; } \n public string? RequestId { get; set; } \n public string? ServiceType { get; set; } \n public string? TxnReferenceNumber { get; set; }\n}\npublic class Model {\n public int Version { get; set; }\n public string? Method { get; set; }\n public PaymentData Data { get; set; }\n public string? Checksum { get; set; }\n}\nModel data = new Model() { Version = 1, Data = new PaymentData() { Amount = 2000 } };\nHttpClient client = new HttpClient();\nclient.BaseAddress = new Uri(\"{BaseUrl}/api/v1/OpenApi/\"); \nclient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(\"application/json\"));\nhttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(\"Bearer\", TOKEN); \nclient.DefaultRequestHeaders.Add(\"x-api-key\", KEY)\nHttpResponseMessage response = client.PostAsJsonAsync(string.Format(\"{paymentProvider}/checkuser\", PAYMENT-PROVIDER), data).Result; \nif (response.statusCode == 200) { // success } \nelse { // error }"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequest"}}}}, "responses": {"200": {"description": "Success"}}, "security": [{"Bearer Auth": [], "x-api-key": []}], "summary": "Merchant Payment Async", "tags": ["Open API"]}}, "/azampay/mno/checkout": {"post": {"operationId": "Mno Checkout", "description": "Checkout and make payment to requested provider.", "servers": [{"url": "https://sandbox.azampay.co.tz", "description": "Sandbox azampay server"}], "x-codeSamples": [{"lang": "Node JS", "source": "const fetch = require('node-fetch');\nconst data = {'accountNumber': '1292-123', 'amount':'2000', 'currency':'TZS','externalId':'123','provider':'Tigo','additionalProperties':{ 'property1': null, 'property2': null   }};\nconst  headers = { 'Content-Type': 'application/json', 'Authorization': 'Bearer ' + TOKEN, 'X-API-Key': 'API-KEY'};\nfetch(`{BaseUrl}/azampay/mno/checkout`, {\n method: 'POST',\n headers, \n body: data, \n})\n.then(res => { /* response */})\n.catch(err => {/* error */});"}, {"lang": ".Net", "source": "public class Data {  \n public string AccountNumber { get; set; } \n public Dictionary<string,string> AdditionalProperties { get; set; } \n public string Amount { get; set; } \n public string Currency { get; set; } \n public string ExternalId { get; set; } \n public string Provider { get; set; } \n}\nData data = new Data() { AccountNumber= \"1292-123\", Amount:\"2000\", Currency:\"TZS\",ExternalId:\"123\",Provider:\"Airtel\" };\nHttpClient client = new HttpClient();\nclient.BaseAddress = new Uri(\"{BaseUrl}/azampay/mno/checkout/\"); \nclient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(\"application/json\"));\nhttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(\"Bearer\", TOKEN); \nclient.DefaultRequestHeaders.Add(\"x-api-key\", KEY)\nHttpResponseMessage response = client.PostAsJsonAsync(\"Checkout\", data).Result; \nif (response.statusCode == 200) { // success } \nelse { // error }"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckoutRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckoutResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Checkout400Response"}}}}, "500": {"description": "Internal Server Error", "content": {}}}, "security": [{"Bearer Auth": []}], "summary": "Mno Checkout", "tags": ["Checkout API"]}}, "/azampay/bank/checkout": {"post": {"operationId": "Bank Checkout", "description": "Checkout and make payment to requested provider.", "servers": [{"url": "https://sandbox.azampay.co.tz", "description": "Sandbox azampay server"}], "x-codeSamples": [{"lang": "Node JS", "source": "const fetch = require('node-fetch');\nconst data = { 'amount':'2000', 'currencyCode':'TZS', 'merchantAccountNumber': '1292-123', 'merchantMobileNumber':'************', 'merchantName':null, otp:'1234', 'provider':'NMB','referenceId':'123321'};\nconst  headers = { 'Content-Type': 'application/json', 'Authorization': 'Bearer ' + TOKEN};\nfetch(`{BaseUrl}/azampay/bank/checkout`, {\n method: 'POST',\n headers, \n body: data, \n})\n.then(res => { /* response */})\n.catch(err => {/* error */});"}, {"lang": ".Net", "source": "public class Data { \n public string Amount { get; set; } \n public string CurrencyCode { get; set; } \n public string MerchantAccountNumber { get; set; } \n public string MerchantMobileNumber { get; set; }\n public string MerchantName { get; set; } \n public string Provider { get; set; } \n public string ReferenceId { get; set; } \n}\nData data = new Data() { Amount:\"2000\", CurrencyCode:\"TZS\", MerchantAccountNumber= \"1292-123\", MerchantMobileNumber:\"************\", MerchantName:null, Otp:\"123\", Provider:\"Airtel\", ReferenceId:\"123\" };\nHttpClient client = new HttpClient();\nclient.BaseAddress = new Uri(\"{BaseUrl}/azampay/bank/checkout\"); \nclient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(\"application/json\"));\nhttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(\"Bearer\", TOKEN); \nHttpResponseMessage response = client.PostAsJsonAsync(\"Checkout\", data).Result; \nif (response.statusCode == 200) { // success } \nelse { // error }"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BankCheckoutRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckoutResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Checkout400Response"}}}}, "500": {"description": "Internal Server Error", "content": {}}}, "security": [{"Bearer Auth": []}], "summary": "Bank Checkout", "tags": ["Checkout API"]}}, "/azampay/bank/otp": {"post": {"operationId": "Generate CRDB OTP", "description": "<p><strong>How to get CRDB OTP to activate your bank account</strong></p><p>Dial *150*03# and Enter your SIM Banking PIN</p><p>Press 7 other services</p><p>Press 5 for azampay the select any of the below</p><p>Link Azampay Account > to generate OTP</p><p>Unlink Azampay Account > unlink linked account</p><p>Disconnect > disable linking</p>", "summary": "Generate CRDB OTP", "tags": ["Checkout API"]}}, "/azampay/bank/otp1": {"post": {"operationId": "Generate NMB OTP", "description": "<p><strong>How to get NMB OTP to activate your bank account</strong></p><p>Dial *150*66#</p><p>Press 8 More</p><p>Press 5 Register Sarafu</p><p>Press 1 Select Account No.</p>", "summary": "Generate NMB OTP", "tags": ["Checkout API"]}}, "/api/v1/Checkout/Callback": {"post": {"operationId": "Callback", "description": "<p style='text-align: justify'>This endpoint must be available in the your application all the time. This application will  send transaction completion status to merchant application upon confirmation by user. \n\n For Sandbox environment, the URL for this callback can be provided upon registering the app \n\n<image src='../../img/app.png' alt='Register App' style='width:100% !important; height:auto; box-sizing: content-box;'/>\n\n For Production, after approval of submitted KYC \n\n<image src='../../img/kyc.png' alt='Register App' style='width:100% !important; height:auto; box-sizing: content-box;'/> You will be asked to provide the production URL for the callback by the Payment Gateway Customer Care team to integrate. \n\nCallback endpoint must follow below provided schema</p>", "servers": [{"url": "https://sandbox.azampay.co.tz", "description": "Sandbox azampay server"}], "x-codeSamples": [{"lang": "Node JS", "source": "const fetch = require('node-fetch');\nconst data = {'msisdn':'0178823', 'amount':'2000', message':'any message', 'utilityref': '1292-123', 'operator':'Tigo', 'reference':'123-123', 'transactionstatus':'success', 'submerchantAcc':'01723113'};\nconst  headers = { 'Content-Type': 'application/json'};\nfetch(`{BaseUrl}/api/v1/Checkout/Callback`, {\n method: 'POST',\n headers, \n body: data, \n})\n.then(res => { /* response */})\n.catch(err => {/* error */});"}, {"lang": ".Net", "source": "public class Data { \n public string msisdn { get; set; } \n public string message { get; set; } \n public string utilityref { get; set; } \n public string reference { get; set; } \n public String transactionstatus { get; set; } \n public string submerchantAcc { get; set; } \n public object AdditionalProperties { get; set; } \n}\nData data = new Data() { msisdn= \"1292-123\", amount:\"2000\", message:\"Some Description\", utilityref:\"123\", operator:\"Airtel\", reference:\"1\", transactionstatus:\"Active\", submerchantAcc:\"123876\" };\nHttpClient client = new HttpClient();\nclient.BaseAddress = new Uri(\"{BaseUrl}/api/v1/Checkout/Callback\"); \nclient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(\"application/json\"));\nHttpResponseMessage response = client.PostAsJsonAsync(\"Callback\", data).Result; \nif (response.statusCode == 200) { // success } \nelse { // error }"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CallbackRequest"}}}}, "responses": {"description": "description", "200": {"description": "Success"}, "500": {"description": "Internal Server Error", "content": {}}}, "summary": "Callback", "tags": ["Checkout API"]}}, "/api/v1/Partner/GetPaymentPartners": {"get": {"operationId": "GetPaymentPartners", "description": "<p style='text-align: justify'>This endpoint will return the registered partners of the provided merchant \n\n Partner List endpoint must follow below provided schema</p>", "servers": [{"url": "https://sandbox.azampay.co.tz", "description": "Sandbox azampay server"}], "x-codeSamples": [{"lang": "Node JS", "source": "const fetch = require('node-fetch');\nconst data = {'currency':'TZS', 'id':'497f6eca-6276-4993-bfeb-53cbbbba6f08', logoUrl':`https://source.unsplash.com/random/200x200?sig=1`, 'partnerName': 'Azampesa', 'paymentPartnerId':'12031', 'paymentVendorId':'123', 'provider':'4', 'status':'active', 'vendorName': 'AzamPesa'};\nconst  headers = { 'Content-Type': 'application/json'};\nfetch(`{BaseUrl}/api/v1/Partner/GetPaymentPartners`, {\n method: 'GET',\n headers, \n body: data, \n})\n.then(res => { /* response */})\n.catch(err => {/* error */});"}, {"lang": ".Net", "source": "public class Data { \n public string msisdn { get; set; } \n public string message { get; set; } \n public string externalreference { get; set; } \n public string reference { get; set; } \n public String transactionstatus { get; set; } \n public string submerchantAcc { get; set; } \n public object AdditionalProperties { get; set; } \n}\nData data = new Data() { currency= \"TZS\", id:\"497f6eca-6276-4993-bfeb-53cbbbba6f08\", logoUrl:\"https://source.unsplash.com/random/200x200?sig=1\", partnerName:\"Azampesa\", paymentPartnerId:\"12031\", paymentVendorId:\"123\", provider:\"4\", status:\"active\", vendorName:\"AzamPesa\" };\nHttpClient client = new HttpClient();\nclient.BaseAddress = new Uri(\"{BaseUrl}/api/v1/Partner/GetPaymentPartners\"); \nclient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(\"application/json\"));\nHttpResponseMessage response = client.PostAsJsonAsync(\"Callback\", data).Result; \nif (response.statusCode == 200) { // success } \nelse { // error }"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PGvNext.Checkout.Contracts.Response.Partner.GetPaymentPartnersResponse"}}}, "text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PGvNext.Checkout.Contracts.Response.Partner.GetPaymentPartnersResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PGvNext.Checkout.Contracts.Response.Partner.GetPaymentPartnersResponse"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "string"}}, "text/plain": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}, "summary": "Payment Partners", "tags": ["Checkout API"]}}, "/api/v1/Partner/PostCheckout": {"post": {"operationId": "PostCheckout", "description": "<p style='text-align: justify'>For this post request, send all params that are mentioned below to this end point. \n\n This end point will respond back with the URL of your payments. Merchant Application can open this url in a new window to continue with the checkout process of the transaction</p>", "servers": [{"url": "https://sandbox.azampay.co.tz", "description": "Sandbox azampay server"}], "x-codeSamples": [{"lang": "Node JS", "source": "const fetch = require('node-fetch');\nconst data = {'amount':'100', 'appName':'AzampayApp', cart':`{items: [{ name: <PERSON>}]}`, 'clientId': '1292123', 'currency':'TZS', 'externalId':'EXT89772223', 'language':'en', 'redirectFailURL':'https://failure', 'redirectSuccessURL': 'https://success', 'requestOrigin': 'https://requestorigin.org', vendorId: '5', 'vendorName': 'Vendor 1'};\nconst  headers = { 'Content-Type': 'application/json'};\nfetch(`{BaseUrl}/api/v1/Partner/PostCheckout`, {\n method: 'POST',\n headers, \n body: data, \n})\n.then(res => { /* response */})\n.catch(err => {/* error */});"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PGvNext.Checkout.Contracts.Request.Partner.PostCheckoutRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PGvNext.Checkout.Contracts.Request.Partner.PostCheckoutRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PGvNext.Checkout.Contracts.Request.Partner.PostCheckoutRequest"}}}}, "responses": {"description": "description", "200": {"description": "Success", "content": {"application/json": {"schema": {"type": "string"}}, "text/plain": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}, "summary": "Post Checkout", "tags": ["Checkout Pages"]}}, "/api/v1/azampay/disburse": {"post": {"description": "This API allows for the transfer of money from other countries to Tanzania. It requires the authorization token generated above, passed as a header in the request. The request should also contain details of the source, destination, and transfer details. Additionally, the request can include an external reference ID and remarks.<br/><br/><b>NOTE</b><p>Please contact us for the fields that will be used to calculate <a href=\"redoc#tag/Disbursement/operation/Checksum%20Calculation\">checksum.</a></p>", "security": [{"Bearer Auth": []}], "servers": [{"url": "https://api-disbursement-sandbox.azampay.co.tz", "description": "Sandbox azampay server"}], "tags": ["Disbursement"], "operationId": "Disburse", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PGvNext.Checkout.Contracts.Request.Disbursement.MnoDisbursementRequest"}}}}, "x-codeSamples": [{"lang": "cURL", "source": "curl -X POST \\n${BaseUrl}/azampay/createtransfer \\n-H 'Authorization: Bearer token' \n-H 'Content-Type: application/json' \n-d '{\n  \"Source\": {\n      \"CountryCode\": \"US\",\n      \"FullName\": \"John Doe\",\n      \"BankName\": \"Bank of America\",\n      \"AccountNumber\": \"*********\",\n      \"Currency\": \"TZS\"\n  },\n  \"Destination\": {\n      \"CountryCode\": \"TZ\",\n      \"FullName\": \"Jane Doe\",\n      \"BankName\": \"Azania Bank\",\n      \"AccountNumber\": \"*********\",\n      \"Currency\": \"TZS\"\n  },\n  \"TransferDetails\": {\n      \"Type\": \"SWIFT\",\n      \"Amount\": 5000,\n      \"dateInEpoch\": 0\n  },\n \"AdditionalProperties\": { \n      \"AdditionalProp1\": \"string\",\n      \"AdditionalProp2\": \"string\",\n      \"AdditionalProp3\": \"string\"\n },\n  \"Checksum\": \"string\",\n  \"ExternalReferenceID\": \"123\",\n  \"Remarks\": \"Payment for goods\"\n}' "}, {"lang": "PHP", "source": "<?php\n$url = '${BaseUrl}/azampay/createtransfer';\n$data = array(\n    \"Source\" => array(\n        \"CountryCode\" => \"US\",\n        \"FullName\" => \"John Doe\",\n        \"BankName\" => \"Bank of America\",\n        \"AccountNumber\" => \"*********\",\n        \"Currency\" => \"TZS\"\n    ),\n    \"Destination\" => array(\n        \"CountryCode\" => \"TZ\",\n        \"FullName\" => \"Jane Doe\",\n        \"BankName\" => \"Azania Bank\",\n        \"AccountNumber\" => \"*********\",\n        \"Currency\" => \"TZS\"\n    ),\n    \"TransferDetails\" => array(\n        \"Type\" => \"SWIFT\",\n        \"Amount\" => 5000,\n        \"DateInEpoch\": 0\n    ),\n    \"AdditionalProperties\": => array( \n      \"AdditionalProp1\" => \"string\",\n      \"AdditionalProp2\" => \"string\",\n    \"AdditionalProp3\" => \"string\"\n   },\n   \"Checksum\": \"string\",\n    \"ExternalReferenceID\" => \"123\",\n    \"Remarks\" => \"Payment for goods\"\n);\n\n$payload = json_encode($data);\n$headers = array(\n    'Content-Type: application/json',\n    'Authorization: Bearer token'\n);\n\n$ch = curl_init($url);\ncurl_setopt($ch, CURLOPT_CUSTOMREQUEST, \"POST\");\ncurl_setopt($ch, CURLOPT_POSTFIELDS, $payload);\ncurl_setopt($ch, CURLOPT_RETURNTRANSFER, true);\ncurl_setopt($ch, CURLOPT_HTTPHEADER, $headers);\n\n$result = curl_exec($ch);\ncurl_close($ch);\necho $result;\n?>"}, {"lang": "Python", "source": "import requests\nimport json\n\nurl = \"${BaseUrl}/azampay/createtransfer\"\n\npayload = {\n  \"Source\": {\n    \"CountryCode\": \"US\",\n    \"FullName\": \"John Doe\",\n    \"BankName\": \"Bank of America\",\n    \"AccountNumber\": \"*********\",\n    \"Currency\": \"TZS\"\n  },\n  \"Destination\": {\n    \"CountryCode\": \"TZ\",\n    \"FullName\": \"Jane Doe\",\n    \"BankName\": \"Azania Bank\",\n    \"AccountNumber\": \"*********\",\n    \"Currency\": \"TZS\"\n  },\n  \"TransferDetails\": {\n    \"Type\": \"SWIFT\",\n    \"Amount\": 5000,\n    \"DateInEpoch\": 0\n  },\n \"AdditionalProperties\": { \n      \"AdditionalProp1\": \"string\",\n      \"AdditionalProp2\": \"string\",\n      \"AdditionalProp3\": \"string\"\n },\n  \"Checksum\": \"string\",\n  \"ExternalReferenceID\": \"123\",\n  \"Remarks\": \"Payment for goods\"\n}\n\nheaders = {\n    \"Authorization\": \"Bearer token\",\n    \"Content-Type\": \"application/json\"\n}\n\nresponse = requests.post(url, data=json.dumps(payload), headers=headers)\n\nprint(response.json())\n"}, {"lang": ".Net", "source": "using System;            \nusing System.Net.Http;            \nusing System.Threading.Tasks;            \n            \nclass Program           \n{                \nstatic async Task Main()                \n{                    \nusing (var client = new HttpClient())                    \n{                        \nclient.BaseAddress = new Uri(\"${BaseUrl}/api/v1/\");                        \nclient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue(\"Bearer\", \"token\");                        \nvar requestContent = new StringContent(@\"{                          \n\t \"Source\": {                            \n\t \"CountryCode\": \"US\",                            \n\t \"FullName\": \"John Doe\",                            \n\t \"BankName\": \"Bank of America\",                            \n\t \"AccountNumber\": \"*********\",                            \n\t \"Currency\": \"TZS\"                          \n\t},                          \n\t \"Destination\": {                            \n\t \"CountryCode\": \"TZ\",                            \n\t \"FullName\": \"Jane Doe\",                            \n\t \"BankName\": \"Azania Bank\",                            \n\t \"AccountNumber\": \"*********\",                            \n\t \"Currency\": \"TZS\"                          \n\t},                          \n\t \"TransferDetails\": {                            \n\t \"Type\": \"SWIFT\",                            \n\t \"Amount\": 5000,                            \n\t \"DateInEpoch\": \"0\"                          \n\t},                            \n\t \"AdditionalProperties\": { \n\t\"AdditionalProp1\": \"string\",\n\t\"AdditionalProp2\": \"string\",\n\t\"AdditionalProp3\": \"string\"                          \n\t },                            \n\t \"Checksum\": \"string\",\n\t\"ExternalReferenceID\": \"123\",                          \n\t \"Remarks\": \"Payment for goods\"                        \n\t});                        \n\trequestContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(\"application/json\");                        \n\tvar response = await client.PostAsync(\"Disbursement/Disburse\", requestContent);                        \n\tvar responseContent = await response.Content.ReadAsStringAsync();                        \n\tConsole.WriteLine(responseContent);                    \n\t}                \n\t}            \n}"}, {"lang": "Node JS", "source": "const request = require('request');\n\n  const options = {\n    url: '${BaseUrl}/azampay/createtransfer',\n    method: 'POST',\n    headers: {\n      'Authorization': 'Bearer token',\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({\n      \"Source\": {\n        \"CountryCode\": \"US\",\n        \"FullName\": \"John Doe\",\n        \"BankName\": \"Bank of America\",\n        \"AccountNumber\": \"*********\",\n        \"Currency\": \"TZS\"\n      },\n      \"Destination\": {\n        \"CountryCode\": \"TZ\",\n        \"FullName\": \"Jane Doe\",\n        \"BankName\": \"Azania Bank\",\n        \"AccountNumber\": \"*********\",\n        \"Currency\": \"TZS\"\n      },\n      \"TransferDetails\": {\n        \"Type\": \"SWIFT\",\n        \"Amount\": 5000,\n        \"DateInEpoch\": \"0\"\n      },\n      \"AdditionalProperties\": { \n        \"AdditionalProp1\": \"string\",\n        \"AdditionalProp2\": \"string\",\n        \"AdditionalProp3\": \"string\"\n\t},\n      \"Checksum\": \"string\",\n      \"ExternalReferenceID\": \"123\",\n      \"Remarks\": \"Payment for goods\"\n    \t })\n  };\n  \n\t  request(options, (error, response, body) => {\n    if (error) {\n      console.error(error);\n    } else {\n      console.log(body);\n    }\n  });"}, {"lang": "<PERSON>", "source": "require 'net/http'\nrequire 'uri'\nrequire 'json'\n\nurl = URI(\"${BaseUrl}/azampay/createtransfer\")\n\npayload = {\n  \"Source\": {\n    \"CountryCode\": \"US\",\n    \"FullName\": \"<PERSON>\",\n    \"BankName\": \"Bank of America\",\n    \"AccountNumber\": \"*********\",\n    \"Currency\": \"TZS\"\n  },\n  \"Destination\": {\n    \"CountryCode\": \"TZ\",\n    \"FullName\": \"Jane Doe\",\n    \"BankName\": \"Azania Bank\",\n    \"AccountNumber\": \"*********\",\n    \"Currency\": \"TZS\"\n  },\n  \"TransferDetails\": {\n    \"Type\": \"SWIFT\",\n    \"Amount\": 5000,\n    \"DateInEpoch\": \"0\"\n  },\n  \"AdditionalProperties\": { \n    \"AdditionalProp1\": \"string\",\n    \"AdditionalProp2\": \"string\",\n    \"AdditionalProp3\": \"string\"\n  },\n  \"Checksum\": \"string\",\n  \"ExternalReferenceID\": \"123\",\n  \"Remarks\": \"Payment for goods\"\n}\n\nheaders = {\n  'Content-Type' => 'application/json',\n  'Authorization' => 'Bearer token'\n}\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = false # if the API is accessed through HTTPS, set this to true\nrequest = Net::HTTP::Post.new(url, headers)\nrequest.body = payload.to_json\n\nresponse = http.request(request)\nputs response.read_body"}, {"lang": "Java", "source": "import java.net.HttpURLConnection;\nimport java.net.URL;\nimport java.io.BufferedReader;\nimport java.io.InputStreamReader;\nimport java.io.OutputStream;\nimport java.util.HashMap;\nimport java.util.Map;\n\npublic class Main {\n    public static void main(String[] args) throws Exception {\n        String url = \"${BaseUrl}/azampay/createtransfer\";\n        URL obj = new URL(url);\n        HttpURLConnection con = (HttpURLConnection) obj.openConnection();\n        con.setRequestMethod(\"POST\");\n        con.setRequestProperty(\"Content-Type\", \"application/json\");\n        con.setRequestProperty(\"Authorization\", \"Bearer token\");\n\n        String postJsonData = {\n        \"Source\": {\n           \"CountryCode\": \"US\",\n           \"FullName\": \"John Doe\",\n           \"BankName\": \"Bank of America\",\n           \"AccountNumber\": \"*********\",\n           \"Currency\": \"TZS\"\n           },\n        \"Destination\": {\n           \"CountryCode\": \"TZ\",\n           \"FullName\": \"Jane Doe\",\n           \"BankName\": \"Azania Bank\",\n           \"AccountNumber\": \"*********\",\n           \"Currency\": \"TZS\"\n           },\n        \"TransferDetails\": {\n           \"Type\": \"SWIFT\",\n           \"Amount\": 5000,\n           \"DateInEpoch\": \"0\"\n           },\n        \"AdditionalProperties\": { \n           \"AdditionalProp1\": \"string\",\n           \"AdditionalProp2\": \"string\",\n           \"AdditionalProp3\": \"string\"\n           },\n        \"Checksum\": \"string\",\n        \"ExternalReferenceID\": \"123\",\n        \"Remarks\": \"Payment for goods\"\n           };\n\n      // Send post request\n        con.setDoOutput(true);\n        OutputStream os = con.getOutputStream();\n        os.write(postJsonData.getBytes(\"UTF-8\"));\n        os.flush();\n        os.close();\n\n        int responseCode = con.getResponseCode();\n        BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));\n        String inputLine;\n        StringBuffer response = new StringBuffer();\n\n        while ((inputLine = in.readLine()) != null) {\\nresponse.append(inputLine);\n        }\n        in.close();\n\n        //print result\n        System.out.println(response.toString());\n    }\n}"}, {"lang": "GO", "source": "package main\nimport (\n  \"fmt\"\n  \"strings\"\n  \"net/http\"\n  \"io/ioutil\"\n)\n\nfunc main() {\n\n  url := \"${BaseUrl}/azampay/createtransfer\"\n  method := \"POST\"\n\n  payload := strings.NewReader(`{\n    \"Source\": {\n        \"CountryCode\": \"US\",\n        \"FullName\": \"John Doe\",\n        \"BankName\": \"Bank of America\",\n        \"AccountNumber\": \"*********\",\n        \"Currency\": \"TZS\"\n    },\n    \"Destination\": {\n        \"CountryCode\": \"TZ\",\n        \"FullName\": \"Jane Doe\",\n        \"BankName\": \"Azania Bank\",\n        \"AccountNumber\": \"*********\",\n        \"Currency\": \"TZS\"\n    },\n    \"TransferDetails\": {\n        \"Type\": \"SWIFT\",\n        \"Amount\": 5000,\n        \"DateInEpoch\": \"0\"\n    },\n    \"AdditionalProperties\": { \n           \"AdditionalProp1\": \"string\",\n           \"AdditionalProp2\": \"string\",\n           \"AdditionalProp3\": \"string\"\n    },\n    \"Checksum\": \"string\",\n    \"ExternalReferenceID\": \"123\",\n    \"Remarks\": \"Payment for goods\"\n}`)\n\n  client := &http.Client {\n  }\n  req, err := http.NewRequest(method, url, payload)\n\n  if err != nil {\n    fmt.Println(err)\n    return\n  }\n  req.Header.Add(\"Authorization\", \"Bearer token\")\n  req.Header.Add(\"Content-Type\", \"application/json\")\n\n  res, err := client.Do(req)\n  if err != nil {\n    fmt.Println(err)\n    return\n  }\n  defer res.Body.Close()\n\n  body, err := ioutil.ReadAll(res.Body)\n  if err != nil {\n    fmt.Println(err)\n    return\n  }\n  fmt.Println(string(body))\n}"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Disbursement200Response"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Disbursement401Response"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Disbursement400Response"}}}}, "402": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Disbursement402Response"}}}}, "403": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Disbursement403Response"}}}}}}}, "/api/v1/azampay/namelookup": {"post": {"description": "This API is used to lookup the name associated with a bank account or Mobile Money account.<br/><br/><b>NOTE</b><p>Please contact us for the fields that will be used to calculate <a href=\"redoc#tag/Disbursement/operation/Checksum%20Calculation\">checksum.</a>", "tags": ["Disbursement"], "security": [{"Bearer Auth": []}], "servers": [{"url": "https://api-disbursement-sandbox.azampay.co.tz", "description": "Sandbox azampay server"}], "operationId": "Name Lookup", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PGvNext.Checkout.Contracts.Request.Disbursement.NameLookupRequest"}}}}, "x-codeSamples": [{"lang": "cURL", "source": "curl -X 'POST' \n'${BaseUrl}/azampay/namelookup ' \n-H 'Authorization: Provide here token' \n-H 'Content-Type: application/json' \n-d '{\n\"bankName\": \"string\",\n\"accountNumber\": \"string\",\n\"checksum\": \"string\"\n}'"}, {"lang": "Python", "source": "import requests\nurl = \"${BaseUrl}/azampay/namelookup \" \npayload = {\n    \"bankName\": \"Azania Bank\",\n    \"accountNumber\": \"*********\",\n    \"checksum\": \"string\"\n}\nheaders = {\n    \"Authorization\": \"Bearer your_token_here\",\n    \"Content-Type\": \"application/json\"\n}\nresponse = requests.post(url, json=payload, headers=headers)\nif response.status_code == 200:\n    data = response.json()\n    print(data[\"name\"])\n    print(data[\"success\"])\n    print(data[\"accountNumber\"])\n    print(data[\"bankName\"])\nelse:\n    print(\"Error occurred with status code:\", response.status_code)"}, {"lang": "PHP", "source": "<?php\n$curl = curl_init();\ncurl_setopt_array($curl, array(\n  CURLOPT_URL => '${BaseUrl}/azampay/namelookup ',\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => '',\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 0,\n  CURLOPT_FOLLOWLOCATION => true,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => 'POST',\n  CURLOPT_POSTFIELDS => '{\n  \"bankName\": \"string\",\n  \"accountNumber\": \"string\",\n  \"checksum\": \"string\"\n}',\n  CURLOPT_HTTPHEADER => array(\n    'Authorization: Provide here token',\n    'Content-Type: application/json'\n  ),\n));\n\n$response = curl_exec($curl);\n\ncurl_close($curl);\necho $response;"}, {"lang": "Node JS", "source": "const https = require('https');\nconst options = {\n  hostname: 'BaseUrl',\n  path: '/azampay/namelookup ',\n  method: 'POST',\n  headers: {\n    'Authorization': 'Provide here token',\n    'Content-Type': 'application/json'\n  }\n};\nconst req = https.request(options, (res) => {\n  console.log(`statusCode: ${res.statusCode}`);\n\n  res.on('data', (d) => {\n    process.stdout.write(d);\n  });\n});\nreq.on('error', (error) => {\n  console.error(error);\n});\nconst data = JSON.stringify({\n  bankName: 'string',\n  accountNumber: 'string',\n  checksum: 'string'\n});\nreq.write(data);\nreq.end();"}, {"lang": ".Net", "source": "var client = new RestClient(\"${BaseUrl}/azampay/namelookup \");\nclient.Timeout = -1;\nvar request = new RestRequest(Method.POST);\nrequest.AddHeader(\"Authorization\", \"Provide here token\");\nrequest.AddHeader(\"Content-Type\", \"application/json\");\nrequest.AddParameter(\"application/json\", \"{\n  \"bankName\": \"string\",\n  \"accountNumber\": \"string\",\n  \"checksum\": \"string\"\n}\",  ParameterType.RequestBody);\nIRestResponse response = client.Execute(request);\nConsole.WriteLine(response.Content);"}, {"lang": "Java", "source": "import java.io.*;\nimport java.net.*;\nimport java.util.*;\n\npublic class Main {\n  public static void main(String[] args) {\n    try {\n      URL url = new URL(\"${BaseUrl}/azampay/namelookup \");\n      HttpURLConnection conn = (HttpURLConnection) url.openConnection();\n      conn.setRequestMethod(\"POST\");\n      conn.setRequestProperty(\"Authorization\", \"Provide here token\");\n      conn.setRequestProperty(\"Content-Type\", \"application/json\");\n      conn.setDoOutput(true);\n\n      String jsonInputString = \"{\"bankName\": \"string\", \"accountNumber\": \"string\", \"checksum\": \"string\"}\";\n\n      try(OutputStream os = conn.getOutputStream()) {\n          byte[] input = jsonInputString.getBytes(\"utf-8\");\n          os.write(input, 0, input.length);           \n      }\n\n      try(BufferedReader br = new BufferedReader(\n              new InputStreamReader(conn.getInputStream(), \"utf-8\"))) {\n          StringBuilder response = new StringBuilder();\n          String responseLine = null;\n          while ((responseLine = br.readLine()) != null) {\n              response.append(responseLine.trim());\n          }\n          System.out.println(response.toString());\n      }\n    } catch (Exception e) {\n      System.out.println(e);\n    }\n  }\n}"}, {"lang": "<PERSON>", "source": "require 'uri'\nrequire 'net/http'\nrequire 'json'\n\nurl = URI(\"${BaseUrl}/azampay/namelookup \")\n\npayload = {\n    \"bankName\": \"Azania Bank\",\n    \"accountNumber\": \"*********\",\n    \"checksum\": \"string\"\n}\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\n\nrequest = Net::HTTP::Post.new(url)\nrequest[\"Authorization\"] = \"Bearer your_token_here\"\nrequest[\"Content-Type\"] = \"application/json\"\nrequest.body = payload.to_json\n\nresponse = http.request(request)\n\nif response.code == \"200\"\n    data = JSON.parse(response.read_body)\n    puts data[\"name\"]\n    puts data[\"success\"]\n    puts data[\"accountNumber\"]\n    puts data[\"bankName\"]\nelse\n    puts \"Error occurred with status code: #{response.code}\"\nend"}, {"lang": "GO", "source": "package main\nimport (\n    \"fmt\"\n    \"net/http\"\n    \"bytes\"\n    \"encoding/json\"\n)\nfunc main() {\n    url := \"${BaseUrl}/azampay/namelookup \"\n\n    payload := map[string]string{\n        \"bankName\": \"Azania Bank\",\n        \"accountNumber\": \"*********\",\n        \"checksum\": \"string\"\n    }\n\n    jsonPayload, _ := json.Marshal(payload)\n\n    req, _ := http.NewRequest(\"POST\", url, bytes.NewBuffer(jsonPayload))\n\n    req.Header.Set(\"Authorization\", \"Bearer your_token_here\")\n    req.Header.Set(\"Content-Type\", \"application/json\")\n\n    client := &http.Client{}\n    resp, _ := client.Do(req)\n\n    defer resp.Body.Close()\n\n    if resp.StatusCode == 200 {\n        var data map[string]interface{}\n        json.NewDecoder(resp.Body).Decode(&data)\n        fmt.Println(data[\"name\"])\n        fmt.Println(data[\"success\"])\n        fmt.Println(data[\"accountNumber\"])\n        fmt.Println(data[\"bankName\"])\n    } else {\n        fmt.Println(\"Error occurred with status code:\", resp.StatusCode)\n    }\n}"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PGvNext.Checkout.Contracts.Response.Disbursement.NameLookupResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Disbursement401Response"}}}}}}}, "/api/v1/azampay/transactionstatus": {"get": {"description": "This API allows you to retrieve the status of a disbursement transaction made through AzamPay.", "tags": ["Disbursement"], "operationId": "Get Transaction Status", "security": [{"Bearer Auth": []}], "servers": [{"url": "https://api-disbursement-sandbox.azampay.co.tz", "description": "Sandbox azampay server"}], "parameters": [{"name": "pgReferenceId", "in": "query", "schema": {"type": "string", "description": "To be generated by Azampay and will be used to track the status of the transaction, same will be returned in the callback."}}, {"name": "bankName", "in": "query", "schema": {"type": "string", "description": "The name of the mobile network operator (MNO) you used to make the disbursement request."}}], "x-codeSamples": [{"lang": "cURL", "source": "curl -X 'GET' \n'${BaseUrl}/azampay/gettransactionstatus?pgReferenceId=TransactionId&bankName=MNOName' \n-H 'Authorization: Bearer YOUR_AUTHORIZATION_TOKEN_HERE'"}, {"lang": "PHP", "source": "$token = 'YOUR_AUTHORIZATION_TOKEN_HERE';\n$transactionId = 'YOUR_TRANSACTION_ID_HERE';\n $mnoName = 'YOUR_MNO_NAME_HERE';\n $url = \"${BaseUrl}/azampay/gettransactionstatus?pgReferenceId={$transactionId}&bankName={$mnoName}\";\n \n $options = array(\n   'http' => array(\n     'header' => \"Authorization: Bearer {$token}\r\n\",\n     'method' => 'GET',\n   )\n );\n \n $context = stream_context_create($options);\n $response = file_get_contents($url, false, $context);\n if ($response === false) {\n   echo 'Request failed.';\n } else {\n   echo $response;\n }"}, {"lang": "Python", "source": "import requests\n   token = 'YOUR_AUTHORIZATION_TOKEN_HERE'\n   transaction_id = 'YOUR_TRANSACTION_ID_HERE'\n   mno_name = 'YOUR_MNO_NAME_HERE'\n   url = f'${BaseUrl}/azampay/gettransactionstatus?pgReferenceId={transaction_id}&bankName={mno_name}'\n   \n   headers = {\n       'Authorization': f'Bearer {token}'\n   }\n   \n   response = requests.get(url, headers=headers)\n   \n   print(response.json())"}, {"lang": ".Net", "source": "using System;\n  using System.Net.Http;\n  using System.Threading.Tasks;\n  \n  class Program\n  {\n      static HttpClient client = new HttpClient();\n      static async Task Main(string[] args)\n      {\nstring token = \"YOUR_AUTHORIZATION_TOKEN_HERE\";\nstring transactionId = \"YOUR_TRANSACTION_ID_HERE\";\nstring mnoName = \"YOUR_MNO_NAME_HERE\";\nstring url = $\"${BaseUrl}/azampay/gettransactionstatus?pgReferenceId={transactionId}&bankName={mnoName}\";\n  \nclient.DefaultRequestHeaders.Add(\"Authorization\", $\"Bearer {token}\");\n  \nHttpResponseMessage response = await client.GetAsync(url);\nif (response.IsSuccessStatusCode)\n{\n    string responseBody = await response.Content.ReadAsStringAsync();\n    Console.WriteLine(responseBody);\n}\nelse\n{\n    Console.WriteLine(\"Request failed.\");\n    Console.WriteLine($\"Status code: {response.StatusCode}\");\n}\n      }\n  }"}, {"lang": "Node JS", "source": "const axios = require('axios');\nconst token = 'YOUR_AUTHORIZATION_TOKEN_HERE';\nconst transactionId = 'YOUR_TRANSACTION_ID_HERE';\nconst mnoName = 'YOUR_MNO_NAME_HERE';\n\naxios.get(`${BaseUrl}/azampay/gettransactionstatus?pgReferenceId=${transactionId}&bankName=${mnoName}`, {\n  headers: {\n    'Authorization': `Bearer ${token}`\n  }\n})\n.then(function (response) {\n  console.log(response.data);\n})\n.catch(function (error) {\n  console.log(error);\n});"}, {"lang": "Java", "source": "import java.io.BufferedReader;\nimport java.io.IOException;\n  import java.io.InputStreamReader;\n  import java.net.HttpURLConnection;\n  import java.net.URL;\n  \n  public class Main {\n      public static void main(String[] args) throws IOException {\nString token = \"YOUR_AUTHORIZATION_TOKEN_HERE\";\nString transactionId = \"YOUR_TRANSACTION_ID_HERE\";\nString mnoName = \"YOUR_MNO_NAME_HERE\";\nString url = String.format(\"${BaseUrl}/azampay/gettransactionstatus?pgReferenceId=%s&bankName=%s\", transactionId, mnoName);\n  \nURL obj = new URL(url);\nHttpURLConnection con = (HttpURLConnection) obj.openConnection();\ncon.setRequestMethod(\"GET\");\ncon.setRequestProperty(\"Authorization\", \"Bearer \" + token);\n  \nBufferedReader in = new BufferedReader(\n    new InputStreamReader(con.getInputStream())\n);\n  \nString inputLine;\nStringBuffer response = new StringBuffer();\n  \nwhile ((inputLine = in.readLine()) != null) {\n    response.append(inputLine);\n}\nin.close();\n  \nSystem.out.println(response.toString());\n      }\n  }"}, {"lang": "<PERSON>", "source": "require 'net/http'\nrequire 'uri'\nrequire 'json'\n\ntoken = 'YOUR_AUTHORIZATION_TOKEN_HERE'\ntransaction_id = 'YOUR_TRANSACTION_ID_HERE'\nmno_name = 'YOUR_MNO_NAME_HERE'\nurl = URI(\"${BaseUrl}/azampay/gettransactionstatus?pgReferenceId=#{transaction_id}&bankName=#{mno_name}\")\n\nhttp = Net::HTTP.new(url.host, url.port)\nhttp.use_ssl = true\n\nrequest = Net::HTTP::Get.new(url)\nrequest[\"Authorization\"] = \"Bearer #{token}\"\n\nresponse = http.request(request)\nputs JSON.parse(response.read_body)\n"}, {"lang": "GO", "source": "import (\n    \"fmt\"\n    \"net/http\"\n    \"io/ioutil\"\n)\n\nfunc main() {\n    token := \"YOUR_AUTHORIZATION_TOKEN_HERE\"\n    transactionID := \"YOUR_TRANSACTION_ID_HERE\"\n    mnoName := \"YOUR_MNO_NAME_HERE\"\n    url := fmt.Sprintf(\"${BaseUrl}/azampay/gettransactionstatus?pgReferenceId=%s&bankName=%s\", transactionID, mnoName)\n\n    req, err := http.NewRequest(\"GET\", url, nil)\n    req.Header.Set(\"Authorization\", \"Bearer \" + token)\n    if err != nil {\\npanic(err)\n    }\n\n    client := &http.Client{}\n    resp, err := client.Do(req)\n    if err != nil {\\npanic(err)\n    }\n    defer resp.Body.Close()\n\n    body, err := ioutil.ReadAll(resp.Body)\n    if err != nil {\\npanic(err)\n    }\n\n    fmt.Println(string(body))\n}"}], "responses": {"402": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Disbursement201Response"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Disbursement200Response"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Disbursement401Response"}}}}}}}, "": {"get": {"description": "<ul><li>Calculate checksum using following method: Base64(RSA(SHA512(string))).</li><li>For RSA Encryption, use PKCS1 Padding.</li> <li>Contact our team to obtain the necessary public key for the encryption process.</li></ul>", "tags": ["Disbursement"], "operationId": "Checksum Calculation", "x-codeSamples": [{"lang": "PHP", "source": "\n<?php\n\n// Load the public key\n$publicKey = openssl_pkey_get_public(file_get_contents('public_key.pem')); // Replace with your actual public key file\n\n// Input string\n$inputString = 'YourInputStringHere';\n\n// Step 1: Calculate SHA-512 hash\n$sha512Hash = hash('sha512', $inputString, true);\n\n// Step 2: Encrypt with RSA using PKCS1 padding\nopenssl_public_encrypt($sha512Hash, $encryptedData, $publicKey, OPENSSL_PKCS1_PADDING);\n\n// Step 3: Encode the encrypted result in Base64\n$base64Checksum = base64_encode($encryptedData);\n\necho 'Encrypted Checksum: ' . $base64Checksum;\n\n?>"}, {"lang": "Python", "source": "\nimport hashlib\nimport base64\nfrom Crypto.Cipher import <PERSON><PERSON>CS1_OAEP\nfrom Crypto.PublicKey import RSA\n\n# Load the public key\nwith open('public_key.pem', 'rb') as f:\n    public_key = RSA.import_key(f.read())\n\n# Input string\ninput_string = 'YourInputStringHere'\n\n# Step 1: Calculate SHA-512 hash\nsha512_hash = hashlib.sha512(input_string.encode()).digest()\n\n# Step 2: Encrypt with RSA using PKCS1 padding\ncipher = PKCS1_OAEP.new(public_key)\nencrypted_data = cipher.encrypt(sha512_hash)\n\n# Step 3: Encode the encrypted result in Base64\nbase64_checksum = base64.b64encode(encrypted_data).decode()\n\nprint('Encrypted Checksum:', base64_checksum)"}, {"lang": ".Net", "source": "\nusing System;\nusing System.IO;\nusing System.Security.Cryptography;\nusing System.Text;\n\nclass Program\n{\n    static void Main()\n    {\n        // Load the public key\n        string publicKeyPath = \"public_key.pem\"; // Replace with your actual public key file\n        string publicKeyText = File.ReadAllText(publicKeyPath);\n        using var rsa = new RSACryptoServiceProvider();\n        rsa.ImportFromPem(publicKeyText);\n\n        // Input string\n        string inputString = \"YourInputStringHere\";\n\n        // Step 1: Calculate SHA-512 hash\n        using var sha512 = SHA512.Create();\n        byte[] sha512Hash = sha512.ComputeHash(Encoding.UTF8.GetBytes(inputString));\n\n        // Step 2: Encrypt with RSA using PKCS1 padding\n        byte[] encryptedData = rsa.Encrypt(sha512Hash, RSAEncryptionPadding.Pkcs1);\n\n        // Step 3: Encode the encrypted result in Base64\n        string base64Checksum = Convert.ToBase64String(encryptedData);\n\n        Console.WriteLine(\"Encrypted Checksum: \" + base64Checksum);\n    }\n}"}, {"lang": "Node JS", "source": "\nconst crypto = require('crypto');\nconst fs = require('fs');\n\n// Load the RSA public key in PEM format\nconst publicKey = fs.readFileSync('public_key.pem', 'utf8'); // Replace with your actual public key file\n\n// Input string\nconst inputString = 'YourInputStringHere';\n\n// Step 1: Calculate SHA-512 hash\nconst sha512Hash = crypto.createHash('sha512').update(inputString).digest();\n\n// Step 2: Encrypt with RSA using PKCS1 padding\nconst encryptedData = crypto.publicEncrypt({\nkey: publicKey,\npadding: crypto.constants.RSA_PKCS1_PADDING,\n}, sha512Hash);\n\n// Step 3: Encode the encrypted result in Base64\nconst base64Checksum = encryptedData.toString('base64');\n\nconsole.log('Encrypted Checksum:', base64Checksum);"}, {"lang": "Java", "source": "\nimport java.nio.charset.StandardCharsets;\nimport java.nio.file.Files;\nimport java.nio.file.Path;\nimport java.nio.file.Paths;\nimport java.security.KeyFactory;\nimport java.security.PublicKey;\nimport java.security.spec.X509EncodedKeySpec;\nimport java.security.MessageDigest;\nimport java.util.Base64;\n\nimport javax.crypto.Cipher;\n\npublic class Main {\n    public static void main(String[] args) throws Exception {\n        // Load the public key\n        String publicKeyPath = \"public_key.pem\"; // Replace with your actual public key file path\n        byte[] publicKeyBytes = Files.readAllBytes(Paths.get(publicKeyPath));\n        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);\n        KeyFactory keyFactory = KeyFactory.getInstance(\"RSA\");\n        PublicKey publicKey = keyFactory.generatePublic(keySpec);\n\n        // Input string\n        String inputString = \"YourInputStringHere\";\n\n        // Step 1: Calculate SHA-512 hash\n        MessageDigest sha512 = MessageDigest.getInstance(\"SHA-512\");\n        byte[] sha512Hash = sha512.digest(inputString.getBytes(StandardCharsets.UTF_8));\n\n        // Step 2: Encrypt with RSA using PKCS1 padding\n        Cipher rsaCipher = Cipher.getInstance(\"RSA/ECB/PKCS1Padding\");\n        rsaCipher.init(Cipher.ENCRYPT_MODE, publicKey);\n        byte[] encryptedData = rsaCipher.doFinal(sha512Hash);\n\n        // Step 3: Encode the encrypted result in Base64\n        String base64Checksum = Base64.getEncoder().encodeToString(encryptedData);\n\n        System.out.println(\"Encrypted Checksum: \" + base64Checksum);\n    }\n}"}, {"lang": "<PERSON>", "source": "\nrequire 'openssl'\nrequire 'base64'\n\n# Load the public key\npublic_key_path = 'public_key.pem' # Replace with your actual public key file path\npublic_key = OpenSSL::PKey::RSA.new(File.read(public_key_path))\n\n# Input string\ninput_string = 'YourInputStringHere'\n\n# Step 1: Calculate SHA-512 hash\nsha512 = OpenSSL::Digest::SHA512.new\nsha512_hash = sha512.digest(input_string)\n\n# Step 2: Encrypt with RSA using PKCS1 padding\nencrypted_data = public_key.public_encrypt(sha512_hash, OpenSSL::PKey::RSA::PKCS1_PADDING)\n\n# Step 3: Encode the encrypted result in Base64\nbase64_checksum = Base64.encode64(encrypted_data)\n\nputs \"Encrypted Checksum: #{base64_checksum}\""}, {"lang": "GO", "source": "\npackage main\n\nimport (\n    \"crypto\"\n    \"crypto/rand\"\n    \"crypto/rsa\"\n    \"crypto/sha512\"\n    \"encoding/base64\"\n    \"fmt\"\n    \"io/ioutil\"\n)\n\nfunc main() {\n    // Load the public key\n    publicKeyPath := \"public_key.pem\" // Replace with your actual public key file path\n    publicKeyBytes, err := ioutil.ReadFile(publicKeyPath)\n    if err != nil {\n        fmt.Println(\"Error reading public key:\", err)\n        return\n    }\n\n    publicKey, err := parseRSAPublicKey(publicKeyBytes)\n    if err != nil {\n        fmt.Println(\"Error parsing public key:\", err)\n        return\n    }\n\n    // Input string\n    inputString := \"YourInputStringHere\"\n\n    // Step 1: Calculate SHA-512 hash\n    sha512Hash := sha512.Sum512([]byte(inputString))\n\n    // Step 2: Encrypt with RSA using PKCS1 padding\n    encryptedData, err := rsa.EncryptOAEP(sha512.New(), rand.<PERSON>, publicKey, sha512Hash[:], nil)\n    if err != nil {\n        fmt.Println(\"Error encrypting data:\", err)\n        return\n    }\n\n    // Step 3: Encode the encrypted result in Base64\n    base64Checksum := base64.StdEncoding.EncodeToString(encryptedData)\n\n    fmt.Println(\"Encrypted Checksum:\", base64Checksum)\n}\n\nfunc parseRSAPublicKey(keyData []byte) (*rsa.PublicKey, error) {\n    block, _ := pem.Decode(keyData)\n    if block == nil {\n        return nil, fmt.Errorf(\"failed to parse PEM block\")\n    }\n\n    publicKey, err := x509.ParsePKIXPublicKey(block.Bytes)\n    if err != nil {\n        return nil, err\n    }\n\n    rsaPublicKey, ok := publicKey.(*rsa.PublicKey)\n    if !ok {\n        return nil, fmt.Errorf(\"not an RSA public key\")\n    }\n\n    return rsaPublicKey, nil\n}\n"}]}}, "/callback": {"post": {"operationId": "Callback", "description": "<p style='text-align: justify'>This endpoint must be available in the your application all the time. This application will send transaction completion status to merchant application upon confirmation by user.<br/><br/>Please contact us to configure the callback URL for your application.</p>", "servers": [{"url": "https://yourapp.etc", "description": ""}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DisburseCallback"}}}}, "responses": {"description": "description", "200": {"description": "Success"}, "500": {"description": "Internal Server Error", "content": {}}}, "summary": "Callback", "tags": ["Disbursement"]}}, "/api/merchant/name-lookup": {"post": {"security": [{"Bearer Auth": []}], "servers": [{"url": "https://YOUR-API-URL", "description": "Name Lookup"}], "description": "Merchants provide an endpoint for name lookup based on a BillIdentifier.<br/><h3>Hash Verification Example:</h3> <ol> <li>Convert the Data object to a minified JSON string. <pre>{\"BillIdentifier\":\"*********\",\"Currency\":\"TZS\",\"Language\":\"en\",\"Country\":\"Tanzania\",\"TimeStamp\":\"2024-06-12T10:20:30Z\",\"AdditionalProperties\":{\"key1\":\"value1\",\"key2\":\"value2\",\"property1\":\"string\",\"property2\":\"string\"},\"BillType\":\"Electricity\"}</pre> </li> <li>Compute the SHA256 hash of the JSON string.</li> <li>SHA256 hash: <pre>fe6173685ce463cc966f6fce41fd59a92f181d1d7e63b8a17a3e42e41e381cbd</pre> </li> <li>Sign the hash with the secret key using HMAC-SHA256. <p>Secret key (Used in sample): <pre>TRlWYMsr0GQU15REn6KRVn2DDv55MMW9VjLfpQn9RVg6m8QaCGNfY9IuGy6sCB1</pre> </p> <p>HMAC-SHA256 signature: <pre>711bd3c3e54488523683182aa01c8a83544e45b7ba2c45652e039bf830e5daf2</pre> </p> </li> <li>Compare the computed hash with the Hash field in the request. If they match, the request is verified.</li> </ol>", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"Data": {"type": "object", "properties": {"BillIdentifier": {"type": "string", "description": "Unique identifier for the bill", "example": "*********"}, "Currency": {"type": "string", "description": "Currency code in ISO 4217 format", "example": "TZS"}, "Language": {"type": "string", "description": "Language code in ISO 639-1 format", "example": "en"}, "Country": {"type": "string", "description": "Name of the country", "example": "Tanzania"}, "TimeStamp": {"type": "string", "format": "date-time", "description": "Timestamp of the request in ISO 8601 format", "example": "2024-06-12T10:20:30Z"}, "AdditionalProperties": {"type": "object", "description": "Additional key-value pairs for extra information", "properties": {"key1": {"type": "string", "description": "Additional key-value pair 1", "example": "value1"}, "key2": {"type": "string", "description": "Additional key-value pair 2", "example": "value2"}}, "additionalProperties": {"type": "string"}}, "BillType": {"type": "string", "description": "Type of the bill", "example": "Electricity"}}, "required": ["BillIdentifier", "<PERSON><PERSON><PERSON><PERSON>", "Language", "Country", "TimeStamp", "BillType"], "additionalProperties": false}, "Hash": {"type": "string", "description": "SHA256 hash of the request data", "example": "78b1e51f6318f57f43995b08b48c2de6f84fbe94b2c0c4b3a7e77a7430b9b104"}}, "required": ["Data", "Hash"], "additionalProperties": false}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"Name": {"type": "string", "description": "The name associated with the bill identifier", "example": "<PERSON>"}, "BillAmount": {"type": "number", "description": "The amount of the bill", "example": 150.0}, "BillIdentifier": {"type": "string", "description": "Unique identifier for the bill", "example": "*********"}, "Status": {"type": "string", "description": "The status of the operation", "example": "Success"}, "Message": {"type": "string", "description": "A message describing the result of the operation", "example": "Name found for the provided BillIdentifier."}, "StatusCode": {"type": "number", "description": "A code representing the status of the response", "example": 0}}, "required": [], "additionalProperties": false}}}}}, "x-codeSamples": [{"lang": "cURL", "source": "curl -X POST {baseUrl}/namelookup \\ \n-H \"Authorization: Bearer TOKEN\" \\ \n-H \"Content-Type: application/json\" \\ \n-d '{ \n    \"Data\": { \n        \"BillIdentifier\": \"*********\", \n        \"Currency\": \"TZS\", \n        \"Language\": \"en\", \n        \"Country\": \"Tanzania\", \n        \"TimeStamp\": \"2024-06-12T10:20:30Z\", \n        \"AdditionalProperties\": { \n            \"key1\": \"value1\", \n            \"key2\": \"value2\" \n        }, \n        \"BillType\": \"Electricity\" \n    }, \n    \"Hash\": \"78b1e51f6318f57f43995b08b48c2de6f84fbe94b2c0c4b3a7e77a7430b9b104\" \n}'"}, {"lang": ".Net", "source": "using System;\nusing System.Net.Http;\nusing System.Text;\nusing System.Threading.Tasks;\n\nclass Program\n{\n    static async Task Main()\n    {\n        var client = new HttpClient();\n        var requestUri = \"{baseUrl}/namelookup\";\n        var requestBody = new\n        {\n            Data = new\n            {\n                BillIdentifier = \"*********\",\n                Currency = \"TZS\",\n                Language = \"en\",\n                Country = \"Tanzania\",\n                TimeStamp = \"2024-06-12T10:20:30Z\",\n                AdditionalProperties = new\n                {\n                    key1 = \"value1\",\n                    key2 = \"value2\"\n                },\n                BillType = \"Electricity\"\n            },\n            Hash = \"78b1e51f6318f57f43995b08b48c2de6f84fbe94b2c0c4b3a7e77a7430b9b104\"\n        };\n\n        var content = new StringContent(\n            Newtonsoft.Json.JsonConvert.SerializeObject(requestBody),\n            Encoding.UTF8,\n            \"application/json\"\n        );\n\n        client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue(\"Bearer\", \"TOKEN\");\n\n        var response = await client.PostAsync(requestUri, content);\n        var responseString = await response.Content.ReadAsStringAsync();\n\n        Console.WriteLine(responseString);\n    }\n}"}, {"lang": "Node JS", "source": "const fetch = require('node-fetch');\n\nconst requestBody = {\n    Data: {\n        BillIdentifier: \"*********\",\n        Currency: \"TZS\",\n        Language: \"en\",\n        Country: \"Tanzania\",\n        TimeStamp: \"2024-06-12T10:20:30Z\",\n        AdditionalProperties: {\n            key1: \"value1\",\n            key2: \"value2\"\n        },\n        BillType: \"Electricity\"\n    },\n    Hash: \"78b1e51f6318f57f43995b08b48c2de6f84fbe94b2c0c4b3a7e77a7430b9b104\"\n};\n\nfetch('{baseUrl}/namelookup', {\n    method: 'POST',\n    headers: {\n        'Authorization': 'Bearer TOKEN',\n        'Content-Type': 'application/json'\n    },\n    body: JSON.stringify(requestBody)\n})\n.then(response => response.json())\n.then(data => console.log(data))\n.catch(error => console.error(error));"}, {"lang": "Java", "source": "import java.net.HttpURLConnection;\nimport java.net.URL;\nimport java.io.OutputStream;\nimport org.json.JSONObject;\n\npublic class Main {\n    public static void main(String[] args) {\n        try {\n            URL url = new URL(\"{baseUrl}/namelookup\");\n            HttpURLConnection conn = (HttpURLConnection) url.openConnection();\n            conn.setRequestMethod(\"POST\");\n            conn.setRequestProperty(\"Authorization\", \"Bearer TOKEN\");\n            conn.setRequestProperty(\"Content-Type\", \"application/json\");\n            conn.setDoOutput(true);\n\n            JSONObject jsonData = new JSONObject();\n            jsonData.put(\"BillIdentifier\", \"*********\");\n            jsonData.put(\"Currency\", \"TZS\");\n            jsonData.put(\"Language\", \"en\");\n            jsonData.put(\"Country\", \"Tanzania\");\n            jsonData.put(\"TimeStamp\", \"2024-06-12T10:20:30Z\");\n            jsonData.put(\"AdditionalProperties\", new JSONObject()\n                .put(\"key1\", \"value1\")\n                .put(\"key2\", \"value2\"));\n            jsonData.put(\"BillType\", \"Electricity\");\n\n            JSONObject requestBody = new JSONObject();\n            requestBody.put(\"Data\", jsonData);\n            requestBody.put(\"Hash\", \"78b1e51f6318f57f43995b08b48c2de6f84fbe94b2c0c4b3a7e77a7430b9b104\");\n\n            try (OutputStream os = conn.getOutputStream()) {\n                byte[] input = requestBody.toString().getBytes(\"utf-8\");\n                os.write(input, 0, input.length);\n            }\n\n            int code = conn.getResponseCode();\n            System.out.println(\"Response Code: \" + code);\n        } catch (Exception e) {\n            e.printStackTrace();\n        }\n    }\n}"}], "summary": "Name Lookup API", "tags": ["Bill Pay API"]}}, "/api/merchant/payment": {"post": {"security": [{"Bearer Auth": []}], "servers": [{"url": "https://YOUR-API-URL", "description": "Payment API"}], "description": "Merchants provide an endpoint to process payments.<br/><h3>Hash Verification Example:</h3> <ol> <li>Convert the Data object to a JSON string. <pre>{\"BillIdentifier\":\"*********\",\"Currency\":\"TZS\",\"Language\":\"en\",\"Country\":\"Tanzania\",\"TimeStamp\":\"2024-06-12T10:20:30Z\",\"AdditionalProperties\":{\"key1\":\"value1\",\"key2\":\"value2\"},\"BillType\":\"Electricity\"}</pre> </li> <li>Compute the SHA256 hash of the minified JSON string.</li> <li>SHA256 hash: <pre>9e6c0c62bdbacc4aef78b422dcd142e2f1bf82b0afbe2224be627a0761ee5825</pre> </li> <li>Sign the hash with the secret key using HMAC-SHA256. <p>Secret key (Used in sample): <pre>TRlWYMsr0GQU15REn6KRVn2DDv55MMW9VjLfpQn9RVg6m8QaCGNfY9IuGy6sCB1</pre> </p> <p>HMAC-SHA256 signature: <pre>ca3f49b2ee7740bf68062445ee7027d33baa4dbb0e456cdc7534ddd34bf19fb1</pre> </p> </li> <li>Compare the computed hash with the Hash field in the request. If they match, the request is verified.</li> </ol>", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"Data": {"type": "object", "properties": {"FspReferenceId": {"type": "string", "description": "The reference ID provided by the Financial Service Provider", "example": "fsp123456"}, "PgReferenceId": {"type": "string", "description": "The reference ID provided by the Payment Gateway", "example": "pg123456"}, "Amount": {"type": "number", "description": "The amount of the transaction", "example": 100}, "BillIdentifier": {"type": "string", "description": "The unique identifier for the bill", "example": "*********"}, "PaymentDesc": {"type": "string", "description": "Description of the payment", "example": "Utility Bill Payment"}, "FspCode": {"type": "string", "description": "The code representing the Financial Service Provider", "example": "FSP123"}, "Country": {"type": "string", "description": "The country of the transaction", "example": "Tanzania"}, "TimeStamp": {"type": "string", "format": "date-time", "description": "The timestamp of the transaction", "example": "2024-06-12T10:20:30Z"}, "BillType": {"type": "string", "description": "The type of the bill", "example": "Water"}, "AdditionalProperties": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Any additional properties relevant to the transaction", "example": {"key1": "value1", "key2": "value2"}}}, "required": ["FspReferenceId", "PgReferenceId", "Amount", "BillIdentifier", "PaymentDesc", "FspCode", "Country", "TimeStamp", "BillType", "AdditionalProperties"]}, "Hash": {"type": "string", "description": "The hash value for verifying the integrity of the request", "example": "9e4fbd74ae54f9f3fbd2e3d4c4cbe2b1b8a5675f78bbd2c13c5fba32d2f4c5b8"}}, "required": ["Data", "Hash"], "additionalProperties": false}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"MerchantReferenceId": {"type": "string", "description": "The reference ID provided by the merchant", "example": "merchant123456"}, "Status": {"type": "string", "description": "The status of the transaction", "example": "Success"}, "StatusCode": {"type": "number", "description": "The status code indicating the result of the operation", "example": 0}, "Message": {"type": "string", "description": "A message describing the result of the operation", "example": "Payment successful."}}, "required": [], "additionalProperties": false}}}}}, "x-codeSamples": [{"lang": "cURL", "source": "curl -X POST {baseUrl}/payment \\ \n-H \"Authorization: Bearer TOKEN\" \\ \n-H \"Content-Type: application/json\" \\ \n-d '{ \n  \"Data\": { \n    \"FspReferenceId\": \"fsp123456\", \n    \"PgReferenceId\": \"pg123456\", \n    \"Amount\": 100, \n    \"BillIdentifier\": \"*********\", \n    \"PaymentDesc\": \"Utility Bill Payment\", \n    \"FspCode\": \"FSP123\", \n    \"Country\": \"Tanzania\", \n    \"TimeStamp\": \"2024-06-12T10:20:30Z\", \n    \"BillType\": \"Water\", \n    \"AdditionalProperties\": { \n      \"key1\": \"value1\", \n      \"key2\": \"value2\" \n    } \n  }, \n  \"Hash\": \"9e4fbd74ae54f9f3fbd2e3d4c4cbe2b1b8a5675f78bbd2c13c5fba32d2f4c5b8\" \n}'"}, {"lang": ".Net", "source": "using System;\nusing System.Net.Http;\nusing System.Text;\nusing System.Threading.Tasks;\n\nclass Program\n{\n    static async Task Main()\n    {\n        var client = new HttpClient();\n        var requestUri = \"{baseUrl}/payment\";\n        var requestBody = new\n        {\n            Data = new\n            {\n                FspReferenceId = \"fsp123456\",\n                PgReferenceId = \"pg123456\",\n                Amount = 100,\n                BillIdentifier = \"*********\",\n                PaymentDesc = \"Utility Bill Payment\",\n                FspCode = \"FSP123\",\n                Country = \"Tanzania\",\n                TimeStamp = \"2024-06-12T10:20:30Z\",\n                BillType = \"Water\",\n                AdditionalProperties = new\n                {\n                    key1 = \"value1\",\n                    key2 = \"value2\"\n                }\n            },\n            Hash = \"9e4fbd74ae54f9f3fbd2e3d4c4cbe2b1b8a5675f78bbd2c13c5fba32d2f4c5b8\"\n        };\n\n        var content = new StringContent(\n            Newtonsoft.Json.JsonConvert.SerializeObject(requestBody),\n            Encoding.UTF8,\n            \"application/json\"\n        );\n\n        client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue(\"Bearer\", \"TOKEN\");\n\n        var response = await client.PostAsync(requestUri, content);\n        var responseString = await response.Content.ReadAsStringAsync();\n\n        Console.WriteLine(responseString);\n    }\n}"}, {"lang": "Node JS", "source": "const fetch = require('node-fetch');\n\nconst requestBody = {\n    Data: {\n        FspReferenceId: \"fsp123456\",\n        PgReferenceId: \"pg123456\",\n        Amount: 100,\n        BillIdentifier: \"*********\",\n        PaymentDesc: \"Utility Bill Payment\",\n        FspCode: \"FSP123\",\n        Country: \"Tanzania\",\n        TimeStamp: \"2024-06-12T10:20:30Z\",\n        BillType: \"Water\",\n        AdditionalProperties: {\n            key1: \"value1\",\n            key2: \"value2\"\n        }\n    },\n    Hash: \"9e4fbd74ae54f9f3fbd2e3d4c4cbe2b1b8a5675f78bbd2c13c5fba32d2f4c5b8\"\n};\n\nfetch('{baseUrl}/payment', {\n    method: 'POST',\n    headers: {\n        'Authorization': 'Bearer TOKEN',\n        'Content-Type': 'application/json'\n    },\n    body: JSON.stringify(requestBody)\n})\n.then(response => response.json())\n.then(data => console.log(data))\n.catch(error => console.error(error));"}, {"lang": "Java", "source": "import java.net.HttpURLConnection;\nimport java.net.URL;\nimport java.io.OutputStream;\nimport org.json.JSONObject;\n\npublic class Main {\n    public static void main(String[] args) {\n        try {\n            URL url = new URL(\"{baseUrl}/payment\");\n            HttpURLConnection conn = (HttpURLConnection) url.openConnection();\n            conn.setRequestMethod(\"POST\");\n            conn.setRequestProperty(\"Authorization\", \"Bearer TOKEN\");\n            conn.setRequestProperty(\"Content-Type\", \"application/json\");\n            conn.setDoOutput(true);\n\n            JSONObject jsonData = new JSONObject();\n            jsonData.put(\"FspReferenceId\", \"fsp123456\");\n            jsonData.put(\"PgReferenceId\", \"pg123456\");\n            jsonData.put(\"Amount\", 100);\n            jsonData.put(\"BillIdentifier\", \"*********\");\n            jsonData.put(\"PaymentDesc\", \"Utility Bill Payment\");\n            jsonData.put(\"FspCode\", \"FSP123\");\n            jsonData.put(\"Country\", \"Tanzania\");\n            jsonData.put(\"TimeStamp\", \"2024-06-12T10:20:30Z\");\n            jsonData.put(\"BillType\", \"Water\");\n            jsonData.put(\"AdditionalProperties\", new JSONObject()\n                .put(\"key1\", \"value1\")\n                .put(\"key2\", \"value2\"));\n\n            JSONObject requestBody = new JSONObject();\n            requestBody.put(\"Data\", jsonData);\n            requestBody.put(\"Hash\", \"9e4fbd74ae54f9f3fbd2e3d4c4cbe2b1b8a5675f78bbd2c13c5fba32d2f4c5b8\");\n\n            try (OutputStream os = conn.getOutputStream()) {\n                byte[] input = requestBody.toString().getBytes(\"utf-8\");\n                os.write(input, 0, input.length);\n            }\n\n            int code = conn.getResponseCode();\n            System.out.println(\"Response Code: \" + code);\n        } catch (Exception e) {\n            e.printStackTrace();\n        }\n    }\n}"}], "summary": "Payment API", "tags": ["Bill Pay API"]}}, "/api/merchant/status-check": {"post": {"security": [{"Bearer Auth": []}], "servers": [{"url": "https://YOUR-API-URL", "description": "Status Check API"}], "description": "Merchants provide an API to check the status of a payment using the MerchantReferenceId.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"MerchantReferenceId": {"type": "string", "description": "The reference ID provided by the merchant", "example": "merchant123456"}}, "required": ["MerchantReferenceId"], "additionalProperties": false}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"MerchantReferenceId": {"type": "string", "description": "The reference ID provided by the merchant", "example": "merchant123456"}, "Status": {"type": "string", "description": "The status of the transaction", "example": "Success"}, "StatusCode": {"type": "number", "description": "The status code indicating the result of the operation", "example": 0}, "Message": {"type": "string", "description": "A message describing the result of the operation", "example": "Payment successful."}}, "required": [], "additionalProperties": false}}}}}, "x-codeSamples": [{"lang": "cURL", "source": "curl -X POST {baseUrl}/statuscheck \\ \n-H \"Authorization: Bearer TOKEN\" \\ \n-H \"Content-Type: application/json\" \\ \n-d '{ \n  \"MerchantReferenceId\": \"merchant123456\" \n}'"}, {"lang": ".Net", "source": "using System;\nusing System.Net.Http;\nusing System.Text;\nusing System.Threading.Tasks;\n\nclass Program\n{\n    static async Task Main()\n    {\n        var client = new HttpClient();\n        var requestUri = \"{baseUrl}/statuscheck\";\n        var requestBody = new\n        {\n            MerchantReferenceId = \"merchant123456\"\n        };\n\n        var content = new StringContent(\n            Newtonsoft.Json.JsonConvert.SerializeObject(requestBody),\n            Encoding.UTF8,\n            \"application/json\"\n        );\n\n        client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue(\"Bearer\", \"TOKEN\");\n\n        var response = await client.PostAsync(requestUri, content);\n        var responseString = await response.Content.ReadAsStringAsync();\n\n        Console.WriteLine(responseString);\n    }\n}"}, {"lang": "Node JS", "source": "const fetch = require('node-fetch');\n\nconst requestBody = {\n    MerchantReferenceId: \"merchant123456\"\n};\n\nfetch('{baseUrl}/statuscheck', {\n    method: 'POST',\n    headers: {\n        'Authorization': 'Bearer TOKEN',\n        'Content-Type': 'application/json'\n    },\n    body: JSON.stringify(requestBody)\n})\n.then(response => response.json())\n.then(data => console.log(data))\n.catch(error => console.error(error));"}, {"lang": "Java", "source": "import java.net.HttpURLConnection;\nimport java.net.URL;\nimport java.io.OutputStream;\nimport org.json.JSONObject;\n\npublic class Main {\n    public static void main(String[] args) {\n        try {\n            URL url = new URL(\"{baseUrl}/statuscheck\");\n            HttpURLConnection conn = (HttpURLConnection) url.openConnection();\n            conn.setRequestMethod(\"POST\");\n            conn.setRequestProperty(\"Authorization\", \"Bearer TOKEN\");\n            conn.setRequestProperty(\"Content-Type\", \"application/json\");\n            conn.setDoOutput(true);\n\n            JSONObject requestBody = new JSONObject();\n            requestBody.put(\"MerchantReferenceId\", \"merchant123456\");\n\n            try (OutputStream os = conn.getOutputStream()) {\n                byte[] input = requestBody.toString().getBytes(\"utf-8\");\n                os.write(input, 0, input.length);\n            }\n\n            int code = conn.getResponseCode();\n            System.out.println(\"Response Code: \" + code);\n        } catch (Exception e) {\n            e.printStackTrace();\n        }\n    }\n}"}], "summary": "Status Check API", "tags": ["Bill Pay API"]}}}, "components": {"securitySchemes": {"Bearer Auth": {"description": "Get access to data while protecting your account credentials.\nJWT is also a safer and more secure way to give you access.\n", "type": "http", "scheme": "bearer", "bearerFormat": "JWT", "in": "header"}, "x-api-key": {"description": "For this sample, you can use the value of api key <strong>'9b70596d-cfa9-4e3f-bf3b-fce79a112ab6'</strong>.\n", "type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "nxame": "X-API-Key"}}, "schemas": {"TokenGeneration200Response": {"type": "object", "properties": {"data": {"type": "object", "example": {"accessToken": {"type": "object"}, "expire": {"type": "object"}}}, "message": {"type": "string", "example": "Token generated successfully"}, "success": {"type": "boolean", "example": true}, "statusCode": {"type": "integer", "format": "int32", "example": 200}}, "additionalProperties": false}, "TokenGeneration423Response": {"type": "object", "properties": {"data": {"type": "string", "example": null}, "message": {"type": "string", "example": "Provided detail is not valid for this app or secret key has been expired"}, "success": {"type": "boolean", "example": false}, "statusCode": {"type": "integer", "format": "int32", "example": 423}}, "additionalProperties": false}, "TokenGeneration500Response": {"type": "object", "properties": {"data": {"type": "string"}, "message": {"type": "string", "example": "Internal Server Error"}, "success": {"type": "boolean", "example": false}, "statusCode": {"type": "integer", "format": "int32", "example": 500}}, "additionalProperties": false}, "Response": {"type": "object", "properties": {"data": {"nullable": true}, "message": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "statusCode": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TokenGeneration": {"required": ["appName", "clientId", "clientSecret"], "type": "object", "properties": {"appName": {"type": "string", "description": "It will be the name of application."}, "clientId": {"type": "string", "description": "It will be the client id which generated during application registration."}, "clientSecret": {"type": "string", "description": "It will be the secret key which generated during application registration."}}, "additionalProperties": false}, "BankCheckoutRequest": {"required": ["currencyCode", "amount", "merchantAccountNumber", "merchantMobileNumber", "otp", "provider"], "type": "object", "properties": {"additionalProperties": {"type": "object", "additionalProperties": {}, "nullable": true, "description": "Total serialized size limit: 4096 bytes (4 Kilobytes)."}, "amount": {"type": "number", "description": "Must contain numeric characters only. <br>Value range: 0 to 5,000,000."}, "currencyCode": {"type": "string", "description": "Cannot be null or empty."}, "merchantAccountNumber": {"type": "string", "description": "Maximum length: 100 characters."}, "merchantMobileNumber": {"type": "string", "description": "Maximum length: 100 characters."}, "merchantName": {"type": "string", "nullable": true, "description": "Maximum length: 100 characters."}, "otp": {"type": "string", "description": "Marked as Sensitive Data; ensure secure handling."}, "provider": {"$ref": "#/components/schemas/BankProvider"}, "referenceId": {"type": "string", "nullable": true, "description": "Maximum length: 128 characters."}}, "additionalProperties": false}, "AirtelUSSDRequest": {"type": "object", "properties": {"apIusername": {"type": "string", "nullable": true}, "apiPassword": {"type": "string", "nullable": true}, "transID": {"type": "string", "nullable": true}, "amount": {"type": "string", "nullable": true}, "paybillnumber": {"type": "string", "nullable": true}, "referenceField": {"type": "string", "nullable": true}, "msisdn": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AirtelUSSDResponse": {"type": "object", "properties": {"status": {"type": "string", "nullable": true}, "transID": {"type": "string", "nullable": true}, "amount": {"type": "string", "nullable": true}, "referenceField": {"type": "string", "nullable": true}, "msisdn": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CustomerNameLookupReponse": {"type": "object", "properties": {"statusCode": {"type": "integer", "format": "int64"}, "status": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/DataResponse"}}, "additionalProperties": false}, "DataResponse": {"type": "object", "properties": {"customerReferenceNumber": {"type": "string", "nullable": true}, "customerName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ExternalServiceDb": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "serviceName": {"type": "string", "nullable": true}, "serviceURL": {"type": "string", "nullable": true}, "httpMethodType": {"type": "string", "nullable": true}, "httpHeader": {"type": "string", "nullable": true}, "sslCertificateCheck": {"type": "boolean"}, "isRetryEnabled": {"type": "boolean"}, "requestOptions": {"type": "string", "nullable": true}, "requestType": {"type": "string", "nullable": true}, "request": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ExternalServiceDbPaginatedResults": {"type": "object", "properties": {"pagingInfo": {"$ref": "#/components/schemas/PagingInfo"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/ExternalServiceDb"}, "nullable": true}}, "additionalProperties": false}, "FilterInfo": {"type": "object", "properties": {"filterKey": {"type": "string", "nullable": true}, "filterValue": {"type": "string", "nullable": true}, "operator": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PagingInfo": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "PaymentData": {"type": "object", "properties": {"txnReferenceNumber": {"type": "string", "nullable": true}, "customerNumber": {"type": "string", "nullable": true}, "requestId": {"type": "string", "nullable": true}, "bankCode": {"type": "string", "nullable": true}, "customerReferenceNumber": {"type": "string", "nullable": true}, "amount": {"type": "integer", "format": "int64"}, "serviceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PaymentPartnerDb": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "operatorCode": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "partnerType": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "PaymentPartnerDbPaginatedResults": {"type": "object", "properties": {"pagingInfo": {"$ref": "#/components/schemas/PagingInfo"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentPartnerDb"}, "nullable": true}}, "additionalProperties": false}, "PaymentRequest": {"type": "object", "properties": {"version": {"type": "integer", "format": "int64"}, "method": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/PaymentData"}, "checksum": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PaymentResponse": {"type": "object", "properties": {"statusCode": {"type": "integer", "format": "int64"}, "status": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/PaymentResponseData"}}, "additionalProperties": false}, "PaymentResponseData": {"type": "object", "properties": {"txnReferenceNumber": {"type": "string", "nullable": true}, "requestId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PaymentVendorDb": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "vendorCode": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "serviceType": {"type": "string", "nullable": true}, "vendorProcessingRoute": {"type": "string", "nullable": true}, "vendorIntimationRoute": {"type": "string", "nullable": true}, "businessTin": {"type": "string", "nullable": true}, "taxCertificateUrl": {"type": "string", "nullable": true}, "countryCode": {"type": "string", "nullable": true}, "businessVat": {"type": "string", "nullable": true}, "additionalInfo": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "PaymentVendorDbPaginatedResults": {"type": "object", "properties": {"pagingInfo": {"$ref": "#/components/schemas/PagingInfo"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentVendorDb"}, "nullable": true}}, "additionalProperties": false}, "CheckoutRequest": {"required": ["accountNumber", "amount", "currency", "externalId", "provider"], "type": "object", "properties": {"accountNumber": {"type": "string", "description": "This is the account number/MSISDN that consumer will provide. The amount will be deducted from this account."}, "additionalProperties": {"type": "object", "description": "Total serialized size limit: 4 Kilobytes (4096 bytes).", "additionalProperties": {}, "nullable": true}, "amount": {"type": "number", "description": "Must contain numeric characters only. <br> Value range: 0 to 5,000,000."}, "currency": {"type": "string", "description": "Maximum length: 32 characters."}, "externalId": {"type": "string", "description": "Maximum length: 128 characters."}, "provider": {"$ref": "#/components/schemas/Provider"}}, "additionalProperties": false}, "CheckoutResponse": {"type": "object", "properties": {"transactionId": {"type": "string", "nullable": true, "description": "Each successful transaction will be given a valid transaction id."}, "message": {"type": "string", "nullable": true, "description": "This is the status message of checkout request."}, "success": {"type": "boolean", "description": "<strong>true</strong> represents that transaction is initiated successfully<br><strong>false</strong> represents that transaction initiation is failed, see message field for the description"}}, "additionalProperties": false}, "Checkout400Response": {"type": "object", "properties": {"errors": {"type": "object", "properties": {"amount": {"type": "array", "nullable": true, "description": "Amount Must be valid"}, "externalId": {"type": "array", "nullable": true, "description": "ExternalId cannot be empty. It should be any reference string from calling Application"}, "accountNumber": {"type": "array", "nullable": true, "description": "Account Number must be valid"}, "provider": {"type": "array", "nullable": true, "description": "Provider enum value is not valid"}}}, "type": {"type": "string", "nullable": false, "description": "Error code"}, "traceId": {"type": "string", "nullable": false, "description": "Error ID"}, "title": {"type": "string", "nullable": false, "description": "One or more validation errors occurred"}, "status": {"type": "number", "nullable": false, "description": "400"}}, "additionalProperties": false}, "CallbackRequest": {"required": ["msisdn", "amount", "message", "utilityref", "operator", "reference", "transactionstatus"], "type": "object", "properties": {"additionalProperties": {"type": "object", "additionalProperties": {}, "nullable": true, "description": "This is additional JSON data that calling application can provide. This is optional."}, "msisdn": {"type": "string", "description": "This is the account number/MSISDN that consumer will provide. The amount will be deducted from this account."}, "amount": {"type": "string", "description": "This is amount that will be charged from the given account."}, "message": {"type": "string", "description": "This is transaction description message"}, "utilityref": {"type": "string", "description": "This is the id that belongs to the calling application."}, "operator": {"$ref": "#/components/schemas/Provider"}, "reference": {"type": "string", "description": "It is transaction id"}, "transactionstatus": {"type": "string", "description": "success or failure"}, "submerchantAcc": {"type": "string", "description": "This field is reserved for future use"}, "fspReferenceId": {"type": "string", "description": "It is the reference ID from partner FSP (Financial Service Provider)"}}, "additionalProperties": false}, "DisburseCallback": {"required": [], "type": "object", "properties": {"additionalProperties": {"type": "object", "additionalProperties": {}, "nullable": true, "description": "This is additional JSON data that calling application can provide. This is optional."}, "initiatorReferenceId": {"type": "string", "description": " An external reference ID to track the transaction which the initiator party sends in the request"}, "fspReferenceId": {"type": "string", "description": " It is the reference ID from partner FSP (Financial Service Provider)"}, "pgReferenceId": {"type": "string", "description": " To be generated by Azampay and will be used to track the status of the transaction, same will be returned in the callback."}, "amount": {"type": "string", "description": "This is amount that will be charged from the given account."}, "status": {"type": "string", "description": "success or failure"}, "message": {"type": "string", "description": "This is transaction description message"}, "operator": {"$ref": "#/components/schemas/DisburseProvider"}}, "additionalProperties": false}, "CheckoutPagesRequest": {"required": ["amount", "appName", "vendorName", "cart", "clientId", "currency", "externalId", "redirectFailURL", "redirectSuccessURL", "<PERSON><PERSON><PERSON><PERSON>", "vendorId", "language"], "type": "object", "properties": {"amount": {"type": "string", "description": "This is amount that will be charged from the given account."}, "vendorName": {"type": "string", "description": "Name of whome will consume payment page."}, "appName": {"type": "string", "description": "It will be name of application."}, "cart": {"description": "This field is shopping cart", "type": "array", "items": {"type": "object"}}, "clientId": {"type": "string", "description": "This is vendor unique id"}, "language": {"type": "string", "description": "Language code for translate checkout page"}, "currency": {"type": "string", "description": "Code of currency"}, "externalId": {"type": "string", "description": "It is unique id"}, "redirectSuccessURL": {"type": "string", "description": "URL for incase for success"}, "redirectFailURL": {"type": "string", "description": "URL for incase for failure"}}, "additionalProperties": false}, "CallbackResponse": {"type": "object", "properties": {}, "additionalProperties": false}, "Provider": {"enum": ["Airtel", "Tigo", "<PERSON><PERSON><PERSON>", "Azampesa", "Mpesa"], "type": "string"}, "DisburseProvider": {"enum": ["Airtel", "Azampesa", "Tigo"], "type": "string"}, "BankProvider": {"enum": ["CRDB", "NMB"], "type": "string"}, "PGvNext.Checkout.Contracts.Response.Partner.GetPaymentPartnersResponse": {"type": "object", "properties": {"logoUrl": {"type": "string", "nullable": true, "description": "Payment Partner logo URL"}, "partnerName": {"type": "string", "nullable": true, "description": "Name of the payment partner e.g (Azampesa, Airtel, Halopesa, Tigopesa, vodacom)"}, "provider": {"type": "integer", "format": "int32", "description": "Provider enum value e.g (airtel=2, tigo=3, halopesa=4, azampesa=5, Mpesa=10)"}, "vendorName": {"type": "string", "nullable": true, "description": "Name of the vendor"}, "paymentVendorId": {"type": "string", "format": "uuid", "description": "Unique id for payment vendor"}, "paymentPartnerId": {"type": "string", "format": "uuid", "description": "Unique id for payment partner"}, "currency": {"type": "string", "nullable": true, "description": "Currency code that will convert amount into specific currency format"}}, "additionalProperties": false}, "PGvNext.Checkout.Contracts.Response.Partner.GetPostCheckout": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "logoUrl": {"type": "string", "nullable": true}, "partnerName": {"type": "string", "nullable": true}, "provider": {"type": "integer", "format": "int32"}, "vendorName": {"type": "string", "nullable": true}, "paymentVendorId": {"type": "string", "format": "uuid"}, "paymentPartnerId": {"type": "string", "format": "uuid"}, "currency": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PGvNext.Checkout.Contracts.Request.Partner.PostCheckoutRequest": {"required": ["amount", "appName", "vendorName", "cart", "clientId", "currency", "externalId", "redirectFailURL", "redirectSuccessURL", "<PERSON><PERSON><PERSON><PERSON>", "vendorId", "language"], "type": "object", "properties": {"appName": {"type": "string", "description": "This is the application name"}, "clientId": {"type": "string", "description": "Client id is unique id for identify client"}, "vendorId": {"type": "string", "format": "uuid", "description": "Unique id for validate vendor"}, "language": {"type": "string", "description": "Language code for translate the application"}, "currency": {"type": "string", "description": "Currency code that will convert amount into specific currency format"}, "externalId": {"type": "string", "description": "30 characters long unique string"}, "requestOrigin": {"type": "string", "description": "URL which the request is being originated"}, "redirectFailURL": {"type": "string", "description": "URL that you want to redirected to at transaction failure"}, "redirectSuccessURL": {"type": "string", "description": "URL that you want to redirected to at transaction success"}, "vendorName": {"type": "string", "description": "Name of vendor"}, "amount": {"type": "string", "description": "This is amount that will be charged from the given account."}, "cart": {"type": "object", "description": "Shopping cart with multiple item", "properties": {"items": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "nullable": true}}, "additionalProperties": false}}, "additionalProperties": false}, "PGvNext.Checkout.Contracts.Request.Partner.Cart": {"type": "object", "description": "Shopping cart with multiple item", "properties": {"items": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "nullable": true}}, "additionalProperties": false}, "PGvNext.Checkout.Contracts.Request.Partner.Item": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PGvNext.Checkout.Contracts.Request.BaseRequest": {"required": ["provider", "type"], "type": "object", "properties": {}, "additionalProperties": false, "discriminator": {"propertyName": "type"}}, "PGvNext.Checkout.Contracts.Request.Disbursement.MnoDisbursementRequest": {"required": ["destination", "externalReferenceId", "source", "transferDetails"], "type": "object", "properties": {"source": {"required": ["accountNumber", "bankName", "currency", "countryCode", "fullName"], "type": "object", "description": "Contains information about the source account.", "properties": {"countryCode": {"type": "string", "description": "The country code of the source account."}, "fullName": {"type": "string", "description": "The full name of the account holder."}, "bankName": {"minLength": 1, "enum": ["tigo", "airtel", "azampesa"], "type": "string", "description": "The name of the bank where the destination account is held."}, "accountNumber": {"minLength": 1, "type": "string", "description": "The account number of the source account."}, "currency": {"minLength": 1, "type": "string", "description": "The currency in which the transfer is made."}}, "additionalProperties": false}, "destination": {"required": ["accountNumber", "bankName", "currency", "countryCode", "fullName"], "type": "object", "description": "Contains information about the destination account.", "properties": {"countryCode": {"type": "string", "description": "The country code of the destination account."}, "fullName": {"type": "string", "description": "The full name of the account holder."}, "bankName": {"minLength": 1, "enum": ["tigo", "airtel", "azampesa"], "type": "string", "description": "The name of the bank where the destination account is held."}, "accountNumber": {"minLength": 1, "type": "string", "description": "The account number of the destination account."}, "currency": {"minLength": 1, "type": "string", "description": "The currency in which the transfer is made."}}, "additionalProperties": false}, "transferDetails": {"required": ["amount", "type", "dateInEpoch"], "type": "object", "description": "Contains information about the transfer.", "properties": {"type": {"minLength": 1, "type": "string", "description": "The type of transfer, e.g. SWIFT, SEPA, etc."}, "amount": {"type": "number", "format": "double", "description": "The amount to be transferred."}, "dateInEpoch": {"type": "number", "format": "double", "description": "The current time in UNIX <a href=\"https://www.epochconverter.com/\" target=\"_blank\">epoch</a> format."}}, "additionalProperties": false}, "externalReferenceId": {"maxLength": 30, "type": "string", "description": "An external reference ID to track the transaction which the initiator party sends in the request."}, "additionalProperties": {"type": "object", "description": "This is additional JSON data that calling application can provide. This is optional.", "additionalProperties": {}, "nullable": true}, "checksum": {"type": "string", "description": "A string containing the <a href=\"redoc#tag/Disbursement/operation/Checksum%20Calculation\">checksum.</a>"}, "remarks": {"type": "string", "nullable": true, "description": "Any remarks to be included with the transaction."}}, "additionalProperties": false}, "Disbursement200Response": {"type": "object", "properties": {"pgReferenceId": {"type": "string", "example": "b42aeas4hl3d4f58bhfk4007782cb452", "description": "To be generated by Azampay and will be used to track the status of the transaction, same will be returned in the callback."}, "message": {"type": "string", "example": "Your transaction is in process", "description": "A string containing a human-readable message describing the response."}, "success": {"type": "boolean", "example": true, "description": "A boolean indicating whether the request was successful or not."}, "statusCode": {"type": "integer", "example": 200, "description": "An integer indicating the status code of the response."}}, "additionalProperties": false}, "Disbursement403Response": {"type": "object", "properties": {"pgReferenceId": {"type": "string", "example": null, "description": "To be generated by Azampay and will be used to track the status of the transaction, same will be returned in the callback."}, "message": {"type": "string", "example": "Duplicate External Reference Id", "description": "A string containing a human-readable message describing the response."}, "success": {"type": "boolean", "example": false, "description": "A boolean indicating whether the request was successful or not."}, "statusCode": {"type": "integer", "example": 400, "description": "An integer indicating the status code of the response."}}, "additionalProperties": false}, "Disbursement201Response": {"type": "object", "properties": {"data": {"type": "object", "example": null, "description": "To be generated by Azampay and will be used to track the status of the transaction, same will be returned in the callback."}, "message": {"type": "string", "example": "Transaction not found", "description": "A string containing a human-readable message describing the response."}, "success": {"type": "boolean", "example": false, "description": "A boolean indicating whether the request was successful or not."}, "statusCode": {"type": "integer", "example": 404, "description": "An integer indicating the status code of the response."}}, "additionalProperties": false}, "Disbursement401Response": {"type": "object", "properties": {"message": {"type": "string", "example": "Please Provide Valid Authorization", "description": "A string containing a human-readable message describing the response."}, "status": {"type": "string", "example": "Error", "description": "A string show status"}}, "additionalProperties": false}, "Disbursement400Response": {"type": "object", "properties": {"success": {"type": "boolean", "example": true, "description": "A boolean indicating whether the request was successful or not."}, "pgReferenceId": {"type": "string", "nullable": true, "description": "To be generated by Azampay and will be used to track the status of the transaction, same will be returned in the callback."}, "message": {"type": "string", "example": "Invalid BankName.", "description": "A string containing a human-readable message describing the response."}, "status": {"type": "integer", "example": 400, "description": "A string show status"}}, "additionalProperties": false}, "Disbursement402Response": {"type": "object", "properties": {"success": {"type": "boolean", "example": true, "description": "A boolean indicating whether the request was successful or not."}, "pgReferenceId": {"type": "string", "nullable": true, "description": "To be generated by Azampay and will be used to track the status of the transaction, same will be returned in the callback."}, "message": {"type": "string", "example": "Request expired", "description": "A string containing a human-readable message describing the response."}, "status": {"type": "integer", "example": 400, "description": "A string show status"}}, "additionalProperties": false}, "TransactionStatus200Response": {"type": "object", "properties": {"data": {"type": "object"}, "message": {"type": "string", "example": "Transaction Status successfuly fetch"}, "success": {"type": "boolean", "example": true}, "statusCode": {"type": "integer", "format": "int32", "example": 200}}, "additionalProperties": false}, "TransactionStatus400Response": {"type": "object", "properties": {"message": {"type": "string", "example": "Transaction status not found"}, "success": {"type": "boolean", "example": true}, "statusCode": {"type": "integer", "format": "int32", "example": 200}}, "additionalProperties": false}, "PGvNext.Checkout.Contracts.Request.Disbursement.NameLookupRequest": {"type": "object", "properties": {"bankName": {"type": "string", "nullable": true, "description": "Bank name or Mobile Money name associated with the account."}, "accountNumber": {"type": "string", "nullable": true, "description": "Bank account number or Mobile Money number."}, "checksum": {"type": "string", "description": "A string containing the <a href=\"redoc#tag/Disbursement/operation/Checksum%20Calculation\">checksum.</a>"}}, "additionalProperties": false}, "PGvNext.Checkout.Contracts.Response.Disbursement.NameLookupResponse": {"type": "object", "properties": {"fname": {"type": "string", "nullable": true, "description": "First Name."}, "lname": {"type": "string", "nullable": true, "description": "Last Name."}, "name": {"type": "string", "nullable": true, "description": "Name associated with the account."}, "message": {"type": "string", "nullable": true, "description": "A brief description of the response status."}, "status": {"type": "boolean", "description": "A boolean value indicating if the request was successful or not."}, "statusCode": {"type": "integer", "format": "int32", "example": 200}, "accountNumber": {"type": "string", "nullable": true, "description": "Account number."}, "bankName": {"type": "string", "nullable": true, "description": "Bank name or Mobile Money name associated with the account."}}, "additionalProperties": false}, "PGvNext.Checkout.Contracts.Request.Partner.Transactor": {"type": "object", "properties": {"tpin": {"type": "string", "nullable": true}, "idValue": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PGvNext.Checkout.Contracts.Request.PaymentLink": {"type": "object", "properties": {"paymentLinkId": {"type": "string", "format": "uuid"}, "vendorId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "PGvNext.Models.Common.Transaction": {"required": ["type"], "type": "object", "properties": {"type": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "partnerReferenceId": {"type": "string", "nullable": true}, "vendorReferenceId": {"type": "string", "nullable": true}, "pgReferenceId": {"type": "string", "nullable": true}, "senderMSISDN": {"type": "string", "nullable": true}, "receiverAccountNumber": {"type": "string", "nullable": true}, "receiverFundingSource": {"type": "string", "nullable": true}, "serviceActivationCode": {"type": "string", "nullable": true}, "serviceProviderName": {"type": "string", "nullable": true}, "vendorId": {"type": "string", "format": "uuid"}, "customerName": {"type": "string", "nullable": true}, "subvendorCode": {"type": "string", "nullable": true}, "serviceName": {"type": "string", "nullable": true}, "serviceType": {"type": "integer", "format": "int32"}, "requestType": {"type": "string", "nullable": true}, "amount": {"type": "string", "nullable": true}, "language": {"type": "string", "nullable": true}, "region": {"type": "string", "nullable": true}, "currency": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "interfaceId": {"type": "string", "nullable": true}, "requestTimeStamp": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "merchantId": {"type": "string", "nullable": true}, "customerAccountNo": {"type": "string", "nullable": true}, "debitCustomerName": {"type": "string", "nullable": true}, "debitCustomerAccountNo": {"type": "string", "nullable": true}, "extraData": {"type": "string", "nullable": true}, "failedReason": {"type": "string", "nullable": true}, "logs": {"type": "array", "items": {"$ref": "#/components/schemas/PGvNext.Models.Common.TransactionLog"}, "nullable": true}}, "additionalProperties": false, "discriminator": {"propertyName": "type"}}, "PGvNext.Models.Common.TransactionLog": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "identifier": {"type": "string", "nullable": true}, "transactionId": {"type": "string", "format": "uuid"}, "eventOrder": {"type": "integer", "format": "int32"}, "eventName": {"type": "string", "nullable": true}, "request": {"type": "string", "nullable": true}, "response": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "PGvNext.Models.Common.VendorType": {"enum": ["seller", "company"], "type": "string"}, "PGvNext.Models.Common.Provider": {"enum": ["tigo", "airtel", "azampesa"], "type": "string", "description": "The name of the bank where the source account is held."}}}}