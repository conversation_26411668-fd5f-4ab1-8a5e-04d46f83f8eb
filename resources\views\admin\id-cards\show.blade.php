@extends('layouts.admin')

@section('title', 'ID Card Details')
@section('page-title', 'ID Card Details')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">ID Card Details</h2>
                <p class="text-muted">{{ $idCard->card_number }}</p>
            </div>
            <div class="d-flex gap-2">
                @if($idCard->is_active)
                <form method="POST" action="{{ route('admin.id-cards.deactivate', $idCard) }}" class="d-inline">
                    @csrf
                    @method('PATCH')
                    <button type="submit" class="btn btn-warning"
                            onclick="return confirm('Deactivate this ID card?')">
                        <i class="fas fa-ban me-2"></i>Deactivate
                    </button>
                </form>
                @else
                <form method="POST" action="{{ route('admin.id-cards.reactivate', $idCard) }}" class="d-inline">
                    @csrf
                    @method('PATCH')
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check me-2"></i>Reactivate
                    </button>
                </form>
                @endif
                <a href="{{ route('admin.id-cards.index') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to ID Cards
                </a>
            </div>
        </div>
    </div>
</div>

<!-- ID Card Information -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-id-card me-2"></i>ID Card Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Card Number:</strong></td>
                                <td><span class="badge bg-dark fs-6">{{ $idCard->card_number }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>User:</strong></td>
                                <td>
                                    <div>
                                        <div class="fw-bold">{{ $idCard->user->full_name }}</div>
                                        <small class="text-muted">{{ $idCard->user->email }}</small>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Summit:</strong></td>
                                <td>
                                    <div>
                                        <div class="fw-bold">{{ $idCard->summit->title }}</div>
                                        <small class="text-muted">{{ $idCard->summit->year }}</small>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Branch:</strong></td>
                                <td>{{ $idCard->user->branch->name ?? 'N/A' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Conference:</strong></td>
                                <td>{{ $idCard->user->branch->zone->conference->name ?? 'N/A' }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    @if($idCard->is_active && $idCard->expires_at > now())
                                        <span class="badge bg-success">Active</span>
                                    @elseif($idCard->expires_at < now())
                                        <span class="badge bg-warning">Expired</span>
                                    @else
                                        <span class="badge bg-secondary">Inactive</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Issued Date:</strong></td>
                                <td>{{ $idCard->issued_at->format('F j, Y \a\t g:i A') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Expires Date:</strong></td>
                                <td>{{ $idCard->expires_at->format('F j, Y \a\t g:i A') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Days Until Expiry:</strong></td>
                                <td>
                                    @if($idCard->expires_at > now())
                                        <span class="text-success">{{ (int) now()->diffInDays($idCard->expires_at) }} days</span>
                                    @else
                                        <span class="text-danger">Expired {{ $idCard->expires_at->diffForHumans() }}</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Created:</strong></td>
                                <td>{{ $idCard->created_at->format('F j, Y \a\t g:i A') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>User Details</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    @if($idCard->user->profile_image)
                        <img src="{{ Storage::url($idCard->user->profile_image) }}" 
                             alt="Profile" 
                             class="rounded-circle border border-3 border-primary"
                             style="width: 100px; height: 100px; object-fit: cover;">
                    @else
                        <div class="rounded-circle bg-primary d-inline-flex align-items-center justify-content-center border border-3 border-primary"
                             style="width: 100px; height: 100px;">
                            <i class="fas fa-user fa-2x text-white"></i>
                        </div>
                    @endif
                </div>
                
                <table class="table table-borderless table-sm">
                    <tr>
                        <td><strong>Student ID:</strong></td>
                        <td>{{ $idCard->user->student_id ?? 'N/A' }}</td>
                    </tr>
                    <tr>
                        <td><strong>University:</strong></td>
                        <td>{{ $idCard->user->university->name ?? 'N/A' }}</td>
                    </tr>
                    <tr>
                        <td><strong>Course:</strong></td>
                        <td>{{ $idCard->user->course ?? 'N/A' }}</td>
                    </tr>
                    <tr>
                        <td><strong>Year of Study:</strong></td>
                        <td>{{ $idCard->user->year_of_study ?? 'N/A' }}</td>
                    </tr>
                    <tr>
                        <td><strong>Phone:</strong></td>
                        <td>{{ $idCard->user->phone ?? 'N/A' }}</td>
                    </tr>
                </table>
                
                <div class="d-grid gap-2 mt-3">
                    <a href="{{ route('admin.users.show', $idCard->user) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-user me-2"></i>View User Profile
                    </a>
                </div>
            </div>
        </div>
        
        <!-- QR Code -->
        @if($idCard->qr_code_path && Storage::disk('public')->exists($idCard->qr_code_path))
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-qrcode me-2"></i>QR Code</h5>
            </div>
            <div class="card-body text-center">
                <img src="{{ Storage::url($idCard->qr_code_path) }}" 
                     alt="QR Code" 
                     class="img-fluid"
                     style="max-width: 200px;">
                <div class="mt-2">
                    <small class="text-muted">Scan to verify card authenticity</small>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

<!-- Verification Information -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Verification Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>QR Code Data</h6>
                        <div class="bg-light p-3 rounded">
                            <pre class="mb-0 small">{{ json_encode(json_decode($idCard->qr_code), JSON_PRETTY_PRINT) }}</pre>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Verification Details</h6>
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td><strong>Verification URL:</strong></td>
                                <td>
                                    <a href="{{ route('id-card.verify', $idCard->card_number) }}" target="_blank" class="text-decoration-none">
                                        {{ route('id-card.verify', $idCard->card_number) }}
                                        <i class="fas fa-external-link-alt ms-1"></i>
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Card Number:</strong></td>
                                <td><code>{{ $idCard->card_number }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>QR Code File:</strong></td>
                                <td>
                                    @if($idCard->qr_code_path && Storage::disk('public')->exists($idCard->qr_code_path))
                                        <span class="text-success">
                                            <i class="fas fa-check-circle me-1"></i>Available
                                        </span>
                                    @else
                                        <span class="text-danger">
                                            <i class="fas fa-times-circle me-1"></i>Missing
                                        </span>
                                    @endif
                                </td>
                            </tr>
                        </table>
                        
                        <div class="mt-3">
                            <a href="{{ route('id-card.verify', $idCard->card_number) }}" 
                               target="_blank" 
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-shield-alt me-2"></i>Test Verification
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
