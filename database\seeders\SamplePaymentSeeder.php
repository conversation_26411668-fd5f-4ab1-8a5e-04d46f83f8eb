<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Payment;
use App\Models\User;
use App\Models\Summit;
use App\Models\SummitRegistration;

class SamplePaymentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if we already have enough payment data
        if (Payment::where('status', 'completed')->count() >= 10) {
            $this->command->info('Sufficient payment data already exists.');
            return;
        }

        $users = User::limit(5)->get();
        $summit = Summit::first();

        if ($users->isEmpty() || !$summit) {
            $this->command->error('No users or summit found. Please run other seeders first.');
            return;
        }

        $this->command->info('Creating sample payment data for charts...');

        foreach ($users as $index => $user) {
            // Check if user already has a registration
            $registration = SummitRegistration::where('user_id', $user->id)
                ->where('summit_id', $summit->id)
                ->first();

            if (!$registration) {
                // Create a registration
                $registration = SummitRegistration::create([
                    'user_id' => $user->id,
                    'summit_id' => $summit->id,
                    'registration_number' => 'SAMPLE' . str_pad($user->id, 4, '0', STR_PAD_LEFT),
                    'registered_at' => now()->subDays(rand(1, 60)),
                    'total_amount' => $summit->registration_fee,
                    'paid_amount' => $summit->registration_fee,
                    'balance' => 0,
                    'payment_complete' => true,
                    'status' => 'confirmed'
                ]);
            }

            // Create 2-3 payments for each user across different months
            for ($i = 1; $i <= rand(2, 3); $i++) {
                $amount = rand(20000, 100000);
                $date = now()->subMonths(rand(1, 11))->addDays(rand(1, 28));

                // Check if payment reference already exists
                $reference = 'SAMPLE' . $user->id . '_' . $i . '_' . time();

                Payment::create([
                    'user_id' => $user->id,
                    'summit_registration_id' => $registration->id,
                    'payment_reference' => $reference,
                    'amount' => $amount,
                    'status' => 'completed',
                    'payment_method' => ['selcom', 'bank_transfer', 'cash', 'mobile_money'][rand(0, 3)],
                    'paid_at' => $date,
                    'created_at' => $date,
                    'updated_at' => $date,
                ]);

                $this->command->info("Created payment for {$user->full_name}: " . number_format($amount) . " TZS");
            }
        }

        $totalPayments = Payment::where('status', 'completed')->count();
        $this->command->info("Sample payment data created successfully. Total completed payments: {$totalPayments}");
    }
}
