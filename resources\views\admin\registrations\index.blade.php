@extends('layouts.admin')

@section('title', 'Registrations Management')
@section('page-title', 'Registrations Management')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">Registrations Management</h2>
                <p class="text-muted">Manage and monitor summit registrations.</p>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Search -->
<x-enhanced-search
    type="registrations"
    placeholder="Search registrations by user name, email, registration number..."
    route="{{ route('admin.registrations.index') }}"
    :filters="[
        [
            'name' => 'search',
            'label' => 'Traditional Search',
            'type' => 'text',
            'placeholder' => 'Name, email, registration number...',
            'width' => 4
        ],
        [
            'name' => 'summit_id',
            'label' => 'Summit',
            'type' => 'select',
            'placeholder' => 'All Summits',
            'width' => 3,
            'options' => $summits->pluck('title', 'id')->toArray()
        ],
        [
            'name' => 'status',
            'label' => 'Status',
            'type' => 'select',
            'placeholder' => 'All Status',
            'width' => 2,
            'options' => ['confirmed' => 'Confirmed', 'pending' => 'Pending', 'cancelled' => 'Cancelled']
        ],
        [
            'name' => 'payment_status',
            'label' => 'Payment',
            'type' => 'select',
            'placeholder' => 'All',
            'width' => 3,
            'options' => ['complete' => 'Payment Complete', 'partial' => 'Partial Payment', 'pending' => 'Payment Pending']
        ]
    ]"
/>

<!-- Traditional Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" action="{{ route('admin.registrations.index') }}">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="summit_id" class="form-label">Summit</label>
                            <select name="summit_id" id="summit_id" class="form-select">
                                <option value="">All Summits</option>
                                @foreach($summits as $summit)
                                    <option value="{{ $summit->id }}" {{ request('summit_id') == $summit->id ? 'selected' : '' }}>
                                        {{ $summit->title }} ({{ $summit->year }})
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="payment_status" class="form-label">Payment Status</label>
                            <select name="payment_status" id="payment_status" class="form-select">
                                <option value="">All Statuses</option>
                                <option value="paid" {{ request('payment_status') == 'paid' ? 'selected' : '' }}>Paid</option>
                                <option value="pending" {{ request('payment_status') == 'pending' ? 'selected' : '' }}>Pending</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="Search by name, email, or registration number..." 
                                   value="{{ request('search') }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Filter
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Registrations Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clipboard-list me-2"></i>Registrations 
                    <span class="badge bg-primary ms-2">{{ $registrations->total() }}</span>
                </h5>
            </div>
            <div class="card-body">
                @if($registrations->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Registration #</th>
                                <th>User</th>
                                <th>Summit</th>
                                <th>Status</th>
                                <th>Amount</th>
                                <th>Payment Status</th>
                                <th>Registered Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($registrations as $registration)
                            <tr class="{{ $registration->status === 'cancelled' ? 'table-secondary' : '' }}">
                                <td>
                                    <code>{{ $registration->registration_number }}</code>
                                </td>
                                <td>
                                    <div>
                                        <strong class="{{ $registration->status === 'cancelled' ? 'text-muted' : '' }}">
                                            {{ $registration->user->full_name }}
                                        </strong><br>
                                        <small class="text-muted">{{ $registration->user->email }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong class="{{ $registration->status === 'cancelled' ? 'text-muted' : '' }}">
                                            {{ $registration->summit->title }}
                                            @if($registration->status === 'cancelled')
                                                <i class="fas fa-times-circle text-danger ms-1" title="Cancelled"></i>
                                            @endif
                                        </strong><br>
                                        <small class="text-muted">{{ $registration->summit->year }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $registration->status === 'confirmed' ? 'success' : ($registration->status === 'cancelled' ? 'danger' : 'warning') }}">
                                        {{ ucfirst($registration->status) }}
                                    </span>
                                </td>
                                <td>
                                    <div>
                                        <strong class="{{ $registration->status === 'cancelled' ? 'text-muted' : '' }}">
                                            TZS {{ number_format($registration->total_amount, 0) }}
                                        </strong><br>
                                        @if($registration->status !== 'cancelled')
                                            <small class="text-muted">
                                                Paid: TZS {{ number_format($registration->paid_amount, 0) }}
                                            </small>
                                        @else
                                            <small class="text-muted">Registration cancelled</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    @if($registration->status === 'cancelled')
                                        <span class="badge bg-secondary">Cancelled</span>
                                    @else
                                        <span class="badge {{ $registration->payment_complete ? 'bg-success' : 'bg-warning' }}">
                                            {{ $registration->payment_complete ? 'Paid' : 'Pending' }}
                                        </span>
                                        @if($registration->balance > 0)
                                            <br><small class="text-danger">Balance: TZS {{ number_format($registration->balance, 2) }}</small>
                                        @endif
                                    @endif
                                </td>
                                <td>{{ $registration->registered_at->format('M j, Y') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('admin.registrations.show', $registration) }}" 
                                           class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $registrations->withQueryString()->links() }}
                </div>
                @else
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Registrations Found</h5>
                    <p class="text-muted">No registrations match your current filters.</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
