@extends(auth()->user()->hasRole('admin') ? 'layouts.admin' : 'layouts.user')

@section('title', 'Payment Reports')
@if(auth()->user()->hasRole('admin'))
@section('page-title', 'Payment Reports')
@endif

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">Payment Reports</h2>
                <p class="text-muted">Comprehensive payment analytics and transaction history.</p>
            </div>
            <a href="{{ route('admin.reports.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Reports
            </a>
        </div>
    </div>
</div>

<!-- Filter Form -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.reports.payments') }}">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select name="status" id="status" class="form-select">
                                <option value="">All Status</option>
                                <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="failed" {{ request('status') == 'failed' ? 'selected' : '' }}>Failed</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="payment_method" class="form-label">Payment Method</label>
                            <select name="payment_method" id="payment_method" class="form-select">
                                <option value="">All Methods</option>
                                @foreach($paymentMethodOptions as $method)
                                    <option value="{{ $method }}" {{ request('payment_method') == $method ? 'selected' : '' }}>
                                        {{ ucfirst($method) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="summit_id" class="form-label">Summit</label>
                            <select name="summit_id" id="summit_id" class="form-select">
                                <option value="">All Summits</option>
                                @foreach($summits as $summit)
                                    <option value="{{ $summit->id }}" {{ request('summit_id') == $summit->id ? 'selected' : '' }}>
                                        {{ $summit->title }} ({{ $summit->year }})
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">Date From</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" value="{{ request('date_from') }}">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">Date To</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" value="{{ request('date_to') }}">
                        </div>
                        <div class="col-md-9 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-2"></i>Apply Filters
                            </button>
                            <a href="{{ route('admin.reports.payments') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Clear Filters
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-2 col-md-4 mb-3">
        <div class="modern-stats-card blue h-100">
            <div class="stat-icon">
                <i class="fas fa-credit-card"></i>
            </div>
            <h3 class="stat-number">{{ number_format($paymentStats['total_payments']) }}</h3>
            <p class="stat-label">Total Payments</p>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 mb-3">
        <div class="modern-stats-card green h-100">
            <div class="stat-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h3 class="stat-number">{{ number_format($paymentStats['completed_payments']) }}</h3>
            <p class="stat-label">Completed</p>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 mb-3">
        <div class="modern-stats-card orange h-100">
            <div class="stat-icon">
                <i class="fas fa-clock"></i>
            </div>
            <h3 class="stat-number">{{ number_format($paymentStats['pending_payments']) }}</h3>
            <p class="stat-label">Pending</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="modern-stats-card purple h-100">
            <div class="stat-icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <h3 class="stat-number">TSh {{ number_format($paymentStats['total_amount']) }}</h3>
            <p class="stat-label">Total Revenue</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="modern-stats-card red h-100">
            <div class="stat-icon">
                <i class="fas fa-hourglass-half"></i>
            </div>
            <h3 class="stat-number">TSh {{ number_format($paymentStats['pending_amount']) }}</h3>
            <p class="stat-label">Pending Amount</p>
        </div>
    </div>
</div>

<!-- Payment Methods Breakdown -->
@if($paymentMethods->count() > 0)
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Payment Methods Breakdown</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($paymentMethods as $method)
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <h4 class="text-primary">{{ number_format($method->count) }}</h4>
                            <p class="text-muted mb-1">{{ ucfirst($method->payment_method) }}</p>
                            <small class="text-success">TSh {{ number_format($method->total) }}</small>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Payments Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>Payment Details</h5>
            </div>
            <div class="card-body">
                @if($payments->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Reference</th>
                                <th>User</th>
                                <th>Summit</th>
                                <th>Amount</th>
                                <th>Method</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($payments as $payment)
                            <tr>
                                <td>
                                    <div>
                                        <div class="fw-bold">#{{ $payment->payment_reference }}</div>
                                        @if($payment->transaction_id)
                                            <small class="text-muted">{{ $payment->transaction_id }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-bold">{{ $payment->user->full_name }}</div>
                                        <small class="text-muted">{{ $payment->user->email }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-bold">{{ Str::limit($payment->summitRegistration->summit->title, 25) }}</div>
                                        <small class="text-muted">{{ $payment->summitRegistration->summit->year }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-bold text-success">TSh {{ number_format($payment->amount) }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ ucfirst($payment->payment_method) }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $payment->status === 'completed' ? 'success' : ($payment->status === 'pending' ? 'warning' : 'danger') }}">
                                        {{ ucfirst($payment->status) }}
                                    </span>
                                </td>
                                <td>
                                    <div>
                                        <div>{{ $payment->created_at->format('M j, Y') }}</div>
                                        @if($payment->paid_at)
                                            <small class="text-muted">Paid: {{ $payment->paid_at->format('M j, Y') }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('payments.show', $payment) }}" class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if($payment->status === 'pending')
                                        <a href="#" class="btn btn-outline-success" title="Mark as Completed">
                                            <i class="fas fa-check"></i>
                                        </a>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-4 p-3 bg-light rounded">
                    <div class="text-muted">
                        <i class="fas fa-info-circle me-2"></i>
                        Showing <strong>{{ $payments->firstItem() ?? 0 }}</strong> to <strong>{{ $payments->lastItem() ?? 0 }}</strong>
                        of <strong>{{ $payments->total() }}</strong> payments
                    </div>
                    <div>
                        {{ $payments->withQueryString()->links('pagination::bootstrap-4') }}
                    </div>
                </div>
                @else
                <div class="text-center py-4">
                    <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No payments found</h5>
                    <p class="text-muted">Try adjusting your filters to see more results.</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
