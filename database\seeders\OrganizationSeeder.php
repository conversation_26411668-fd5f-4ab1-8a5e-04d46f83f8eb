<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\University;
use App\Models\Conference;
use App\Models\Zone;
use App\Models\Branch;

class OrganizationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Universities
        $universities = [
            ['name' => 'University of Dar es Salaam', 'code' => 'UDSM', 'location' => 'Dar es Salaam'],
            ['name' => 'Sokoine University of Agriculture', 'code' => 'SUA', 'location' => 'Morogoro'],
            ['name' => 'Mzumbe University', 'code' => 'MU', 'location' => 'Morogoro'],
            ['name' => 'University of Dodoma', 'code' => 'UDOM', 'location' => 'Dodoma'],
            ['name' => 'Ardhi University', 'code' => 'ARU', 'location' => 'Dar es Salaam'],
            ['name' => 'Muhimbili University of Health and Allied Sciences', 'code' => 'MUHAS', 'location' => 'Dar es Salaam'],
            ['name' => 'Nelson Mandela African Institution of Science and Technology', 'code' => 'NM-AIST', 'location' => 'Arusha'],
            ['name' => 'State University of Zanzibar', 'code' => 'SUZA', 'location' => 'Zanzibar'],
        ];

        foreach ($universities as $university) {
            University::create($university);
        }

        // Create Conferences
        $conferences = [
            ['name' => 'East Tanzania Conference', 'code' => 'ETC', 'region' => 'Eastern Tanzania'],
            ['name' => 'North Tanzania Conference', 'code' => 'NTC', 'region' => 'Northern Tanzania'],
            ['name' => 'South Tanzania Conference', 'code' => 'STC', 'region' => 'Southern Tanzania'],
            ['name' => 'Central Tanzania Conference', 'code' => 'CTC', 'region' => 'Central Tanzania'],
        ];

        foreach ($conferences as $conference) {
            Conference::create($conference);
        }

        // Create Zones
        $zones = [
            // East Tanzania Conference Zones
            ['name' => 'Dar es Salaam Zone', 'code' => 'DSM', 'conference_id' => 1],
            ['name' => 'Coast Zone', 'code' => 'CST', 'conference_id' => 1],
            ['name' => 'Morogoro Zone', 'code' => 'MRG', 'conference_id' => 1],

            // North Tanzania Conference Zones
            ['name' => 'Arusha Zone', 'code' => 'ARU', 'conference_id' => 2],
            ['name' => 'Kilimanjaro Zone', 'code' => 'KLM', 'conference_id' => 2],
            ['name' => 'Manyara Zone', 'code' => 'MNY', 'conference_id' => 2],

            // South Tanzania Conference Zones
            ['name' => 'Mbeya Zone', 'code' => 'MBY', 'conference_id' => 3],
            ['name' => 'Iringa Zone', 'code' => 'IRG', 'conference_id' => 3],
            ['name' => 'Ruvuma Zone', 'code' => 'RUV', 'conference_id' => 3],

            // Central Tanzania Conference Zones
            ['name' => 'Dodoma Zone', 'code' => 'DDM', 'conference_id' => 4],
            ['name' => 'Singida Zone', 'code' => 'SNG', 'conference_id' => 4],
        ];

        foreach ($zones as $zone) {
            Zone::create($zone);
        }

        // Create Branches
        $branches = [
            // Dar es Salaam Zone Branches
            ['name' => 'UDSM Main Campus', 'code' => 'UDSM-MC', 'zone_id' => 1, 'university_id' => 1],
            ['name' => 'MUHAS Branch', 'code' => 'MUHAS-BR', 'zone_id' => 1, 'university_id' => 6],
            ['name' => 'Ardhi University Branch', 'code' => 'ARU-BR', 'zone_id' => 1, 'university_id' => 5],

            // Morogoro Zone Branches
            ['name' => 'SUA Main Campus', 'code' => 'SUA-MC', 'zone_id' => 3, 'university_id' => 2],
            ['name' => 'Mzumbe University Branch', 'code' => 'MU-BR', 'zone_id' => 3, 'university_id' => 3],

            // Arusha Zone Branches
            ['name' => 'NM-AIST Branch', 'code' => 'NMAIST-BR', 'zone_id' => 4, 'university_id' => 7],

            // Dodoma Zone Branches
            ['name' => 'UDOM Main Campus', 'code' => 'UDOM-MC', 'zone_id' => 10, 'university_id' => 4],
        ];

        foreach ($branches as $branch) {
            Branch::create($branch);
        }
    }
}
