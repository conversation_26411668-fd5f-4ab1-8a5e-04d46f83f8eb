<?php

namespace App\Http\Controllers;

use App\Models\Summit;
use App\Models\SummitRegistration;
use App\Models\Payment;
use App\Models\User;
use App\Models\Branch;
use App\Models\Zone;
use App\Models\Conference;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard based on user role.
     */
    public function index()
    {
        $user = Auth::user();

        // Check for multi-level admin roles first
        if ($user->hasRole('conference_admin') && $user->admin_conference_id) {
            return redirect()->route('admin.multi-level.conference.dashboard', $user->admin_conference_id);
        } elseif ($user->hasRole('zone_admin') && $user->admin_zone_id) {
            return redirect()->route('admin.multi-level.zone.dashboard', $user->admin_zone_id);
        } elseif ($user->hasRole('branch_admin') && $user->admin_branch_id) {
            return redirect()->route('admin.multi-level.branch.dashboard', $user->admin_branch_id);
        } elseif ($user->hasRole('admin')) {
            return $this->adminDashboard();
        } elseif ($user->hasRole('treasurer')) {
            return redirect()->route('treasurer.dashboard');
        } elseif ($user->hasRole('moderator')) {
            return $this->moderatorDashboard();
        } else {
            return $this->userDashboard();
        }
    }

    /**
     * Admin Dashboard
     */
    private function adminDashboard()
    {
        $stats = [
            'total_users' => User::count(),
            'total_summits' => Summit::count(),
            'current_summit' => Summit::current()->first(),
            'total_registrations' => SummitRegistration::count(),
            'total_payments' => Payment::where('status', 'completed')->sum('amount'),
            'pending_payments' => Payment::where('status', 'pending')->count(),
            'completed_payments' => Payment::where('status', 'completed')->count(),
            'failed_payments' => Payment::where('status', 'failed')->count(),
            'active_branches' => Branch::where('is_active', true)->count(),
            'total_conferences' => Conference::count(),
        ];

        // Recent activities
        $recentRegistrations = SummitRegistration::with(['user', 'summit'])
            ->latest()
            ->limit(5)
            ->get();

        $recentPayments = Payment::with(['user', 'summitRegistration.summit'])
            ->where('status', 'completed')
            ->latest()
            ->limit(5)
            ->get();

        // Hourly registration data for the last 24 hours
        $hourlyRegistrations = SummitRegistration::select(
            DB::raw('EXTRACT(HOUR FROM created_at) as hour'),
            DB::raw('COUNT(*) as count')
        )
        ->where('created_at', '>=', now()->subHours(24))
        ->groupBy('hour')
        ->pluck('count', 'hour')
        ->toArray();

        // Daily registration data for the last 7 days
        $dailyRegistrations = SummitRegistration::select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('COUNT(*) as count')
        )
        ->where('created_at', '>=', now()->subDays(7))
        ->groupBy('date')
        ->orderBy('date')
        ->pluck('count', 'date')
        ->toArray();

        // Monthly registration data for the last 12 months
        $monthlyRegistrations = SummitRegistration::select(
            DB::raw('EXTRACT(MONTH FROM created_at) as month'),
            DB::raw('COUNT(*) as count')
        )
        ->whereRaw('EXTRACT(YEAR FROM created_at) = ?', [date('Y')])
        ->groupBy('month')
        ->pluck('count', 'month')
        ->toArray();

        // Registration statistics for mini stats
        $registrationStats = [
            'today' => SummitRegistration::whereDate('created_at', today())->count(),
            'this_week' => SummitRegistration::where('created_at', '>=', now()->startOfWeek())->count(),
            'this_month' => SummitRegistration::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)->count(),
            'growth_rate' => $this->calculateGrowthRate(),
        ];

        // Payment statistics by branch
        $paymentsByBranch = Payment::join('summit_registrations', 'payments.summit_registration_id', '=', 'summit_registrations.id')
            ->join('users', 'summit_registrations.user_id', '=', 'users.id')
            ->join('branches', 'users.branch_id', '=', 'branches.id')
            ->where('payments.status', 'completed')
            ->select('branches.name', DB::raw('SUM(payments.amount) as total'))
            ->groupBy('branches.id', 'branches.name')
            ->orderBy('total', 'desc')
            ->limit(10)
            ->get();

        return view('dashboards.admin', compact(
            'stats',
            'recentRegistrations',
            'recentPayments',
            'hourlyRegistrations',
            'dailyRegistrations',
            'monthlyRegistrations',
            'registrationStats',
            'paymentsByBranch'
        ));
    }

    /**
     * Calculate growth rate for registrations
     */
    private function calculateGrowthRate()
    {
        $thisMonth = SummitRegistration::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)->count();

        $lastMonth = SummitRegistration::whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)->count();

        if ($lastMonth == 0) {
            return $thisMonth > 0 ? 100 : 0;
        }

        return round((($thisMonth - $lastMonth) / $lastMonth) * 100, 1);
    }

    /**
     * Treasurer Dashboard
     */
    private function treasurerDashboard()
    {
        $user = Auth::user();

        // Get user's branch hierarchy for filtering
        $branchIds = $this->getUserBranchHierarchy($user);

        $stats = [
            'total_payments' => Payment::whereHas('summitRegistration.user', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->where('status', 'completed')->sum('amount'),
            'pending_payments' => Payment::whereHas('summitRegistration.user', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->where('status', 'pending')->count(),
            'total_registrations' => SummitRegistration::whereHas('user', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->count(),
            'completed_payments' => Payment::whereHas('summitRegistration.user', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->where('status', 'completed')->count(),
        ];

        // Recent payments in user's hierarchy
        $recentPayments = Payment::with(['user', 'summitRegistration.summit'])
            ->whereHas('summitRegistration.user', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })
            ->where('status', 'completed')
            ->latest()
            ->limit(10)
            ->get();

        // Payment summary by branch
        $paymentsByBranch = Payment::join('summit_registrations', 'payments.summit_registration_id', '=', 'summit_registrations.id')
            ->join('users', 'summit_registrations.user_id', '=', 'users.id')
            ->join('branches', 'users.branch_id', '=', 'branches.id')
            ->whereIn('users.branch_id', $branchIds)
            ->where('payments.status', 'completed')
            ->select('branches.name', DB::raw('SUM(payments.amount) as total'), DB::raw('COUNT(*) as count'))
            ->groupBy('branches.id', 'branches.name')
            ->orderBy('total', 'desc')
            ->get();

        return view('dashboards.treasurer', compact(
            'stats',
            'recentPayments',
            'paymentsByBranch'
        ));
    }

    /**
     * Moderator Dashboard
     */
    private function moderatorDashboard()
    {
        $currentSummit = Summit::current()->first();

        $stats = [
            'current_summit' => $currentSummit,
            'total_registrations' => $currentSummit ? $currentSummit->registrations()->count() : 0,
            'checked_in_users' => 0, // Will implement with gate entries
            'total_activities' => $currentSummit ? $currentSummit->activities()->count() : 0,
        ];

        // Recent registrations for current summit
        $recentRegistrations = $currentSummit
            ? $currentSummit->registrations()->with('user')->latest()->limit(10)->get()
            : collect();

        // Upcoming activities
        $upcomingActivities = $currentSummit
            ? $currentSummit->activities()
                ->where('start_time', '>=', now())
                ->orderBy('start_time')
                ->limit(5)
                ->get()
            : collect();

        return view('dashboards.moderator', compact(
            'stats',
            'recentRegistrations',
            'upcomingActivities'
        ));
    }

    /**
     * User Dashboard
     */
    private function userDashboard()
    {
        $user = Auth::user();
        $currentSummit = Summit::current()->first();

        // User's active summit registrations (exclude cancelled)
        $registrations = $user->summitRegistrations()
            ->with(['summit', 'payments'])
            ->whereIn('status', ['pending', 'confirmed'])
            ->latest()
            ->get();

        // Current summit registration (only active)
        $currentRegistration = $currentSummit
            ? $user->summitRegistrations()
                ->where('summit_id', $currentSummit->id)
                ->whereIn('status', ['pending', 'confirmed'])
                ->first()
            : null;

        // User's payment history (only for active registrations)
        $payments = $user->payments()
            ->with('summitRegistration.summit')
            ->whereHas('summitRegistration', function($query) {
                $query->whereIn('status', ['pending', 'confirmed']);
            })
            ->latest()
            ->limit(10)
            ->get();

        // Upcoming activities for current summit
        $upcomingActivities = $currentSummit
            ? $currentSummit->activities()
                ->where('start_time', '>=', now())
                ->orderBy('start_time')
                ->limit(5)
                ->get()
            : collect();

        return view('dashboards.user', compact(
            'user',
            'currentSummit',
            'registrations',
            'currentRegistration',
            'payments',
            'upcomingActivities'
        ));
    }

    /**
     * Get branch hierarchy for user (for treasurer filtering)
     */
    private function getUserBranchHierarchy($user)
    {
        if (!$user->branch_id) {
            return Branch::pluck('id')->toArray();
        }

        $branch = $user->branch;
        $zone = $branch->zone;
        $conference = $zone->conference;

        // Return all branches in the same conference
        return Branch::whereHas('zone', function($q) use ($conference) {
            $q->where('conference_id', $conference->id);
        })->pluck('id')->toArray();
    }

    /**
     * Show user's registrations
     */
    public function registrations()
    {
        $user = Auth::user();
        // Show all registrations including cancelled for user's reference
        // But they can only interact with active ones
        $registrations = $user->summitRegistrations()
            ->with(['summit', 'payments'])
            ->latest()
            ->paginate(10);

        return view('user.registrations.index', compact('registrations'));
    }

    /**
     * Show specific registration
     */
    public function showRegistration(SummitRegistration $registration)
    {
        // Ensure user can only view their own registrations
        if ($registration->user_id !== Auth::id()) {
            abort(403);
        }

        $registration->load(['summit', 'payments']);

        return view('user.registrations.show', compact('registration'));
    }

    /**
     * Show registration form for a summit
     */
    public function createRegistration(Summit $summit)
    {
        $user = Auth::user();

        // Check if summit is active and accepting registrations
        if (!$summit->is_current || !$summit->is_active) {
            return redirect()->route('dashboard')
                ->with('error', 'This summit is not currently accepting registrations.');
        }

        // Check if user already has an active registration for this summit
        if (!SummitRegistration::canUserRegisterForSummit($user->id, $summit->id)) {
            $existingRegistration = SummitRegistration::getUserActiveRegistration($user->id, $summit->id);
            return redirect()->route('registrations.show', $existingRegistration)
                ->with('info', 'You are already registered for this summit.');
        }

        // Check if user profile is complete
        if (!$user->branch_id || !$user->first_name || !$user->last_name || !$user->phone) {
            return redirect()->route('profile.edit')
                ->with('error', 'Please complete your profile before registering for a summit.');
        }

        return view('user.registrations.create', compact('summit'));
    }

    /**
     * Store a new registration
     */
    public function storeRegistration(Request $request, Summit $summit)
    {
        $user = Auth::user();

        // Check if summit is active and accepting registrations
        if (!$summit->is_current || !$summit->is_active) {
            return redirect()->route('dashboard')
                ->with('error', 'This summit is not currently accepting registrations.');
        }

        // Check if user already has an active registration for this summit
        if (!SummitRegistration::canUserRegisterForSummit($user->id, $summit->id)) {
            $existingRegistration = SummitRegistration::getUserActiveRegistration($user->id, $summit->id);
            return redirect()->route('registrations.show', $existingRegistration)
                ->with('info', 'You are already registered for this summit.');
        }

        // Check if user profile is complete
        if (!$user->branch_id || !$user->first_name || !$user->last_name || !$user->phone) {
            return redirect()->route('profile.edit')
                ->with('error', 'Please complete your profile before registering for a summit.');
        }

        // Validate request
        $validated = $request->validate([
            'terms_accepted' => 'required|accepted',
            'dietary_requirements' => 'nullable|string|max:500',
            'special_needs' => 'nullable|string|max:500',
        ]);

        // Generate registration number
        $registrationNumber = 'REG-' . $summit->year . '-' . str_pad(
            $summit->registrations()->count() + 1,
            4,
            '0',
            STR_PAD_LEFT
        );

        // Prepare special requirements data
        $specialRequirements = [];
        if ($validated['dietary_requirements']) {
            $specialRequirements['dietary_requirements'] = $validated['dietary_requirements'];
        }
        if ($validated['special_needs']) {
            $specialRequirements['special_needs'] = $validated['special_needs'];
        }

        // Create registration
        $registration = SummitRegistration::create([
            'user_id' => $user->id,
            'summit_id' => $summit->id,
            'registration_number' => $registrationNumber,
            'status' => 'pending',
            'total_amount' => $summit->registration_fee,
            'paid_amount' => 0,
            'balance' => $summit->registration_fee,
            'payment_complete' => false,
            'registered_at' => now(),
            'special_requirements' => !empty($specialRequirements) ? json_encode($specialRequirements) : null,
        ]);

        return redirect()->route('registrations.show', $registration)
            ->with('success', 'Successfully registered for ' . $summit->title . '! You can now proceed with payment.');
    }

    /**
     * Show user's payments
     */
    public function payments()
    {
        $user = Auth::user();
        // Show all payments including those for cancelled registrations
        // for complete payment history
        $payments = $user->payments()
            ->with('summitRegistration.summit')
            ->latest()
            ->paginate(10);

        return view('user.payments.index', compact('payments'));
    }

    /**
     * Show user's ID card
     */
    public function idCard()
    {
        $user = Auth::user();
        $currentSummit = Summit::current()->first();
        $registration = null;

        if ($currentSummit) {
            $registration = $user->summitRegistrations()
                ->where('summit_id', $currentSummit->id)
                ->where('payment_complete', true)
                ->where('status', 'confirmed')
                ->first();
        }

        return view('user.id-card', compact('user', 'currentSummit', 'registration'));
    }

    /**
     * Show help page
     */
    public function help()
    {
        return view('user.help');
    }

    /**
     * Cancel user's registration
     */
    public function cancelRegistration(SummitRegistration $registration)
    {
        $user = Auth::user();

        // Check if the registration belongs to the authenticated user
        if ($registration->user_id !== $user->id) {
            abort(403, 'Unauthorized action.');
        }

        // Use the model method to check if cancellation is allowed
        if (!$registration->canBeCancelledByUser()) {
            $errorMessage = 'Cannot cancel registration.';

            if ($registration->status === 'cancelled') {
                $errorMessage = 'Registration is already cancelled.';
            } elseif ($registration->hasSuccessfulPayments()) {
                $errorMessage = 'Cannot cancel registration with successful payments. Please contact admin for refund.';
            } elseif ($registration->summit->registration_deadline < now()) {
                $errorMessage = 'Registration deadline has passed. Cannot cancel registration.';
            }

            return redirect()->back()->with('error', $errorMessage);
        }

        // Cancel any pending/failed payments for this registration
        $registration->payments()
            ->whereIn('status', ['pending', 'processing', 'failed'])
            ->update(['status' => 'cancelled']);

        // Update registration status
        $registration->update([
            'status' => 'cancelled'
        ]);

        return redirect()->route('registrations.index')
            ->with('success', 'Registration has been cancelled successfully. Any pending or failed payments have also been cancelled.');
    }
}
