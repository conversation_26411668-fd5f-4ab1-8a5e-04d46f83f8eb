<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Revenue Summary Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            color: #333;
            font-size: 24px;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        .summary-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .summary-card h3 {
            margin: 0;
            color: #28a745;
            font-size: 20px;
        }
        .summary-card p {
            margin: 5px 0 0 0;
            color: #666;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .section-title {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            margin: 20px 0 10px 0;
        }
        .amount {
            font-weight: bold;
            color: #28a745;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #666;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>TUCASA Summit Management</h1>
        <h2>Revenue Summary Report</h2>
        <p>Period: {{ ucfirst($period) }} Report</p>
        <p>Generated on: {{ $generated_at }} by {{ $generated_by }}</p>
    </div>

    <div class="summary-grid">
        <div class="summary-card">
            <h3>{{ number_format($summary['total_revenue'] ?? 0, 0) }} TZS</h3>
            <p>Total Revenue</p>
        </div>
        <div class="summary-card">
            <h3>{{ $summary['completed_payments'] ?? 0 }}</h3>
            <p>Completed Payments</p>
        </div>
        <div class="summary-card">
            <h3>{{ number_format($summary['outstanding_amount'] ?? 0, 0) }} TZS</h3>
            <p>Outstanding Amount</p>
        </div>
    </div>

    <h3 class="section-title">Monthly Revenue Breakdown</h3>
    <table>
        <thead>
            <tr>
                <th>Month</th>
                <th>Year</th>
                <th>Revenue (TZS)</th>
            </tr>
        </thead>
        <tbody>
            @if(isset($monthly_revenue) && $monthly_revenue->count() > 0)
                @foreach($monthly_revenue as $month)
                <tr>
                    <td>{{ date('F', mktime(0, 0, 0, $month->month, 1)) }}</td>
                    <td>{{ $month->year }}</td>
                    <td class="amount">{{ number_format($month->total, 0) }} TZS</td>
                </tr>
                @endforeach
            @else
                <tr>
                    <td colspan="3" style="text-align: center; color: #666;">No monthly data available</td>
                </tr>
            @endif
        </tbody>
    </table>

    <h3 class="section-title">Revenue by Branch</h3>
    <table>
        <thead>
            <tr>
                <th>Branch</th>
                <th>Conference</th>
                <th>Revenue (TZS)</th>
                <th>Transactions</th>
            </tr>
        </thead>
        <tbody>
            @if(isset($revenue_by_branch) && $revenue_by_branch->count() > 0)
                @foreach($revenue_by_branch->take(15) as $branch)
                <tr>
                    <td>{{ $branch->branch_name }}</td>
                    <td>{{ $branch->conference_name }}</td>
                    <td class="amount">{{ number_format($branch->total, 0) }} TZS</td>
                    <td>{{ $branch->count }}</td>
                </tr>
                @endforeach
            @else
                <tr>
                    <td colspan="4" style="text-align: center; color: #666;">No branch data available</td>
                </tr>
            @endif
        </tbody>
    </table>

    <h3 class="section-title">Revenue by Conference</h3>
    <table>
        <thead>
            <tr>
                <th>Conference</th>
                <th>Revenue (TZS)</th>
                <th>Transactions</th>
                <th>Percentage</th>
            </tr>
        </thead>
        <tbody>
            @if(isset($revenue_by_conference) && $revenue_by_conference->count() > 0)
                @php
                    $totalConferenceRevenue = $revenue_by_conference->sum('total');
                @endphp
                @foreach($revenue_by_conference as $conference)
                <tr>
                    <td>{{ $conference->name }}</td>
                    <td class="amount">{{ number_format($conference->total, 0) }} TZS</td>
                    <td>{{ $conference->count }}</td>
                    <td>{{ $totalConferenceRevenue > 0 ? number_format(($conference->total / $totalConferenceRevenue) * 100, 1) : 0 }}%</td>
                </tr>
                @endforeach
            @else
                <tr>
                    <td colspan="4" style="text-align: center; color: #666;">No conference data available</td>
                </tr>
            @endif
        </tbody>
    </table>

    <h3 class="section-title">Payment Methods</h3>
    <table>
        <thead>
            <tr>
                <th>Payment Method</th>
                <th>Revenue (TZS)</th>
                <th>Transactions</th>
                <th>Average Amount</th>
            </tr>
        </thead>
        <tbody>
            @if(isset($payment_method_stats) && $payment_method_stats->count() > 0)
                @foreach($payment_method_stats as $method)
                <tr>
                    <td>{{ ucfirst($method->payment_method ?? 'Unknown') }}</td>
                    <td class="amount">{{ number_format($method->total, 0) }} TZS</td>
                    <td>{{ $method->count }}</td>
                    <td class="amount">{{ $method->count > 0 ? number_format($method->total / $method->count, 0) : 0 }} TZS</td>
                </tr>
                @endforeach
            @else
                <tr>
                    <td colspan="4" style="text-align: center; color: #666;">No payment method data available</td>
                </tr>
            @endif
        </tbody>
    </table>

    <div class="footer">
        <p>This report was generated automatically by TUCASA Summit Management System</p>
        <p>For questions or concerns, please contact the system administrator</p>
        <p><strong>Confidential:</strong> This report contains sensitive financial information</p>
    </div>
</body>
</html>
