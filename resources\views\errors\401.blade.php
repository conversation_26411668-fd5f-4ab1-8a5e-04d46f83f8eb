@extends('errors.layout')

@section('title', 'Authentication Required')

@section('icon')
    <i class="fas fa-lock text-warning"></i>
@endsection

@section('code', '401')

@section('title', 'Authentication Required')

@section('message')
    @if(session('session_expired'))
        Your session has expired due to inactivity. Please login again to continue.
    @else
        You need to be authenticated to access this resource. Please login to continue.
    @endif
@endsection

@section('actions')
    <a href="{{ route('login') }}" class="btn btn-primary">
        <i class="fas fa-sign-in-alt me-2"></i>Login
    </a>
    
    @if(Route::has('register'))
    <a href="{{ route('register') }}" class="btn btn-outline-secondary">
        <i class="fas fa-user-plus me-2"></i>Register
    </a>
    @endif
@endsection

@section('details')
    <h6><i class="fas fa-info-circle me-2"></i>What happened?</h6>
    <ul>
        @if(session('session_expired'))
            <li>Your session expired after 5 minutes of inactivity</li>
            <li>This is a security measure to protect your account</li>
            <li>Simply login again to continue where you left off</li>
        @else
            <li>This page requires you to be logged in</li>
            <li>Your authentication credentials may have expired</li>
            <li>You may have been logged out for security reasons</li>
        @endif
        <li>After logging in, you'll be redirected to your intended page</li>
        <li>If you don't have an account, you can register for free</li>
    </ul>
@endsection
