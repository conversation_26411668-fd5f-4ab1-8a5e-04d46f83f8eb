@extends('layouts.admin')

@section('title', 'University Details')
@section('page-title', 'University Details')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">{{ $university->name }}</h2>
                <p class="text-muted">University details and statistics.</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('admin.universities.edit', $university) }}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>Edit University
                </a>
                <a href="{{ route('admin.universities.index') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Universities
                </a>
            </div>
        </div>
    </div>
</div>

<!-- University Information -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-university me-2"></i>University Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Name:</strong></td>
                                <td>{{ $university->name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Code:</strong></td>
                                <td><span class="badge bg-primary">{{ $university->code }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Location:</strong></td>
                                <td>{{ $university->location ?? 'Not specified' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    <span class="badge bg-{{ $university->is_active ? 'success' : 'secondary' }}">
                                        {{ $university->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Total Branches:</strong></td>
                                <td><span class="badge bg-info">{{ $university->branches->count() }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Total Users:</strong></td>
                                <td><span class="badge bg-success">{{ $university->users->count() }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Created:</strong></td>
                                <td>{{ $university->created_at->format('F j, Y') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Last Updated:</strong></td>
                                <td>{{ $university->updated_at->format('F j, Y') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                @if($university->description)
                <div class="mt-3">
                    <h6>Description</h6>
                    <p class="text-muted">{{ $university->description }}</p>
                </div>
                @endif
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Quick Stats</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Active Branches</span>
                    <span class="badge bg-success">{{ $university->branches->where('is_active', true)->count() }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Inactive Branches</span>
                    <span class="badge bg-secondary">{{ $university->branches->where('is_active', false)->count() }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Active Users</span>
                    <span class="badge bg-success">{{ $university->users->where('is_active', true)->count() }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Inactive Users</span>
                    <span class="badge bg-secondary">{{ $university->users->where('is_active', false)->count() }}</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Branches -->
@if($university->branches->count() > 0)
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-code-branch me-2"></i>Branches ({{ $university->branches->count() }})</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Branch Name</th>
                                <th>Code</th>
                                <th>Zone</th>
                                <th>Conference</th>
                                <th>Users</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($university->branches as $branch)
                            <tr>
                                <td>
                                    <div class="fw-bold">{{ $branch->name }}</div>
                                    @if($branch->description)
                                        <small class="text-muted">{{ Str::limit($branch->description, 40) }}</small>
                                    @endif
                                </td>
                                <td><span class="badge bg-primary">{{ $branch->code }}</span></td>
                                <td>{{ $branch->zone->name ?? 'N/A' }}</td>
                                <td>{{ $branch->zone->conference->name ?? 'N/A' }}</td>
                                <td>
                                    <span class="badge bg-info">{{ $branch->users->count() }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $branch->is_active ? 'success' : 'secondary' }}">
                                        {{ $branch->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('admin.branches.show', $branch) }}" 
                                           class="btn btn-outline-primary" title="View Branch">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.branches.edit', $branch) }}" 
                                           class="btn btn-outline-secondary" title="Edit Branch">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Recent Users -->
@if($university->users->count() > 0)
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-users me-2"></i>Recent Users ({{ $university->users->count() }} total)</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Email</th>
                                <th>Branch</th>
                                <th>Course</th>
                                <th>Status</th>
                                <th>Joined</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($university->users->take(10) as $user)
                            <tr>
                                <td>
                                    <div class="fw-bold">{{ $user->full_name }}</div>
                                    @if($user->student_id)
                                        <small class="text-muted">ID: {{ $user->student_id }}</small>
                                    @endif
                                </td>
                                <td>{{ $user->email }}</td>
                                <td>{{ $user->branch->name ?? 'N/A' }}</td>
                                <td>{{ $user->course ?? 'N/A' }}</td>
                                <td>
                                    <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }}">
                                        {{ $user->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td>{{ $user->created_at->format('M j, Y') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('admin.users.show', $user) }}" 
                                           class="btn btn-outline-primary" title="View User">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.users.edit', $user) }}" 
                                           class="btn btn-outline-secondary" title="Edit User">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                @if($university->users->count() > 10)
                <div class="text-center mt-3">
                    <a href="{{ route('admin.users.index', ['university_id' => $university->id]) }}" class="btn btn-outline-primary">
                        <i class="fas fa-users me-2"></i>View All Users ({{ $university->users->count() }})
                    </a>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endif
@endsection
