<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Minister;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class MinisterController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Minister::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('title', 'like', "%{$search}%")
                  ->orWhere('position', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status);
        }

        $ministers = $query->ordered()->paginate(10);
        return view('admin.ministers.index', compact('ministers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.ministers.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'bio' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'position' => 'nullable|string|max:255',
            'order' => 'nullable|integer|min:0',
            'is_active' => 'nullable|boolean',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('minister-images', 'public');
        }

        // Handle checkbox values (unchecked checkboxes don't send values)
        $validated['is_active'] = $request->has('is_active');

        Minister::create($validated);

        return redirect()->route('admin.ministers.index')
            ->with('success', 'Minister created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Minister $minister)
    {
        return view('admin.ministers.show', compact('minister'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Minister $minister)
    {
        return view('admin.ministers.edit', compact('minister'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Minister $minister)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'bio' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'position' => 'nullable|string|max:255',
            'order' => 'nullable|integer|min:0',
            'is_active' => 'nullable|boolean',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($minister->image) {
                Storage::disk('public')->delete($minister->image);
            }
            $validated['image'] = $request->file('image')->store('minister-images', 'public');
        }

        // Handle checkbox values (unchecked checkboxes don't send values)
        $validated['is_active'] = $request->has('is_active');

        $minister->update($validated);

        return redirect()->route('admin.ministers.index')
            ->with('success', 'Minister updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Minister $minister)
    {
        // Delete image
        if ($minister->image) {
            Storage::disk('public')->delete($minister->image);
        }

        $minister->delete();

        return redirect()->route('admin.ministers.index')
            ->with('success', 'Minister deleted successfully.');
    }
}
