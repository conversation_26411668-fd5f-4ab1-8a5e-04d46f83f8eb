<?php $__env->startSection('title', 'Conferences Management'); ?>
<?php $__env->startSection('page-title', 'Conferences Management'); ?>

<?php $__env->startPush('styles'); ?>
<style>
.page-header {
    background-color: var(--md-surface);
    border-radius: var(--md-border-radius-lg);
    padding: var(--md-spacing-xl);
    margin-bottom: var(--md-spacing-lg);
    box-shadow: var(--md-elevation-1);
}

.page-title {
    @extend .md-headline-medium;
    margin: 0 0 var(--md-spacing-xs) 0;
    color: var(--md-on-surface);
}

.page-subtitle {
    @extend .md-body-large;
    color: var(--md-on-surface-variant);
    margin: 0;
}

.search-card {
    background-color: var(--md-surface);
    border-radius: var(--md-border-radius-md);
    padding: var(--md-spacing-lg);
    margin-bottom: var(--md-spacing-lg);
    box-shadow: var(--md-elevation-1);
}

.search-title {
    @extend .md-title-medium;
    margin: 0 0 var(--md-spacing-md) 0;
    display: flex;
    align-items: center;
    gap: var(--md-spacing-sm);
    color: var(--md-on-surface);
}

.search-form {
    display: flex;
    gap: var(--md-spacing-md);
    align-items: end;
}

.search-input-container {
    flex: 1;
}

.search-actions {
    display: flex;
    gap: var(--md-spacing-sm);
}

@media (max-width: 768px) {
    .page-header {
        padding: var(--md-spacing-lg);
    }

    .search-form {
        flex-direction: column;
        align-items: stretch;
    }

    .search-actions {
        justify-content: stretch;
    }

    .search-actions .md-button-filled,
    .search-actions .md-button-outlined {
        flex: 1;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<div class="page-header">
    <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: var(--md-spacing-md);">
        <div>
            <h1 class="page-title">Conferences Management</h1>
            <p class="page-subtitle">Manage conferences in the system.</p>
        </div>
        <a href="<?php echo e(route('admin.conferences.create')); ?>" class="md-button-filled">
            <span class="material-icons">add</span>
            Add Conference
        </a>
    </div>
</div>

<!-- Search and Filter -->
<div class="search-card">
    <h2 class="search-title">
        <span class="material-icons">search</span>
        Search & Filter
    </h2>
    <form method="GET" action="<?php echo e(route('admin.conferences.index')); ?>" class="search-form">
        <div class="search-input-container">
            <div class="md-text-field">
                <input
                    type="text"
                    name="search"
                    class="md-text-field-input"
                    value="<?php echo e(request('search')); ?>"
                    placeholder=" "
                >
                <label class="md-text-field-label">Search by name, code, or region...</label>
            </div>
        </div>
        <div class="search-actions">
            <button type="submit" class="md-button-filled">
                <span class="material-icons">search</span>
                Search
            </button>
            <a href="<?php echo e(route('admin.conferences.index')); ?>" class="md-button-outlined">
                <span class="material-icons">clear</span>
                Clear
            </a>
        </div>
    </form>
</div>

<!-- Conferences Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>Conferences (<?php echo e($conferences->total()); ?>)</h5>
            </div>
            <div class="card-body">
                <?php if($conferences->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Conference</th>
                                <th>Code</th>
                                <th>Region</th>
                                <th>Zones</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $conferences; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $conference): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <div>
                                        <div class="fw-bold"><?php echo e($conference->name); ?></div>
                                        <?php if($conference->description): ?>
                                            <small class="text-muted"><?php echo e(Str::limit($conference->description, 50)); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary"><?php echo e($conference->code); ?></span>
                                </td>
                                <td><?php echo e($conference->region ?? 'N/A'); ?></td>
                                <td>
                                    <div class="text-center">
                                        <span class="badge bg-info"><?php echo e($conference->zones->count()); ?></span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo e($conference->is_active ? 'success' : 'secondary'); ?>">
                                        <?php echo e($conference->is_active ? 'Active' : 'Inactive'); ?>

                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?php echo e(route('admin.conferences.show', $conference)); ?>" 
                                           class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.conferences.edit', $conference)); ?>" 
                                           class="btn btn-outline-secondary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php if(!$conference->zones->count()): ?>
                                        <form method="POST" action="<?php echo e(route('admin.conferences.destroy', $conference)); ?>" 
                                              class="d-inline" onsubmit="return confirm('Are you sure you want to delete this conference?')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-outline-danger" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($conferences->withQueryString()->links()); ?>

                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-globe fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No conferences found</h5>
                    <p class="text-muted">
                        <?php if(request('search')): ?>
                            No conferences match your search criteria.
                        <?php else: ?>
                            Get started by adding your first conference.
                        <?php endif; ?>
                    </p>
                    <?php if(!request('search')): ?>
                    <a href="<?php echo e(route('admin.conferences.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Conference
                    </a>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\summit-management-system\resources\views/admin/conferences/index.blade.php ENDPATH**/ ?>