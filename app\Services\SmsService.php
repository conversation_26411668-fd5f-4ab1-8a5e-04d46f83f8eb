<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SmsService
{
    private $username;
    private $apiKey;
    private $from;
    private $baseUrl;

    public function __construct()
    {
        $this->username = config('services.africastalking.username');
        $this->apiKey = config('services.africastalking.api_key');
        $this->from = config('services.africastalking.from');
        $this->baseUrl = 'https://api.africastalking.com/version1/messaging';
    }

    /**
     * Send SMS to a single recipient
     */
    public function sendSms(string $phoneNumber, string $message): array
    {
        return $this->sendBulkSms([$phoneNumber], $message);
    }

    /**
     * Send SMS to multiple recipients
     */
    public function sendBulkSms(array $phoneNumbers, string $message): array
    {
        try {
            // Format phone numbers
            $formattedNumbers = array_map(function($number) {
                return $this->formatPhoneNumber($number);
            }, $phoneNumbers);

            $response = Http::withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => 'application/x-www-form-urlencoded',
                'apiKey' => $this->apiKey,
            ])->asForm()->post($this->baseUrl, [
                'username' => $this->username,
                'to' => implode(',', $formattedNumbers),
                'message' => $message,
                'from' => $this->from,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                Log::info('SMS sent successfully', [
                    'recipients' => $formattedNumbers,
                    'message' => $message,
                    'response' => $data
                ]);

                return [
                    'success' => true,
                    'data' => $data,
                    'message' => 'SMS sent successfully'
                ];
            } else {
                Log::error('SMS sending failed', [
                    'recipients' => $formattedNumbers,
                    'message' => $message,
                    'response' => $response->body()
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to send SMS',
                    'error' => $response->body()
                ];
            }
        } catch (\Exception $e) {
            Log::error('SMS service error: ' . $e->getMessage());
            
            return [
                'success' => false,
                'message' => 'SMS service unavailable',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Send registration confirmation SMS
     */
    public function sendRegistrationConfirmation(string $phoneNumber, string $userName, string $summitTitle): array
    {
        $message = "Hello {$userName}! Your registration for {$summitTitle} has been received. Complete your payment to secure your spot. TUCASA STU Summit Management.";
        
        return $this->sendSms($phoneNumber, $message);
    }

    /**
     * Send payment confirmation SMS
     */
    public function sendPaymentConfirmation(string $phoneNumber, string $userName, float $amount, string $summitTitle): array
    {
        $message = "Hello {$userName}! Your payment of TSh " . number_format($amount) . " for {$summitTitle} has been received. Thank you! TUCASA STU Summit Management.";
        
        return $this->sendSms($phoneNumber, $message);
    }

    /**
     * Send payment reminder SMS
     */
    public function sendPaymentReminder(string $phoneNumber, string $userName, float $balance, string $summitTitle, string $deadline): array
    {
        $message = "Hello {$userName}! Reminder: You have a balance of TSh " . number_format($balance) . " for {$summitTitle}. Payment deadline: {$deadline}. TUCASA STU Summit Management.";
        
        return $this->sendSms($phoneNumber, $message);
    }

    /**
     * Send summit reminder SMS
     */
    public function sendSummitReminder(string $phoneNumber, string $userName, string $summitTitle, string $startDate, string $venue): array
    {
        $message = "Hello {$userName}! {$summitTitle} starts on {$startDate} at {$venue}. Don't forget to bring your ID card. See you there! TUCASA STU Summit Management.";
        
        return $this->sendSms($phoneNumber, $message);
    }

    /**
     * Send custom SMS
     */
    public function sendCustomSms(string $phoneNumber, string $message): array
    {
        $customMessage = $message . " - TUCASA STU Summit Management.";
        
        return $this->sendSms($phoneNumber, $customMessage);
    }

    /**
     * Format phone number for Tanzania
     */
    private function formatPhoneNumber(string $phoneNumber): string
    {
        // Remove any non-digit characters
        $phoneNumber = preg_replace('/[^0-9]/', '', $phoneNumber);
        
        // Handle different formats
        if (strlen($phoneNumber) === 9) {
            // Format: 712345678 -> +255712345678
            return '+255' . $phoneNumber;
        } elseif (strlen($phoneNumber) === 10 && substr($phoneNumber, 0, 1) === '0') {
            // Format: 0712345678 -> +255712345678
            return '+255' . substr($phoneNumber, 1);
        } elseif (strlen($phoneNumber) === 12 && substr($phoneNumber, 0, 3) === '255') {
            // Format: 255712345678 -> +255712345678
            return '+' . $phoneNumber;
        } elseif (strlen($phoneNumber) === 13 && substr($phoneNumber, 0, 4) === '+255') {
            // Already in correct format
            return $phoneNumber;
        }
        
        // If we can't determine the format, return as is
        return $phoneNumber;
    }

    /**
     * Validate Tanzania phone number
     */
    public function isValidTanzanianNumber(string $phoneNumber): bool
    {
        $formatted = $this->formatPhoneNumber($phoneNumber);
        
        // Check if it's a valid Tanzanian mobile number
        return preg_match('/^\+255[67]\d{8}$/', $formatted);
    }

    /**
     * Get SMS delivery status
     */
    public function getDeliveryStatus(string $messageId): array
    {
        try {
            $response = Http::withHeaders([
                'Accept' => 'application/json',
                'apiKey' => $this->apiKey,
            ])->get('https://api.africastalking.com/version1/messaging', [
                'username' => $this->username,
                'messageId' => $messageId,
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json()
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to get delivery status'
                ];
            }
        } catch (\Exception $e) {
            Log::error('SMS status check error: ' . $e->getMessage());
            
            return [
                'success' => false,
                'message' => 'Status check unavailable',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Send bulk SMS to all users in a branch/zone/conference
     */
    public function sendBulkSmsToOrganization(string $organizationType, int $organizationId, string $message): array
    {
        $users = collect();
        
        switch ($organizationType) {
            case 'branch':
                $users = \App\Models\User::where('branch_id', $organizationId)
                    ->whereNotNull('phone')
                    ->where('is_active', true)
                    ->get();
                break;
                
            case 'zone':
                $users = \App\Models\User::whereHas('branch', function($q) use ($organizationId) {
                    $q->where('zone_id', $organizationId);
                })
                ->whereNotNull('phone')
                ->where('is_active', true)
                ->get();
                break;
                
            case 'conference':
                $users = \App\Models\User::whereHas('branch.zone', function($q) use ($organizationId) {
                    $q->where('conference_id', $organizationId);
                })
                ->whereNotNull('phone')
                ->where('is_active', true)
                ->get();
                break;
        }
        
        if ($users->isEmpty()) {
            return [
                'success' => false,
                'message' => 'No users found with phone numbers'
            ];
        }
        
        $phoneNumbers = $users->pluck('phone')->toArray();
        
        return $this->sendBulkSms($phoneNumbers, $message);
    }
}
