<?php $__env->startSection('title', 'Payment Management'); ?>
<?php $__env->startSection('page-title', 'Payment Management'); ?>

<?php $__env->startSection('content'); ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">Payment Management</h2>
                    <p class="text-muted">Manage and monitor all payment transactions.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-2 col-md-4 mb-2">
            <div class="payment-stats-card blue h-100">
                <div class="stat-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
                <h3 class="stat-number"><?php echo e(number_format($stats['total_payments'])); ?></h3>
                <p class="stat-label">Total Payments</p>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 mb-2">
            <div class="payment-stats-card green h-100">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3 class="stat-number"><?php echo e(number_format($stats['completed_payments'])); ?></h3>
                <p class="stat-label">Completed</p>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 mb-2">
            <div class="payment-stats-card orange h-100">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <h3 class="stat-number"><?php echo e(number_format($stats['pending_payments'])); ?></h3>
                <p class="stat-label">Pending</p>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-2">
            <div class="payment-stats-card purple h-100">
                <div class="stat-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <h3 class="stat-number">TSh <?php echo e(number_format($stats['total_amount'])); ?></h3>
                <p class="stat-label">Total Revenue</p>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-2">
            <div class="payment-stats-card red h-100">
                <div class="stat-icon">
                    <i class="fas fa-hourglass-half"></i>
                </div>
                <h3 class="stat-number">TSh <?php echo e(number_format($stats['pending_amount'])); ?></h3>
                <p class="stat-label">Pending Amount</p>
            </div>
        </div>
    </div>

    <!-- Filter Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="payment-filter-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters & Search</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('admin.payments.index')); ?>">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" name="search" id="search" class="form-control"
                                    placeholder="Reference, name, email..." value="<?php echo e(request('search')); ?>">
                            </div>
                            <div class="col-md-2">
                                <label for="status" class="form-label">Status</label>
                                <select name="status" id="status" class="form-select">
                                    <option value="">All Status</option>
                                    <option value="completed" <?php echo e(request('status') == 'completed' ? 'selected' : ''); ?>>
                                        Completed</option>
                                    <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Pending
                                    </option>
                                    <option value="failed" <?php echo e(request('status') == 'failed' ? 'selected' : ''); ?>>Failed
                                    </option>
                                    <option value="cancelled" <?php echo e(request('status') == 'cancelled' ? 'selected' : ''); ?>>
                                        Cancelled</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="payment_method" class="form-label">Method</label>
                                <select name="payment_method" id="payment_method" class="form-select">
                                    <option value="">All Methods</option>
                                    <?php $__currentLoopData = $paymentMethods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $method): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($method); ?>" <?php echo e(request('payment_method') == $method ? 'selected' : ''); ?>>
                                            <?php echo e(ucfirst($method)); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="summit_id" class="form-label">Summit</label>
                                <select name="summit_id" id="summit_id" class="form-select">
                                    <option value="">All Summits</option>
                                    <?php $__currentLoopData = $summits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $summit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($summit->id); ?>" <?php echo e(request('summit_id') == $summit->id ? 'selected' : ''); ?>>
                                            <?php echo e($summit->title); ?> (<?php echo e($summit->year); ?>)
                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="conference_id" class="form-label">Conference</label>
                                <select name="conference_id" id="conference_id" class="form-select">
                                    <option value="">All Conferences</option>
                                    <?php $__currentLoopData = $conferences; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $conference): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($conference->id); ?>" <?php echo e(request('conference_id') == $conference->id ? 'selected' : ''); ?>>
                                            <?php echo e($conference->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-3">
                                <label for="date_from" class="form-label">Date From</label>
                                <input type="date" name="date_from" id="date_from" class="form-control"
                                    value="<?php echo e(request('date_from')); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="date_to" class="form-label">Date To</label>
                                <input type="date" name="date_to" id="date_to" class="form-control"
                                    value="<?php echo e(request('date_to')); ?>">
                            </div>
                            <div class="col-md-6 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-2"></i>Apply Filters
                                </button>
                                <a href="<?php echo e(route('admin.payments.index')); ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>Clear Filters
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Payments Table -->
    <div class="row">
        <div class="col-12">
            <div class="payment-table-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-table me-2"></i>Payment Transactions</h5>
                </div>
                <div class="card-body">
                    <?php if($payments->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Reference</th>
                                        <th>User</th>
                                        <th>Summit</th>
                                        <th>Amount</th>
                                        <th>Method</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <div class="fw-bold">#<?php echo e($payment->payment_reference); ?></div>
                                                    <?php if($payment->transaction_id): ?>
                                                        <small class="text-muted"><?php echo e($payment->transaction_id); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <div class="fw-bold"><?php echo e($payment->user->full_name); ?></div>
                                                    <small class="text-muted"><?php echo e($payment->user->email); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <div class="fw-bold">
                                                        <?php echo e(Str::limit($payment->summitRegistration->summit->title, 25)); ?></div>
                                                    <small
                                                        class="text-muted"><?php echo e($payment->summitRegistration->summit->year); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-bold text-success">TSh <?php echo e(number_format($payment->amount)); ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo e(ucfirst($payment->payment_method)); ?></span>
                                            </td>
                                            <td>
                                                <span
                                                    class="badge bg-<?php echo e($payment->status === 'completed' ? 'success' : ($payment->status === 'pending' ? 'warning' : 'danger')); ?>">
                                                    <?php echo e(ucfirst($payment->status)); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <div>
                                                    <div><?php echo e($payment->created_at->format('M j, Y')); ?></div>
                                                    <?php if($payment->paid_at): ?>
                                                        <small class="text-muted">Paid:
                                                            <?php echo e($payment->paid_at->format('M j, Y')); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm m-2">
    <a href="<?php echo e(route('admin.payments.show', $payment)); ?>" title="View Details" class="me-2">
        <i class="fas fa-eye text-primary"></i>
    </a>

    <?php if($payment->status === 'pending'): ?>
        <form method="POST" action="<?php echo e(route('admin.payments.approve', $payment)); ?>" class="d-inline-block me-2">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PATCH'); ?>
            <button type="submit"
                    class="border-0 bg-transparent p-0 m-0"
                    title="Approve Payment"
                    aria-label="Approve Payment"
                    onclick="return confirm('Are you sure you want to approve this payment?')">
                <i class="fas fa-check text-success"></i>
            </button>
        </form>
    <?php endif; ?>

    <?php if($payment->status !== 'completed'): ?>
        <form method="POST" action="<?php echo e(route('admin.payments.destroy', $payment)); ?>" class="d-inline-block">
            <?php echo csrf_field(); ?>
            <?php echo method_field('DELETE'); ?>
            <button type="submit"
                    class="border-0 bg-transparent p-0 m-0"
                    title="Delete Payment"
                    aria-label="Delete Payment"
                    onclick="return confirm('Are you sure you want to delete this payment?')">
                <i class="fas fa-trash text-danger"></i>
            </button>
        </form>
    <?php endif; ?>
</div>


                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-4 p-3 bg-light rounded">
                            <div class="text-muted">
                                <i class="fas fa-info-circle me-2"></i>
                                Showing <strong><?php echo e($payments->firstItem() ?? 0); ?></strong> to <strong><?php echo e($payments->lastItem() ?? 0); ?></strong>
                                of <strong><?php echo e($payments->total()); ?></strong> payments
                            </div>
                            <div>
                                <?php echo e($payments->withQueryString()->links('pagination::bootstrap-4')); ?>

                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No payments found</h5>
                            <p class="text-muted">Try adjusting your filters to see more results.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\summit-management-system\resources\views/admin/payments/index.blade.php ENDPATH**/ ?>