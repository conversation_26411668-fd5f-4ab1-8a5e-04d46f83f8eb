@extends('layouts.app')

@section('title', 'Meal Tracking Dashboard')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-utensils me-2"></i>
                    Meal Tracking Dashboard - {{ $summit->title }}
                </h2>
                <div>
                    <a href="{{ route('meal-tracking.scanner') }}" class="btn btn-primary">
                        <i class="fas fa-qrcode me-2"></i>QR Scanner
                    </a>
                    <a href="{{ route('meal-tracking.reports') }}" class="btn btn-secondary">
                        <i class="fas fa-chart-bar me-2"></i>Reports
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        @foreach(['breakfast', 'lunch', 'dinner', 'snack'] as $mealType)
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: {{ $mealType === 'breakfast' ? '#e6f4ea' : ($mealType === 'lunch' ? '#fff7e6' : ($mealType === 'dinner' ? '#e6f0ff' : '#f0e6ff')) }};">
                                <i class="fas fa-{{ $mealType === 'breakfast' ? 'coffee' : ($mealType === 'lunch' ? 'hamburger' : ($mealType === 'dinner' ? 'utensils' : 'cookie-bite')) }} fa-2x" 
                                   style="color: {{ $mealType === 'breakfast' ? '#28a745' : ($mealType === 'lunch' ? '#ffc107' : ($mealType === 'dinner' ? '#007bff' : '#6f42c1')) }};"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">{{ ucfirst($mealType) }}</h6>
                            <h3 class="mb-0">{{ $stats[$mealType]['today_tracked'] ?? 0 }}</h3>
                            <small class="text-muted">Today</small>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="row text-center">
                            <div class="col-6">
                                <small class="text-muted">Total</small><br>
                                <strong>{{ $stats[$mealType]['total_tracked'] ?? 0 }}</strong>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Unique Users</small><br>
                                <strong>{{ $stats[$mealType]['unique_users'] ?? 0 }}</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Today's Activity -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-day me-2"></i>
                        Today's Meal Activity
                    </h5>
                </div>
                <div class="card-body">
                    @if(collect($dailyReport)->sum('count') > 0)
                        <div class="row">
                            @foreach($dailyReport as $mealType => $data)
                                @if($data['count'] > 0)
                                <div class="col-lg-6 mb-4">
                                    <h6 class="text-capitalize">
                                        <i class="fas fa-{{ $mealType === 'breakfast' ? 'coffee' : ($mealType === 'lunch' ? 'hamburger' : ($mealType === 'dinner' ? 'utensils' : 'cookie-bite')) }} me-2"></i>
                                        {{ $mealType }} ({{ $data['count'] }})
                                    </h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Time</th>
                                                    <th>User</th>
                                                    <th>Activity</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($data['trackings']->take(5) as $tracking)
                                                <tr>
                                                    <td>{{ $tracking->scanned_at->format('H:i') }}</td>
                                                    <td>{{ $tracking->user->full_name }}</td>
                                                    <td>{{ $tracking->summitActivity->title ?? 'N/A' }}</td>
                                                </tr>
                                                @endforeach
                                                @if($data['trackings']->count() > 5)
                                                <tr>
                                                    <td colspan="3" class="text-center text-muted">
                                                        <small>... and {{ $data['trackings']->count() - 5 }} more</small>
                                                    </td>
                                                </tr>
                                                @endif
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                @endif
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No meal tracking activity today yet.</p>
                            <a href="{{ route('meal-tracking.scanner') }}" class="btn btn-primary">
                                <i class="fas fa-qrcode me-2"></i>Start Scanning
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .card {
        transition: transform 0.2s;
    }
    
    .card:hover {
        transform: translateY(-2px);
    }
    
    .table-sm th {
        border-top: none;
        font-size: 0.8rem;
        color: #6c757d;
    }
    
    .table-sm td {
        font-size: 0.85rem;
    }
</style>
@endpush
