<?php $__env->startSection('title', 'Payment Status'); ?>

<?php $__env->startSection('content'); ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">Payment Status</h2>
                <p class="text-muted">Payment Reference: <code><?php echo e($payment->payment_reference); ?></code></p>
            </div>
            <div>
                <a href="<?php echo e(route('payments.index')); ?>" class="btn btn-outline-primary">
                    <i class="fas fa-list me-2"></i>All Payments
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Payment Status Card -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Payment Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Payment Reference:</strong></td>
                                <td><code><?php echo e($payment->payment_reference); ?></code></td>
                            </tr>
                            <tr>
                                <td><strong>Amount:</strong></td>
                                <td><strong>TZS <?php echo e(number_format($payment->amount, 0)); ?></strong></td>
                            </tr>
                            <tr>
                                <td><strong>Payment Method:</strong></td>
                                <td><?php echo e(ucfirst(str_replace('_', ' ', $payment->payment_method))); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    <?php switch($payment->status):
                                        case ('pending'): ?>
                                            <span class="badge bg-warning">
                                                <i class="fas fa-clock me-1"></i>Pending
                                            </span>
                                            <?php break; ?>
                                        <?php case ('processing'): ?>
                                            <span class="badge bg-info">
                                                <i class="fas fa-spinner fa-spin me-1"></i>Processing
                                            </span>
                                            <?php break; ?>
                                        <?php case ('completed'): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check-circle me-1"></i>Completed
                                            </span>
                                            <?php break; ?>
                                        <?php case ('failed'): ?>
                                            <span class="badge bg-danger">
                                                <i class="fas fa-times-circle me-1"></i>Failed
                                            </span>
                                            <?php break; ?>
                                        <?php case ('cancelled'): ?>
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-ban me-1"></i>Cancelled
                                            </span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="badge bg-secondary"><?php echo e(ucfirst($payment->status)); ?></span>
                                    <?php endswitch; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Created:</strong></td>
                                <td><?php echo e($payment->created_at->format('M j, Y g:i A')); ?></td>
                            </tr>
                            <?php if($payment->paid_at): ?>
                            <tr>
                                <td><strong>Paid At:</strong></td>
                                <td><?php echo e($payment->paid_at->format('M j, Y g:i A')); ?></td>
                            </tr>
                            <?php endif; ?>
                            <?php if($payment->transaction_id): ?>
                            <tr>
                                <td><strong>Transaction ID:</strong></td>
                                <td><code><?php echo e($payment->transaction_id); ?></code></td>
                            </tr>
                            <?php endif; ?>
                            <?php if($payment->receipt_number): ?>
                            <tr>
                                <td><strong>Receipt Number:</strong></td>
                                <td><code><?php echo e($payment->receipt_number); ?></code></td>
                            </tr>
                            <?php endif; ?>
                        </table>
                    </div>
                </div>

                <!-- Status-specific messages -->
                <?php if($payment->status === 'pending'): ?>
                <div class="alert alert-warning">
                    <h6><i class="fas fa-clock me-2"></i>Payment Pending</h6>
                    <p class="mb-2">Your payment is being processed. Please check your mobile phone for a USSD prompt to complete the payment.</p>
                    <div class="d-flex gap-2">
                        <button onclick="checkPaymentStatus()" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-sync me-1"></i>Check Status
                        </button>
                        <a href="<?php echo e(route('payments.status', $payment)); ?>" class="btn btn-sm btn-outline-info">
                            <i class="fas fa-refresh me-1"></i>Refresh Page
                        </a>
                    </div>
                </div>
                <?php elseif($payment->status === 'processing'): ?>
                <div class="alert alert-info">
                    <h6><i class="fas fa-spinner fa-spin me-2"></i>Payment Processing</h6>
                    <p class="mb-2">Your payment is currently being processed. This usually takes a few minutes.</p>
                    <button onclick="checkPaymentStatus()" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-sync me-1"></i>Check Status
                    </button>
                </div>
                <?php elseif($payment->status === 'completed'): ?>
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle me-2"></i>Payment Successful!</h6>
                    <p class="mb-2">Your payment has been successfully processed. Thank you!</p>
                    <?php if($payment->summitRegistration->payment_complete): ?>
                    <a href="<?php echo e(route('id-cards.index')); ?>" class="btn btn-sm btn-success">
                        <i class="fas fa-id-card me-1"></i>Get Your ID Card
                    </a>
                    <?php endif; ?>
                </div>
                <?php elseif($payment->status === 'failed'): ?>
                <div class="alert alert-danger">
                    <h6><i class="fas fa-times-circle me-2"></i>Payment Failed</h6>
                    <p class="mb-2">Unfortunately, your payment could not be processed. Please try again or contact support.</p>
                    <a href="<?php echo e(route('payments.create', $payment->summitRegistration)); ?>" class="btn btn-sm btn-danger">
                        <i class="fas fa-retry me-1"></i>Try Again
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Summit Registration Details -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-mountain me-2"></i>Summit Registration</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Summit:</strong></td>
                                <td><?php echo e($payment->summitRegistration->summit->title); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Year:</strong></td>
                                <td><?php echo e($payment->summitRegistration->summit->year); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Registration Number:</strong></td>
                                <td><code><?php echo e($payment->summitRegistration->registration_number); ?></code></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Total Fee:</strong></td>
                                <td>TZS <?php echo e(number_format($payment->summitRegistration->total_amount, 0)); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Amount Paid:</strong></td>
                                <td class="text-success">TZS <?php echo e(number_format($payment->summitRegistration->paid_amount, 0)); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Balance:</strong></td>
                                <td>
                                    <?php if($payment->summitRegistration->balance > 0): ?>
                                        <span class="text-danger">TZS <?php echo e(number_format($payment->summitRegistration->balance, 0)); ?></span>
                                    <?php else: ?>
                                        <span class="text-success">TZS 0 (Fully Paid)</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions Sidebar -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if($payment->status === 'completed'): ?>
                        <a href="<?php echo e(route('registrations.show', $payment->summitRegistration)); ?>" class="btn btn-primary">
                            <i class="fas fa-eye me-2"></i>View Registration
                        </a>
                        <?php if($payment->summitRegistration->payment_complete): ?>
                            <a href="<?php echo e(route('id-cards.index')); ?>" class="btn btn-success">
                                <i class="fas fa-id-card me-2"></i>Get ID Card
                            </a>
                        <?php endif; ?>
                    <?php elseif(in_array($payment->status, ['failed', 'cancelled'])): ?>
                        <a href="<?php echo e(route('payments.create', $payment->summitRegistration)); ?>" class="btn btn-danger">
                            <i class="fas fa-retry me-2"></i>Try Payment Again
                        </a>
                    <?php endif; ?>
                    
                    <a href="<?php echo e(route('payments.index')); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>All Payments
                    </a>
                    
                    <a href="<?php echo e(route('dashboard')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>

        <!-- Payment Instructions -->
        <?php if(in_array($payment->status, ['pending', 'processing'])): ?>
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>What's Next?</h6>
            </div>
            <div class="card-body">
                <ol class="small mb-0">
                    <li>Check your mobile phone for a USSD prompt</li>
                    <li>Enter your mobile money PIN when prompted</li>
                    <li>Wait for confirmation SMS</li>
                    <li>Refresh this page to see updated status</li>
                </ol>
            </div>
        </div>
        <?php endif; ?>

        <!-- Support -->
        <div class="card mt-3">
            <div class="card-body">
                <h6><i class="fas fa-headset me-2"></i>Need Help?</h6>
                <p class="small text-muted mb-2">
                    If you're experiencing issues with your payment, please contact our support team.
                </p>
                <a href="<?php echo e(route('help')); ?>" class="btn btn-sm btn-outline-info">
                    <i class="fas fa-question-circle me-1"></i>Get Help
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function checkPaymentStatus() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Checking...';
    
    <?php if(in_array($payment->payment_method, ['azampay_mobile', 'azampay_bank'])): ?>
    fetch('<?php echo e(route("payments.azampay.check", $payment)); ?>')
    <?php else: ?>
    fetch('<?php echo e(route("payments.selcom.status", $payment)); ?>')
    <?php endif; ?>
        .then(response => response.json())
        .then(data => {
            if (data.status_changed) {
                location.reload();
            } else {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
}

// Auto-refresh for pending/processing payments
<?php if(in_array($payment->status, ['pending', 'processing'])): ?>
setInterval(function() {
    <?php if(in_array($payment->payment_method, ['azampay_mobile', 'azampay_bank'])): ?>
    fetch('<?php echo e(route("payments.azampay.check", $payment)); ?>')
    <?php else: ?>
    fetch('<?php echo e(route("payments.selcom.status", $payment)); ?>')
    <?php endif; ?>
        .then(response => response.json())
        .then(data => {
            if (data.status_changed) {
                location.reload();
            }
        })
        .catch(error => console.error('Auto-refresh error:', error));
}, 30000); // Check every 30 seconds
<?php endif; ?>
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\summit-management-system\resources\views/payments/show.blade.php ENDPATH**/ ?>