<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Summit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SummitController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Summit::withCount(['registrations', 'activities', 'ministers']);

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('theme', 'like', "%{$search}%")
                  ->orWhere('venue', 'like', "%{$search}%")
                  ->orWhere('year', 'like', "%{$search}%");
            });
        }

        if ($request->filled('year')) {
            $query->where('year', $request->year);
        }

        $summits = $query->orderBy('year', 'desc')->paginate(15);

        // Get years for filter
        $years = Summit::distinct()->orderBy('year', 'desc')->pluck('year');

        return view('admin.summits.index', compact('summits', 'years'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.summits.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'theme' => 'required|string|max:255',
            'description' => 'required|string',
            'year' => 'required|integer|min:2020|max:2050',
            'start_date' => 'required|date|after:today',
            'end_date' => 'required|date|after:start_date',
            'venue' => 'required|string|max:255',
            'venue_address' => 'required|string',
            'registration_fee' => 'required|numeric|min:0',
            'registration_deadline' => 'required|date|before:start_date',
            'poster_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
            'is_active' => 'boolean',
            'is_current' => 'boolean',
        ]);

        // Handle poster image upload
        if ($request->hasFile('poster_image')) {
            $validated['poster_image'] = $request->file('poster_image')->store('summit-posters', 'public');
        }

        // Handle gallery images upload
        if ($request->hasFile('gallery_images')) {
            $galleryImages = [];
            foreach ($request->file('gallery_images') as $image) {
                $galleryImages[] = $image->store('summit-gallery', 'public');
            }
            $validated['gallery_images'] = $galleryImages;
        }

        $validated['is_active'] = $request->boolean('is_active', true);
        $validated['is_current'] = $request->boolean('is_current', false);

        // If this summit is set as current, unset all other current summits
        if ($validated['is_current']) {
            Summit::where('is_current', true)->update(['is_current' => false]);
        }

        Summit::create($validated);

        return redirect()->route('admin.summits.index')
            ->with('success', 'Summit created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Summit $summit)
    {
        $summit->load(['registrations.user', 'idCards']);

        return view('admin.summits.show', compact('summit'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Summit $summit)
    {
        return view('admin.summits.edit', compact('summit'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Summit $summit)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'theme' => 'required|string|max:255',
            'description' => 'required|string',
            'year' => 'required|integer|min:2020|max:2050',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'venue' => 'required|string|max:255',
            'venue_address' => 'required|string',
            'registration_fee' => 'required|numeric|min:0',
            'registration_deadline' => 'required|date|before:start_date',
            'poster_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
            'is_active' => 'boolean',
            'is_current' => 'boolean',
        ]);

        // Handle poster image upload
        if ($request->hasFile('poster_image')) {
            // Delete old poster
            if ($summit->poster_image) {
                Storage::disk('public')->delete($summit->poster_image);
            }
            $validated['poster_image'] = $request->file('poster_image')->store('summit-posters', 'public');
        }

        // Handle gallery images upload
        if ($request->hasFile('gallery_images')) {
            // Delete old gallery images
            if ($summit->gallery_images) {
                foreach ($summit->gallery_images as $image) {
                    Storage::disk('public')->delete($image);
                }
            }
            $galleryImages = [];
            foreach ($request->file('gallery_images') as $image) {
                $galleryImages[] = $image->store('summit-gallery', 'public');
            }
            $validated['gallery_images'] = $galleryImages;
        }

        $validated['is_active'] = $request->boolean('is_active', true);
        $validated['is_current'] = $request->boolean('is_current', false);

        // If this summit is set as current, unset all other current summits
        if ($validated['is_current'] && !$summit->is_current) {
            Summit::where('is_current', true)->update(['is_current' => false]);
        }

        $summit->update($validated);

        return redirect()->route('admin.summits.index')
            ->with('success', 'Summit updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Summit $summit)
    {
        // Delete associated images
        if ($summit->poster_image) {
            Storage::disk('public')->delete($summit->poster_image);
        }
        if ($summit->gallery_images) {
            foreach ($summit->gallery_images as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $summit->delete();

        return redirect()->route('admin.summits.index')
            ->with('success', 'Summit deleted successfully.');
    }

    /**
     * Set a summit as current.
     */
    public function setCurrent(Summit $summit)
    {
        Summit::where('is_current', true)->update(['is_current' => false]);
        $summit->update(['is_current' => true]);

        return redirect()->route('admin.summits.index')
            ->with('success', 'Summit set as current successfully.');
    }
}
