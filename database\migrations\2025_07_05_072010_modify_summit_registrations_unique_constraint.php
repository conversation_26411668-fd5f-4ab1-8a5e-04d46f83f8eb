<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the existing unique constraint
        Schema::table('summit_registrations', function (Blueprint $table) {
            $table->dropUnique(['user_id', 'summit_id']);
        });

        // Create a partial unique constraint that only applies to non-cancelled registrations
        // This allows users to have multiple registrations for the same summit as long as only one is active
        DB::statement('CREATE UNIQUE INDEX summit_registrations_user_summit_active_unique
                      ON summit_registrations (user_id, summit_id)
                      WHERE status IN (\'pending\', \'confirmed\')');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the partial unique constraint
        DB::statement('DROP INDEX IF EXISTS summit_registrations_user_summit_active_unique');

        // Restore the original unique constraint
        Schema::table('summit_registrations', function (Blueprint $table) {
            $table->unique(['user_id', 'summit_id']);
        });
    }
};
