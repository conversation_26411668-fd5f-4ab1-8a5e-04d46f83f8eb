# 🧪 TUCASA Summit Management - Testing Guide

## 🚀 Quick Setup for Testing with ngrok

### Prerequisites
- Laravel app running locally
- ngrok installed and configured
- Internet connection

### Step-by-Step Setup

#### 1. **Install ngrok** (if not already installed)
- Go to [ngrok.com](https://ngrok.com) and sign up
- Download ngrok for Windows
- Extract to a folder (e.g., `C:\ngrok\`)
- Get your auth token from ngrok dashboard
- Run: `ngrok config add-authtoken YOUR_AUTH_TOKEN`

#### 2. **Start the Application**
```bash
# Option A: Use the provided batch file
setup-ngrok.bat

# Option B: Manual setup
# Terminal 1: Start Laravel
php artisan serve --host=127.0.0.1 --port=8000

# Terminal 2: Start ngrok
ngrok http 8000
```

#### 3. **Configure the ngrok URL**
After ngrok starts, you'll see a URL like: `https://abc123.ngrok-free.app`

```bash
# Option A: Use the helper script
update-ngrok-url.bat

# Option B: Manual update
# 1. Copy the ngrok URL
# 2. Update .env: APP_URL=https://your-ngrok-url.ngrok-free.app
# 3. Run: php artisan config:cache
```

## 🧪 **Testing Scenarios**

### **Scenario 1: New User Registration & Summit Registration**

**Test Flow:**
1. **Landing Page** → Visit ngrok URL
2. **Register Account** → Click "Register" 
3. **Complete Profile** → Fill in all required fields
4. **Login** → Use registered credentials
5. **Register for Summit** → Complete summit registration
6. **Make Payment** → Test payment flow (use test numbers)
7. **Generate ID Card** → After successful payment
8. **Download ID Card** → Test PDF download with colors

**Test Data for Registration:**
```
First Name: Test
Last Name: User
Email: <EMAIL>
Phone: +255 123 456 789
University: Select any
Branch: Select any
Student ID: TEST/2024/001
Course: Computer Science
Year of Study: 2
```

### **Scenario 2: Payment Testing**

**Test Phone Numbers for Selcom:**
```
Vodacom: ********* or *********
Tigo: ********* or *********
Airtel: ********* or *********
Halopesa: *********
```

**Payment Flow:**
1. Register for summit
2. Go to payment page
3. Enter test amount (minimum 1000 TZS)
4. Select MNO provider
5. Enter test phone number
6. Complete payment simulation

### **Scenario 3: ID Card & QR Code Testing**

**After Payment Completion:**
1. Navigate to ID Cards section
2. Generate ID card
3. Download PDF and verify:
   - ✅ Colors are preserved (not white)
   - ✅ Text is readable
   - ✅ QR code is large enough to scan
4. Test QR code scanning with phone camera
5. Verify QR code contains correct user data

### **Scenario 4: Meal Tracking Testing**

**For Admin/Moderator Users:**
1. Login as admin/moderator
2. Go to `/meal-tracking/scanner`
3. Test QR code scanning:
   - Scan user's ID card QR code
   - Select meal type (breakfast/lunch/dinner/snack)
   - Verify meal tracking success
4. Check dashboard at `/meal-tracking/dashboard`
5. View reports at `/meal-tracking/reports`

## 👥 **Test User Accounts**

### **Admin Account**
```
Email: <EMAIL>
Password: password123
Role: Admin (full access)
```

### **Treasurer Account**
```
Email: <EMAIL>
Password: password123
Role: Treasurer (payment management)
```

## 📱 **Mobile Testing**

### **QR Code Scanning Apps**
- **Android**: QR Code Reader, Google Lens
- **iOS**: Built-in Camera app, QR Code Reader

### **Mobile Browser Testing**
- Test responsive design on mobile browsers
- Verify QR scanner works on mobile devices
- Test payment flow on mobile

## 🔧 **Troubleshooting**

### **Common Issues & Solutions**

#### **ngrok URL not working**
```bash
# Check if Laravel is running
php artisan serve --host=127.0.0.1 --port=8000

# Check if ngrok is running
ngrok http 8000

# Update APP_URL in .env and clear cache
php artisan config:cache
```

#### **Payment not working**
- Verify Selcom credentials in `.env`
- Check if test mode is enabled
- Use valid test phone numbers

#### **QR Code not scanning**
- Ensure QR code is large enough
- Check lighting conditions
- Try different QR scanner apps

#### **ID Card PDF colors missing**
- Clear browser cache
- Try different browser
- Check if PDF viewer supports colors

## 📊 **What to Test & Verify**

### ✅ **Core Functionality**
- [ ] User registration and login
- [ ] Profile completion
- [ ] Summit registration
- [ ] Payment processing
- [ ] ID card generation
- [ ] PDF download with colors
- [ ] QR code scanning

### ✅ **UI/UX Testing**
- [ ] Responsive design on mobile
- [ ] Navigation flow
- [ ] Error handling
- [ ] Success messages
- [ ] Form validation

### ✅ **Security Testing**
- [ ] Authentication required for protected pages
- [ ] Role-based access control
- [ ] Payment security
- [ ] Data validation

### ✅ **Performance Testing**
- [ ] Page load times
- [ ] Image loading
- [ ] PDF generation speed
- [ ] QR code generation

## 📞 **Support During Testing**

If testers encounter issues:
1. Check the Laravel logs: `storage/logs/laravel.log`
2. Check ngrok status and URL
3. Verify database connections
4. Clear caches: `php artisan cache:clear`

## 🎯 **Success Criteria**

Testing is successful when users can:
1. ✅ Access the landing page via ngrok URL
2. ✅ Register and complete their profile
3. ✅ Register for a summit
4. ✅ Complete payment (simulation)
5. ✅ Generate and download ID card with proper colors
6. ✅ Scan QR codes for meal tracking
7. ✅ Navigate the system without errors

---

**Happy Testing! 🚀**
