<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix <PERSON>'s payment status for testing ID card functionality
        DB::table('summit_registrations')
            ->where('id', 6) // <PERSON>'s active registration
            ->update([
                'status' => 'confirmed',
                'paid_amount' => 83700.00,
                'balance' => 0,
                'payment_complete' => true,
                'updated_at' => now(),
            ]);

        // Create a completed payment record
        DB::table('payments')->insert([
            'user_id' => 5, // <PERSON>'s user ID
            'summit_registration_id' => 6,
            'payment_reference' => 'TEST-KEVIN-' . now()->format('YmdHis'),
            'transaction_id' => 'TXN-KEVIN-' . now()->format('YmdHis'),
            'amount' => 83700.00,
            'payment_method' => 'cash',
            'status' => 'completed',
            'gateway_response' => 'Payment completed by admin for ID card testing',
            'paid_at' => now(),
            'receipt_number' => 'RCP-KEVIN-' . now()->format('YmdHis'),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert Kevin's payment status
        DB::table('summit_registrations')
            ->where('id', 6)
            ->update([
                'status' => 'pending',
                'paid_amount' => 0,
                'balance' => 83700.00,
                'payment_complete' => false,
                'updated_at' => now(),
            ]);

        // Remove the test payment record
        DB::table('payments')
            ->where('summit_registration_id', 6)
            ->where('payment_method', 'cash')
            ->where('gateway_response', 'Payment completed by admin for ID card testing')
            ->delete();
    }
};
