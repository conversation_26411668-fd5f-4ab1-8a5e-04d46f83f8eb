<?php

namespace App\Services;

use App\Models\IdCard;
use App\Models\User;
use App\Models\Summit;
use App\Models\SummitRegistration;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\Encoding\Encoding;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;

class IdCardService
{
    /**
     * Generate or retrieve ID card for user and summit
     */
    public function generateIdCard(User $user, Summit $summit): ?IdCard
    {
        // Check if user has completed payment for this summit
        $registration = $user->summitRegistrations()
            ->where('summit_id', $summit->id)
            ->where('payment_complete', true)
            ->first();

        if (!$registration) {
            return null;
        }

        // Check if ID card already exists
        $existingCard = $user->idCards()
            ->where('summit_id', $summit->id)
            ->where('is_active', true)
            ->first();

        if ($existingCard) {
            return $existingCard;
        }

        // Generate new ID card
        $cardNumber = $this->generateCardNumber($user, $summit);
        $qrData = $this->generateQrData($user, $summit, $cardNumber);
        $qrCodePath = $this->generateQrCode($qrData, $cardNumber);

        $idCard = IdCard::create([
            'user_id' => $user->id,
            'summit_id' => $summit->id,
            'card_number' => $cardNumber,
            'qr_code' => $qrData,
            'qr_code_path' => $qrCodePath,
            'is_active' => true,
            'issued_at' => now(),
            'expires_at' => max(
                now()->addMonths(6),
                $summit->end_date->copy()->addMonths(6)
            ), // Card expires 6 months from now or 6 months after summit, whichever is later
        ]);

        return $idCard;
    }

    /**
     * Generate unique card number
     */
    private function generateCardNumber(User $user, Summit $summit): string
    {
        $prefix = 'TUCASA';
        $year = $summit->year;
        $userCode = str_pad($user->id, 4, '0', STR_PAD_LEFT);
        $random = strtoupper(Str::random(4));
        
        return "{$prefix}-{$year}-{$userCode}-{$random}";
    }

    /**
     * Generate QR code data
     */
    private function generateQrData(User $user, Summit $summit, string $cardNumber): string
    {
        $data = [
            'card_number' => $cardNumber,
            'user_id' => $user->id,
            'summit_id' => $summit->id,
            'name' => $user->full_name,
            'email' => $user->email,
            'branch' => $user->branch->name ?? 'N/A',
            'conference' => $user->branch->zone->conference->name ?? 'N/A',
            'issued_at' => now()->toISOString(),
            'verification_url' => route('id-card.verify', ['card' => $cardNumber])
        ];

        return json_encode($data);
    }

    /**
     * Generate QR code image (public method for external use)
     */
    public function generateQrCodeFromData(string $data, string $cardNumber): string
    {
        return $this->generateQrCode($data, $cardNumber);
    }

    /**
     * Generate QR code image
     */
    private function generateQrCode(string $data, string $cardNumber): string
    {
        try {
            // Create QR code with optimal settings for PDF scanning
            $qrCode = new QrCode(
                data: $data,
                encoding: new Encoding('UTF-8'),
                errorCorrectionLevel: ErrorCorrectionLevel::High, // Higher error correction for better scanning
                size: 1600, // Very large size for excellent PDF quality and scanning
                margin: 100 // Large margin for better scanning
            );

            $writer = new PngWriter();
            $result = $writer->write($qrCode);

            $filename = "qr-codes/{$cardNumber}.png";
            Storage::disk('public')->put($filename, $result->getString());

            return $filename;
        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error('QR Code generation failed: ' . $e->getMessage());

            // Fallback: create a simple text file with QR data
            $filename = "qr-codes/{$cardNumber}.txt";
            Storage::disk('public')->put($filename, $data);

            return $filename;
        }
    }

    /**
     * Generate PDF ID card
     */
    public function generatePdfCard(IdCard $idCard): string
    {
        $user = $idCard->user;
        $summit = $idCard->summit;

        // Convert images to base64 for better PDF compatibility
        $qrCodeBase64 = null;
        $profileImageBase64 = null;

        // Convert QR code to base64
        if ($idCard->qr_code_path && Storage::disk('public')->exists($idCard->qr_code_path)) {
            $qrCodeContent = Storage::disk('public')->get($idCard->qr_code_path);
            $qrCodeBase64 = 'data:image/png;base64,' . base64_encode($qrCodeContent);
        }

        // Convert profile image to base64
        if ($user->profile_image && Storage::disk('public')->exists($user->profile_image)) {
            $profileImageContent = Storage::disk('public')->get($user->profile_image);
            $imageInfo = getimagesizefromstring($profileImageContent);
            $mimeType = $imageInfo['mime'] ?? 'image/jpeg';
            $profileImageBase64 = 'data:' . $mimeType . ';base64,' . base64_encode($profileImageContent);
        }

        // Prepare data for PDF
        $data = [
            'idCard' => $idCard,
            'user' => $user,
            'summit' => $summit,
            'qrCodeBase64' => $qrCodeBase64,
            'profileImageBase64' => $profileImageBase64,
        ];

        // Generate HTML content
        $html = view('pdf.id-card', $data)->render();

        try {
            // Create PDF using DomPDF with optimized settings for QR codes
            $dompdf = new \Dompdf\Dompdf([
                'isRemoteEnabled' => true,
                'isHtml5ParserEnabled' => true,
                'isFontSubsettingEnabled' => true,
                'defaultFont' => 'Arial',
                'dpi' => 300, // Higher DPI for better QR code quality
                'isPhpEnabled' => true,
                'chroot' => storage_path('app/public'),
            ]);

            $dompdf->loadHtml($html);

            // Set paper size to A4 portrait for proper ID card printing
            $dompdf->setPaper('A4', 'portrait');

            // Render the PDF
            $dompdf->render();

            // Save PDF to storage
            $filename = "id-cards/{$idCard->card_number}.pdf";
            Storage::disk('public')->put($filename, $dompdf->output());

            return $filename;

        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error('PDF generation failed: ' . $e->getMessage());

            // Fallback: return HTML filename if PDF generation fails
            $filename = "id-cards/{$idCard->card_number}.html";
            Storage::disk('public')->put($filename, $html);
            return $filename;
        }
    }

    /**
     * Verify ID card by card number
     */
    public function verifyCard(string $cardNumber): ?IdCard
    {
        return IdCard::where('card_number', $cardNumber)
            ->where('is_active', true)
            ->where('expires_at', '>', now())
            ->with(['user', 'summit'])
            ->first();
    }

    /**
     * Check if user can generate ID card for summit
     */
    public function canGenerateCard(User $user, Summit $summit): bool
    {
        $registration = $user->summitRegistrations()
            ->where('summit_id', $summit->id)
            ->where('payment_complete', true)
            ->first();

        return $registration !== null;
    }

    /**
     * Get user's active ID cards
     */
    public function getUserActiveCards(User $user): \Illuminate\Database\Eloquent\Collection
    {
        return $user->idCards()
            ->where('is_active', true)
            ->where('expires_at', '>', now())
            ->with('summit')
            ->orderBy('issued_at', 'desc')
            ->get();
    }

    /**
     * Deactivate ID card
     */
    public function deactivateCard(IdCard $idCard): bool
    {
        return $idCard->update(['is_active' => false]);
    }

    /**
     * Get card statistics for admin
     */
    public function getCardStatistics(): array
    {
        return [
            'total_cards' => IdCard::count(),
            'active_cards' => IdCard::where('is_active', true)->count(),
            'expired_cards' => IdCard::where('expires_at', '<', now())->count(),
            'cards_this_month' => IdCard::whereMonth('issued_at', now()->month)
                ->whereYear('issued_at', now()->year)
                ->count(),
        ];
    }
}
