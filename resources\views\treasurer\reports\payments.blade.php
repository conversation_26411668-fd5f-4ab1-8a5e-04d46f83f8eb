@extends('layouts.user')

@section('title', 'Payment Reports')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">💳 Payment Reports</h2>
                    <p class="text-muted mb-0">Detailed payment analysis and transaction reports</p>
                </div>
                <div>
                    <a href="{{ route('treasurer.dashboard') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back
                    </a>
                    <a href="{{ route('treasurer.export.payment-report', request()->query()) }}" class="btn btn-success">
                        <i class="fas fa-download me-2"></i>Export PDF
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('treasurer.payment-reports') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="date_from" class="form-label">From Date</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" 
                                       value="{{ $dateFrom ?? now()->startOfMonth()->format('Y-m-d') }}">
                            </div>
                            <div class="col-md-3">
                                <label for="date_to" class="form-label">To Date</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" 
                                       value="{{ $dateTo ?? now()->format('Y-m-d') }}">
                            </div>
                            <div class="col-md-2">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="all" {{ ($status ?? 'all') === 'all' ? 'selected' : '' }}>All</option>
                                    <option value="completed" {{ ($status ?? '') === 'completed' ? 'selected' : '' }}>Completed</option>
                                    <option value="pending" {{ ($status ?? '') === 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="failed" {{ ($status ?? '') === 'failed' ? 'selected' : '' }}>Failed</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="branch" class="form-label">Branch</label>
                                <select class="form-select" id="branch" name="branch">
                                    <option value="all">All Branches</option>
                                    @if(isset($branches))
                                        @foreach($branches as $branchOption)
                                            <option value="{{ $branchOption->id }}" {{ ($branch ?? 'all') == $branchOption->id ? 'selected' : '' }}>
                                                {{ $branchOption->name }}
                                            </option>
                                        @endforeach
                                    @endif
                                </select>
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    @if(isset($summary))
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h6>Total Amount</h6>
                    <h3>{{ number_format($summary['total_amount'] ?? 0, 0) }} TZS</h3>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h6>Total Transactions</h6>
                    <h3>{{ $summary['total_count'] ?? 0 }}</h3>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <h6>Completed</h6>
                    <h3>{{ $summary['completed_count'] ?? 0 }}</h3>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <h6>Failed</h6>
                    <h3>{{ $summary['failed_count'] ?? 0 }}</h3>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Payments Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Payment Transactions</h5>
                </div>
                <div class="card-body">
                    @if(isset($payments) && $payments->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>User</th>
                                        <th>Branch</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Method</th>
                                        <th>Reference</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($payments as $payment)
                                    <tr>
                                        <td>{{ $payment->created_at->format('M j, Y H:i') }}</td>
                                        <td>
                                            <strong>{{ $payment->user->full_name }}</strong><br>
                                            <small class="text-muted">{{ $payment->user->email }}</small>
                                        </td>
                                        <td>{{ $payment->user->branch->name ?? 'N/A' }}</td>
                                        <td><strong>{{ number_format($payment->amount, 0) }} TZS</strong></td>
                                        <td>
                                            @if($payment->status === 'completed')
                                                <span class="badge bg-success">Completed</span>
                                            @elseif($payment->status === 'pending')
                                                <span class="badge bg-warning">Pending</span>
                                            @else
                                                <span class="badge bg-danger">Failed</span>
                                            @endif
                                        </td>
                                        <td>{{ ucfirst($payment->payment_method ?? 'N/A') }}</td>
                                        <td><small>{{ $payment->reference_number ?? 'N/A' }}</small></td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        @if(method_exists($payments, 'links'))
                            <div class="mt-3">
                                {{ $payments->appends(request()->query())->links() }}
                            </div>
                        @endif
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No payments found for the selected criteria.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
