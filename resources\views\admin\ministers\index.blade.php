@extends('layouts.admin')

@section('title', 'Ministers Management')
@section('page-title', 'Ministers Management')

@push('styles')
<style>
.admin-page-container {
    padding: 20px;
    max-width: 100%;
}

.page-header {
    background-color: var(--md-surface, white);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-title {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: #1a1a1a;
}

.page-subtitle {
    font-size: 16px;
    color: #666;
    margin: 0;
}

.search-card {
    background-color: var(--md-surface, white);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #1a1a1a;
}

.search-form {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 16px;
    align-items: end;
}

.search-inputs {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

.form-input, .form-select {
    padding: 12px 16px;
    border: 1.5px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.2s ease;
    background: white;
}

.form-input:focus, .form-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-actions {
    display: flex;
    gap: 8px;
}

.btn-primary {
    background-color: #667eea;
    border: none;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary:hover {
    background-color: #5a67d8;
    transform: translateY(-1px);
    color: white;
    text-decoration: none;
}

.btn-outline-secondary {
    background-color: transparent;
    border: 1.5px solid #e5e7eb;
    color: #6b7280;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-outline-secondary:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
    color: #374151;
    text-decoration: none;
}

.data-table-card {
    background-color: var(--md-surface, white);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-header {
    background-color: #f8fafc;
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.table-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #1a1a1a;
}

.table-responsive {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 900px;
}

.data-table th {
    background-color: #f8fafc;
    padding: 16px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
    font-size: 14px;
}

.data-table td {
    padding: 16px;
    border-bottom: 1px solid #f1f5f9;
    color: #1f2937;
}

.data-table tr:hover {
    background-color: #f8fafc;
}

.minister-image {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e5e7eb;
}

.minister-image-placeholder {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
    font-size: 20px;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.status-active {
    background-color: #d1fae5;
    color: #065f46;
}

.status-inactive {
    background-color: #f3f4f6;
    color: #6b7280;
}

.action-buttons {
    display: flex;
    gap: 4px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 6px;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.empty-state-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #374151;
}

.empty-state-text {
    font-size: 16px;
    margin: 0 0 20px 0;
}

@media (max-width: 768px) {
    .admin-page-container {
        padding: 16px;
    }

    .page-header {
        padding: 20px;
    }

    .page-header > div {
        flex-direction: column;
        gap: 16px;
        align-items: stretch !important;
    }

    .search-form {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .search-inputs {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .search-actions {
        justify-content: stretch;
    }

    .search-actions .btn-primary,
    .search-actions .btn-outline-secondary {
        flex: 1;
        justify-content: center;
    }

    .data-table-card {
        margin: 0 -16px;
        border-radius: 0;
    }

    .table-header {
        padding: 16px;
    }

    .data-table th,
    .data-table td {
        padding: 12px 8px;
        font-size: 14px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 4px;
    }

    .btn-sm {
        padding: 8px 12px;
        font-size: 12px;
    }

    .minister-image,
    .minister-image-placeholder {
        width: 40px;
        height: 40px;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 24px;
    }

    .data-table {
        min-width: 700px;
    }

    .data-table th,
    .data-table td {
        padding: 8px 6px;
        font-size: 13px;
    }
}
</style>
@endpush

@section('content')
<div class="admin-page-container">
    <!-- Page Header -->
    <div class="page-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1 class="page-title">Ministers Management</h1>
                <p class="page-subtitle">Manage ministers displayed on the landing page</p>
            </div>
            <a href="{{ route('admin.ministers.create') }}" class="btn-primary">
                <span class="material-icons">add</span>
                Add Minister
            </a>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="search-card">
        <h2 class="search-title">
            <span class="material-icons">search</span>
            Search & Filter
        </h2>
        <form method="GET" action="{{ route('admin.ministers.index') }}" class="search-form">
            <div class="search-inputs">
                <div class="form-group">
                    <label class="form-label">Search</label>
                    <input
                        type="text"
                        name="search"
                        class="form-input"
                        placeholder="Search by name, title, position, email..."
                        value="{{ request('search') }}"
                    >
                </div>
                <div class="form-group">
                    <label class="form-label">Status</label>
                    <select name="status" class="form-select">
                        <option value="">All Status</option>
                        <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>Active</option>
                        <option value="0" {{ request('status') == '0' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>
            </div>
            <div class="search-actions">
                <button type="submit" class="btn-primary">
                    <span class="material-icons">search</span>
                    Search
                </button>
                <a href="{{ route('admin.ministers.index') }}" class="btn-outline-secondary">
                    <span class="material-icons">clear</span>
                    Clear
                </a>
            </div>
        </form>
    </div>

    <!-- Ministers Table -->
    <div class="data-table-card">
        <div class="table-header">
            <h2 class="table-title">
                <span class="material-icons">people</span>
                Ministers ({{ $ministers->total() }})
            </h2>
        </div>

        @if($ministers->count() > 0)
        <div class="table-responsive">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Image</th>
                        <th>Name</th>
                        <th>Title</th>
                        <th>Position</th>
                        <th>Order</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($ministers as $minister)
                    <tr>
                        <td>
                            @if($minister->image)
                                <img src="{{ Storage::url($minister->image) }}"
                                     alt="{{ $minister->name }}"
                                     class="minister-image">
                            @else
                                <div class="minister-image-placeholder">
                                    <span class="material-icons">person</span>
                                </div>
                            @endif
                        </td>
                        <td style="font-weight: 600;">{{ $minister->name }}</td>
                        <td>{{ $minister->title }}</td>
                        <td>{{ $minister->position ?? 'N/A' }}</td>
                        <td style="text-align: center;">
                            <span class="status-badge status-active">{{ $minister->order }}</span>
                        </td>
                        <td>
                            <span class="status-badge {{ $minister->is_active ? 'status-active' : 'status-inactive' }}">
                                {{ $minister->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="{{ route('admin.ministers.show', $minister) }}"
                                   class="btn-outline-secondary btn-sm" title="View Details">
                                    <span class="material-icons" style="font-size: 16px;">visibility</span>
                                </a>
                                <a href="{{ route('admin.ministers.edit', $minister) }}"
                                   class="btn-outline-secondary btn-sm" title="Edit">
                                    <span class="material-icons" style="font-size: 16px;">edit</span>
                                </a>
                                <form action="{{ route('admin.ministers.destroy', $minister) }}"
                                      method="POST" style="display: inline;"
                                      onsubmit="return confirm('Are you sure you want to delete this minister?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn-outline-secondary btn-sm" title="Delete"
                                            style="color: #ef4444; border-color: #ef4444;">
                                        <span class="material-icons" style="font-size: 16px;">delete</span>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div style="padding: 20px; text-align: center; border-top: 1px solid #e5e7eb;">
            {{ $ministers->withQueryString()->links() }}
        </div>
        @else
        <div class="empty-state">
            <div class="empty-state-icon">
                <span class="material-icons" style="font-size: inherit;">people</span>
            </div>
            <h3 class="empty-state-title">No ministers found</h3>
            <p class="empty-state-text">
                @if(request('search'))
                    No ministers match your search criteria.
                @else
                    Start by adding your first minister.
                @endif
            </p>
            @if(!request('search'))
            <a href="{{ route('admin.ministers.create') }}" class="btn-primary">
                <span class="material-icons">add</span>
                Add Minister
            </a>
            @endif
        </div>
        @endif
    </div>
</div>
        </div>
    </div>
</div>
@endsection
