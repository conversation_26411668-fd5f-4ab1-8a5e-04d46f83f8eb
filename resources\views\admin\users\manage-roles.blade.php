@extends('layouts.admin')

@section('title', 'Manage User Roles')
@section('page-title', 'Manage User Roles')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">Manage Roles for {{ $user->full_name }}</h2>
                <p class="text-muted">Assign administrative roles and permissions</p>
            </div>
            <div>
                <a href="{{ route('admin.users.show', $user) }}" class="btn btn-outline-info me-2">
                    <i class="fas fa-eye me-2"></i>View User
                </a>
                <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Users
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Current Roles -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-shield me-2"></i>
                    Current Roles
                </h5>
            </div>
            <div class="card-body">
                @if($user->roles->count() > 0)
                    <div class="row">
                        @foreach($user->roles as $role)
                        <div class="col-md-4 mb-3">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-{{ $role->name === 'admin' ? 'crown' : ($role->name === 'treasurer' ? 'calculator' : ($role->name === 'conference_admin' ? 'building' : ($role->name === 'zone_admin' ? 'map-marked-alt' : ($role->name === 'branch_admin' ? 'code-branch' : 'user')))) }} fa-2x text-primary mb-2"></i>
                                    <h6 class="card-title">{{ ucwords(str_replace('_', ' ', $role->name)) }}</h6>
                                    @if(in_array($role->name, ['conference_admin', 'zone_admin', 'branch_admin']))
                                        <small class="text-muted">
                                            @if($role->name === 'conference_admin' && $user->adminConference)
                                                Conference: {{ $user->adminConference->name }}
                                            @elseif($role->name === 'zone_admin' && $user->adminZone)
                                                Zone: {{ $user->adminZone->name }}
                                            @elseif($role->name === 'branch_admin' && $user->adminBranch)
                                                Branch: {{ $user->adminBranch->name }}
                                            @else
                                                <span class="text-warning">No assignment</span>
                                            @endif
                                        </small>
                                    @endif
                                    <div class="mt-2">
                                        <form method="POST" action="{{ route('admin.users.remove-role') }}" style="display: inline;">
                                            @csrf
                                            <input type="hidden" name="user_id" value="{{ $user->id }}">
                                            <input type="hidden" name="role" value="{{ $role->name }}">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                    onclick="return confirm('Are you sure you want to remove this role?')">
                                                <i class="fas fa-times me-1"></i>Remove
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-user fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No roles assigned to this user.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Assign New Role -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus-circle me-2"></i>
                    Assign New Role
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.users.assign-role') }}">
                    @csrf
                    <input type="hidden" name="user_id" value="{{ $user->id }}">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role" class="form-label">Select Role <span class="text-danger">*</span></label>
                                <select name="role" id="role" class="form-select @error('role') is-invalid @enderror" required>
                                    <option value="">Choose a role...</option>
                                    @foreach($availableRoles as $role)
                                        <option value="{{ $role->name }}" {{ old('role') === $role->name ? 'selected' : '' }}>
                                            {{ ucwords(str_replace('_', ' ', $role->name)) }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('role')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Conference Admin Assignment -->
                    <div id="conference-assignment" class="row" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="admin_conference_id" class="form-label">Assign to Conference</label>
                                <select name="admin_conference_id" id="admin_conference_id" class="form-select">
                                    <option value="">Select Conference...</option>
                                    @foreach($conferences as $conference)
                                        <option value="{{ $conference->id }}" {{ old('admin_conference_id') == $conference->id ? 'selected' : '' }}>
                                            {{ $conference->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Zone Admin Assignment -->
                    <div id="zone-assignment" class="row" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="admin_zone_id" class="form-label">Assign to Zone</label>
                                <select name="admin_zone_id" id="admin_zone_id" class="form-select">
                                    <option value="">Select Zone...</option>
                                    @foreach($zones as $zone)
                                        <option value="{{ $zone->id }}" {{ old('admin_zone_id') == $zone->id ? 'selected' : '' }}>
                                            {{ $zone->name }} ({{ $zone->conference->name }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Branch Admin Assignment -->
                    <div id="branch-assignment" class="row" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="admin_branch_id" class="form-label">Assign to Branch</label>
                                <select name="admin_branch_id" id="admin_branch_id" class="form-select">
                                    <option value="">Select Branch...</option>
                                    @foreach($branches as $branch)
                                        <option value="{{ $branch->id }}" {{ old('admin_branch_id') == $branch->id ? 'selected' : '' }}>
                                            {{ $branch->name }} ({{ $branch->zone->name }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-user-plus me-2"></i>Assign Role
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const roleSelect = document.getElementById('role');
    const conferenceAssignment = document.getElementById('conference-assignment');
    const zoneAssignment = document.getElementById('zone-assignment');
    const branchAssignment = document.getElementById('branch-assignment');

    function toggleAssignments() {
        const selectedRole = roleSelect.value;
        
        // Hide all assignments first
        conferenceAssignment.style.display = 'none';
        zoneAssignment.style.display = 'none';
        branchAssignment.style.display = 'none';
        
        // Show relevant assignment based on role
        if (selectedRole === 'conference_admin') {
            conferenceAssignment.style.display = 'block';
        } else if (selectedRole === 'zone_admin') {
            zoneAssignment.style.display = 'block';
        } else if (selectedRole === 'branch_admin') {
            branchAssignment.style.display = 'block';
        }
    }

    roleSelect.addEventListener('change', toggleAssignments);
    
    // Initialize on page load
    toggleAssignments();
});
</script>
@endpush

@push('styles')
<style>
    .card {
        transition: transform 0.2s, box-shadow 0.2s;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }
</style>
@endpush
