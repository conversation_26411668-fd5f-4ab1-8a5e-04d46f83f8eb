@extends('layouts.user')

@section('title', 'Register for Summit')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">Register for Summit</h2>
                <p class="text-muted">{{ $summit->title }} ({{ $summit->year }})</p>
            </div>
            <a href="{{ route('dashboard') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    </div>
</div>

<!-- Summit Information -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-mountain me-2"></i>Summit Details</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold">{{ $summit->title }}</h6>
                        <p class="text-muted mb-2">{{ $summit->description }}</p>
                        <div class="mb-2">
                            <strong>Theme:</strong> {{ $summit->theme }}
                        </div>
                        <div class="mb-2">
                            <strong>Venue:</strong> {{ $summit->venue }}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-2">
                            <strong>Start Date:</strong> {{ $summit->start_date->format('M d, Y') }}
                        </div>
                        <div class="mb-2">
                            <strong>End Date:</strong> {{ $summit->end_date->format('M d, Y') }}
                        </div>
                        <div class="mb-2">
                            <strong>Registration Fee:</strong> 
                            <span class="text-success fw-bold">TZS {{ number_format($summit->registration_fee) }}</span>
                        </div>
                        <div class="mb-2">
                            <strong>Registration Deadline:</strong> 
                            {{ $summit->registration_deadline->format('M d, Y') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Registration Form -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-plus me-2"></i>Registration Form</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('registrations.store', $summit) }}">
                    @csrf
                    
                    <!-- Personal Information (Read-only) -->
                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Personal Information</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">First Name</label>
                                <input type="text" class="form-control" value="{{ Auth::user()->first_name }}" readonly>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Last Name</label>
                                <input type="text" class="form-control" value="{{ Auth::user()->last_name }}" readonly>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" value="{{ Auth::user()->email }}" readonly>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Phone</label>
                                <input type="text" class="form-control" value="{{ Auth::user()->phone }}" readonly>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            To update your personal information, please <a href="{{ route('profile.edit') }}">edit your profile</a>.
                        </div>
                    </div>

                    <!-- Organization Information (Read-only) -->
                    @if(Auth::user()->branch)
                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Organization Information</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Conference</label>
                                <input type="text" class="form-control" value="{{ Auth::user()->branch->zone->conference->name }}" readonly>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Zone</label>
                                <input type="text" class="form-control" value="{{ Auth::user()->branch->zone->name }}" readonly>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Branch</label>
                                <input type="text" class="form-control" value="{{ Auth::user()->branch->name }}" readonly>
                            </div>
                            @if(Auth::user()->university)
                            <div class="col-md-6 mb-3">
                                <label class="form-label">University</label>
                                <input type="text" class="form-control" value="{{ Auth::user()->university->name }}" readonly>
                            </div>
                            @endif
                        </div>
                    </div>
                    @endif

                    <!-- Emergency Contact (From Profile) -->
                    @if(Auth::user()->emergency_contact_name || Auth::user()->emergency_contact_phone)
                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Emergency Contact (From Profile)</h6>
                        <div class="row">
                            @if(Auth::user()->emergency_contact_name)
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Contact Name</label>
                                <input type="text" class="form-control" value="{{ Auth::user()->emergency_contact_name }}" readonly>
                            </div>
                            @endif
                            @if(Auth::user()->emergency_contact_phone)
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Contact Phone</label>
                                <input type="text" class="form-control" value="{{ Auth::user()->emergency_contact_phone }}" readonly>
                            </div>
                            @endif
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            To update emergency contact information, please <a href="{{ route('profile.edit') }}">edit your profile</a>.
                        </div>
                    </div>
                    @else
                    <div class="mb-4">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            No emergency contact information found in your profile.
                            <a href="{{ route('profile.edit') }}">Add emergency contact details</a> for safety purposes.
                        </div>
                    </div>
                    @endif

                    <!-- Special Requirements -->
                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Special Requirements (Optional)</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="dietary_requirements" class="form-label">Dietary Requirements</label>
                                <textarea class="form-control @error('dietary_requirements') is-invalid @enderror" 
                                          id="dietary_requirements" 
                                          name="dietary_requirements" 
                                          rows="3"
                                          placeholder="Any dietary restrictions or preferences">{{ old('dietary_requirements') }}</textarea>
                                @error('dietary_requirements')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="special_needs" class="form-label">Special Needs</label>
                                <textarea class="form-control @error('special_needs') is-invalid @enderror" 
                                          id="special_needs" 
                                          name="special_needs" 
                                          rows="3"
                                          placeholder="Any accessibility needs or special accommodations">{{ old('special_needs') }}</textarea>
                                @error('special_needs')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input @error('terms_accepted') is-invalid @enderror" 
                                   type="checkbox" 
                                   id="terms_accepted" 
                                   name="terms_accepted" 
                                   value="1"
                                   {{ old('terms_accepted') ? 'checked' : '' }}>
                            <label class="form-check-label" for="terms_accepted">
                                I agree to the <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">Terms and Conditions</a> 
                                and understand that the registration fee is non-refundable.
                            </label>
                            @error('terms_accepted')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-user-plus me-2"></i>Complete Registration
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Registration Summary -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>Registration Summary</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Summit:</strong><br>
                    <span class="text-muted">{{ $summit->title }}</span>
                </div>
                <div class="mb-3">
                    <strong>Duration:</strong><br>
                    <span class="text-muted">
                        {{ $summit->start_date->format('M d') }} - {{ $summit->end_date->format('M d, Y') }}
                    </span>
                </div>
                <div class="mb-3">
                    <strong>Venue:</strong><br>
                    <span class="text-muted">{{ $summit->venue }}</span>
                </div>
                <hr>
                <div class="mb-3">
                    <strong>Registration Fee:</strong><br>
                    <span class="h5 text-success">TZS {{ number_format($summit->registration_fee) }}</span>
                </div>
                <div class="alert alert-warning">
                    <small>
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        Payment will be required after registration to secure your spot.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Terms and Conditions Modal -->
<div class="modal fade" id="termsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Terms and Conditions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Registration Terms</h6>
                <ul>
                    <li>Registration fees are non-refundable once payment is completed.</li>
                    <li>Participants must attend all mandatory sessions.</li>
                    <li>TUCASA STU reserves the right to modify the program schedule.</li>
                    <li>Participants are responsible for their own accommodation and travel arrangements.</li>
                    <li>By registering, you consent to photography and videography during the event.</li>
                </ul>
                
                <h6>Code of Conduct</h6>
                <ul>
                    <li>Maintain professional behavior throughout the summit.</li>
                    <li>Respect all participants, speakers, and organizers.</li>
                    <li>Follow all venue rules and regulations.</li>
                    <li>Participate actively in all sessions and activities.</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection
