@extends('layouts.user')

@section('title', 'Revenue Analytics')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">📊 Revenue Analytics</h2>
                    <p class="text-muted mb-0">Comprehensive financial insights and trends</p>
                </div>
                <div>
                    <a href="{{ route('treasurer.dashboard') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back
                    </a>
                    <a href="{{ route('treasurer.export.revenue-summary') }}" class="btn btn-success">
                        <i class="fas fa-download me-2"></i>Export Report
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue Charts -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Monthly Revenue Trend</h5>
                </div>
                <div class="card-body">
                    <canvas id="monthlyRevenueChart" height="100"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Payment Methods</h5>
                </div>
                <div class="card-body">
                    <canvas id="paymentMethodChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Branch Performance -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Top Performing Branches</h5>
                </div>
                <div class="card-body">
                    <canvas id="branchRevenueChart" height="120"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Conference Revenue Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="conferenceRevenueChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Tables -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Branch Performance Details</h5>
                </div>
                <div class="card-body">
                    @if(isset($revenueByBranch) && $revenueByBranch->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Branch</th>
                                        <th>Conference</th>
                                        <th>Revenue</th>
                                        <th>Count</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($revenueByBranch->take(10) as $branch)
                                    <tr>
                                        <td>{{ Str::limit($branch->branch_name, 20) }}</td>
                                        <td><small>{{ Str::limit($branch->conference_name, 15) }}</small></td>
                                        <td><strong>{{ number_format($branch->total, 0) }} TZS</strong></td>
                                        <td>{{ $branch->count }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted text-center">No branch data available.</p>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Recent Registration Trends</h5>
                </div>
                <div class="card-body">
                    @if(isset($registrationTrends) && $registrationTrends->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Registrations</th>
                                        <th>Trend</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($registrationTrends->take(10) as $index => $trend)
                                    <tr>
                                        <td>{{ \Carbon\Carbon::parse($trend->date)->format('M j') }}</td>
                                        <td><strong>{{ $trend->count }}</strong></td>
                                        <td>
                                            @if($index > 0)
                                                @php
                                                    $prev = $registrationTrends[$index - 1]->count ?? 0;
                                                    $current = $trend->count;
                                                    $change = $prev > 0 ? (($current - $prev) / $prev) * 100 : 0;
                                                @endphp
                                                @if($change > 0)
                                                    <small class="text-success">
                                                        <i class="fas fa-arrow-up"></i> {{ number_format($change, 1) }}%
                                                    </small>
                                                @elseif($change < 0)
                                                    <small class="text-danger">
                                                        <i class="fas fa-arrow-down"></i> {{ number_format(abs($change), 1) }}%
                                                    </small>
                                                @else
                                                    <small class="text-muted">-</small>
                                                @endif
                                            @else
                                                <small class="text-muted">-</small>
                                            @endif
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted text-center">No registration data available.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Monthly Revenue Chart
const monthlyCtx = document.getElementById('monthlyRevenueChart').getContext('2d');
const monthlyData = @json($monthlyRevenue ?? collect());
const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

new Chart(monthlyCtx, {
    type: 'line',
    data: {
        labels: monthlyData.map(item => monthNames[item.month - 1] + ' ' + item.year),
        datasets: [{
            label: 'Revenue (TZS)',
            data: monthlyData.map(item => item.total),
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return new Intl.NumberFormat().format(value) + ' TZS';
                    }
                }
            }
        }
    }
});

// Payment Methods Chart
const paymentCtx = document.getElementById('paymentMethodChart').getContext('2d');
const paymentData = @json($paymentMethodStats ?? collect());

new Chart(paymentCtx, {
    type: 'doughnut',
    data: {
        labels: paymentData.map(item => item.payment_method || 'Unknown'),
        datasets: [{
            data: paymentData.map(item => item.total),
            backgroundColor: ['#28a745', '#ffc107', '#17a2b8', '#dc3545', '#6f42c1']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    boxWidth: 12,
                    padding: 10
                }
            }
        }
    }
});

// Branch Revenue Chart
const branchCtx = document.getElementById('branchRevenueChart').getContext('2d');
const branchData = @json($revenueByBranch ?? collect());

new Chart(branchCtx, {
    type: 'bar',
    data: {
        labels: branchData.slice(0, 8).map(item => item.branch_name.substring(0, 15)),
        datasets: [{
            label: 'Revenue (TZS)',
            data: branchData.slice(0, 8).map(item => item.total),
            backgroundColor: 'rgba(40, 167, 69, 0.8)',
            borderColor: '#28a745',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return new Intl.NumberFormat().format(value) + ' TZS';
                    }
                }
            }
        }
    }
});

// Conference Revenue Chart
const conferenceCtx = document.getElementById('conferenceRevenueChart').getContext('2d');
const conferenceData = @json($revenueByConference ?? collect());

new Chart(conferenceCtx, {
    type: 'pie',
    data: {
        labels: conferenceData.map(item => item.name),
        datasets: [{
            data: conferenceData.map(item => item.total),
            backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#17a2b8', '#6f42c1']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    boxWidth: 12,
                    padding: 10
                }
            }
        }
    }
});
</script>
@endpush
