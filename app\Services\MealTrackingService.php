<?php

namespace App\Services;

use App\Models\MealTracking;
use App\Models\IdCard;
use App\Models\SummitActivity;
use App\Models\User;
use App\Models\Summit;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class MealTrackingService
{
    /**
     * Scan QR code and track meal
     */
    public function scanQrCodeForMeal(string $cardNumber, string $mealType, ?int $activityId = null, ?string $scannedBy = null): array
    {
        try {
            // Verify the ID card
            $idCard = IdCard::where('card_number', $cardNumber)
                ->where('is_active', true)
                ->where('expires_at', '>', now())
                ->with(['user', 'summit'])
                ->first();

            if (!$idCard) {
                return [
                    'success' => false,
                    'message' => 'Invalid or expired ID card.',
                    'error_code' => 'INVALID_CARD'
                ];
            }

            // Check if user has completed payment for the summit
            $registration = $idCard->user->summitRegistrations()
                ->where('summit_id', $idCard->summit_id)
                ->where('payment_complete', true)
                ->first();

            if (!$registration) {
                return [
                    'success' => false,
                    'message' => 'User has not completed payment for this summit.',
                    'error_code' => 'PAYMENT_INCOMPLETE'
                ];
            }

            // Get current summit activity if not provided
            if (!$activityId) {
                $currentActivity = SummitActivity::where('summit_id', $idCard->summit_id)
                    ->where('start_time', '<=', now())
                    ->where('end_time', '>=', now())
                    ->first();

                if (!$currentActivity) {
                    // If no current activity, get the most recent one for today
                    $currentActivity = SummitActivity::where('summit_id', $idCard->summit_id)
                        ->whereDate('start_time', today())
                        ->orderBy('start_time', 'desc')
                        ->first();
                }

                if (!$currentActivity) {
                    return [
                        'success' => false,
                        'message' => 'No active summit activity found for meal tracking.',
                        'error_code' => 'NO_ACTIVITY'
                    ];
                }

                $activityId = $currentActivity->id;
            }

            // Check if meal has already been tracked for this user, activity, and meal type today
            $existingTracking = MealTracking::where('user_id', $idCard->user_id)
                ->where('summit_id', $idCard->summit_id)
                ->where('summit_activity_id', $activityId)
                ->where('meal_type', $mealType)
                ->whereDate('scanned_at', today())
                ->first();

            if ($existingTracking) {
                return [
                    'success' => false,
                    'message' => "Meal ({$mealType}) already tracked for this user today.",
                    'error_code' => 'ALREADY_TRACKED',
                    'existing_tracking' => $existingTracking
                ];
            }

            // Create meal tracking record
            $mealTracking = MealTracking::create([
                'user_id' => $idCard->user_id,
                'summit_id' => $idCard->summit_id,
                'summit_activity_id' => $activityId,
                'meal_type' => $mealType,
                'scanned_at' => now(),
                'scanned_by' => $scannedBy ?? Auth::user()?->full_name ?? 'System',
                'notes' => "Scanned via QR code: {$cardNumber}"
            ]);

            return [
                'success' => true,
                'message' => "Meal ({$mealType}) tracked successfully for {$idCard->user->full_name}.",
                'meal_tracking' => $mealTracking,
                'user' => $idCard->user,
                'summit' => $idCard->summit
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'An error occurred while tracking the meal.',
                'error_code' => 'SYSTEM_ERROR',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get meal tracking statistics for a summit
     */
    public function getMealTrackingStats(Summit $summit): array
    {
        $stats = [];

        $mealTypes = ['breakfast', 'lunch', 'dinner', 'snack'];

        foreach ($mealTypes as $mealType) {
            $stats[$mealType] = [
                'total_tracked' => MealTracking::where('summit_id', $summit->id)
                    ->where('meal_type', $mealType)
                    ->count(),
                'today_tracked' => MealTracking::where('summit_id', $summit->id)
                    ->where('meal_type', $mealType)
                    ->whereDate('scanned_at', today())
                    ->count(),
                'unique_users' => MealTracking::where('summit_id', $summit->id)
                    ->where('meal_type', $mealType)
                    ->distinct('user_id')
                    ->count('user_id')
            ];
        }

        return $stats;
    }

    /**
     * Get meal tracking history for a user
     */
    public function getUserMealHistory(User $user, Summit $summit): array
    {
        $mealTrackings = MealTracking::where('user_id', $user->id)
            ->where('summit_id', $summit->id)
            ->with(['summitActivity'])
            ->orderBy('scanned_at', 'desc')
            ->get();

        return $mealTrackings->groupBy(function ($item) {
            return $item->scanned_at->format('Y-m-d');
        })->toArray();
    }

    /**
     * Check if user can have a specific meal type now
     */
    public function canUserHaveMeal(User $user, Summit $summit, string $mealType): array
    {
        // Check if user has already had this meal today
        $existingTracking = MealTracking::where('user_id', $user->id)
            ->where('summit_id', $summit->id)
            ->where('meal_type', $mealType)
            ->whereDate('scanned_at', today())
            ->first();

        if ($existingTracking) {
            return [
                'can_have_meal' => false,
                'reason' => "Already had {$mealType} today",
                'last_meal_time' => $existingTracking->scanned_at
            ];
        }

        // Check time-based restrictions (optional - can be customized)
        $currentHour = now()->hour;
        $timeRestrictions = [
            'breakfast' => ['start' => 6, 'end' => 10],
            'lunch' => ['start' => 11, 'end' => 15],
            'dinner' => ['start' => 17, 'end' => 21],
            'snack' => ['start' => 0, 'end' => 23] // Snacks allowed all day
        ];

        if (isset($timeRestrictions[$mealType])) {
            $restriction = $timeRestrictions[$mealType];
            if ($currentHour < $restriction['start'] || $currentHour > $restriction['end']) {
                return [
                    'can_have_meal' => false,
                    'reason' => "Outside {$mealType} time window ({$restriction['start']}:00 - {$restriction['end']}:00)",
                    'current_time' => now()->format('H:i')
                ];
            }
        }

        return [
            'can_have_meal' => true,
            'reason' => 'Eligible for meal'
        ];
    }

    /**
     * Get daily meal report
     */
    public function getDailyMealReport(Summit $summit, ?Carbon $date = null): array
    {
        $date = $date ?? today();

        $report = [];
        $mealTypes = ['breakfast', 'lunch', 'dinner', 'snack'];

        foreach ($mealTypes as $mealType) {
            $trackings = MealTracking::where('summit_id', $summit->id)
                ->where('meal_type', $mealType)
                ->whereDate('scanned_at', $date)
                ->with(['user', 'summitActivity'])
                ->orderBy('scanned_at', 'desc')
                ->get();

            $report[$mealType] = [
                'count' => $trackings->count(),
                'trackings' => $trackings
            ];
        }

        return $report;
    }
}
