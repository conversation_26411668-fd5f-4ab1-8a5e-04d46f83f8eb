@echo off
echo ========================================
echo   Fix CSS Issues for ngrok
echo ========================================
echo.

echo Step 1: Building production assets...
npm run build

echo.
echo Step 2: Clearing Laravel caches...
php artisan config:cache
php artisan view:clear
php artisan route:clear
php artisan cache:clear

echo.
echo Step 3: Restarting Laravel server...
taskkill /f /im php.exe 2>nul
timeout /t 2 /nobreak > nul
start "Laravel Server" cmd /k "php artisan serve --host=127.0.0.1 --port=8000"

echo.
echo ========================================
echo CSS Fix Complete!
echo.
echo Next steps:
echo 1. Make sure ngrok is running: ngrok http 8000
echo 2. Update your .env with the ngrok URL
echo 3. Run: php artisan config:cache
echo 4. Refresh your browser
echo ========================================
pause
