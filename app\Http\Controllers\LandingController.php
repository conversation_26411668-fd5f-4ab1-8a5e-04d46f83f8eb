<?php

namespace App\Http\Controllers;

use App\Models\Summit;
use App\Models\Minister;
use App\Models\Activity;

class LandingController extends Controller
{
    /**
     * Display the landing page.
     */
    public function index()
    {
        $currentSummit = Summit::current()->with(['ministers', 'activities'])->first();

        // Get general activities and ministers for the landing page
        $activities = Activity::active()->ordered()->limit(6)->get();
        $ministers = Minister::active()->ordered()->limit(8)->get();
        $featuredActivities = Activity::active()->featured()->ordered()->limit(3)->get();

        $upcomingActivities = null;
        $summitMinisters = collect();
        $daysUntilSummit = null;

        if ($currentSummit) {
            $daysUntilSummit = (int) now()->diffInDays($currentSummit->start_date, false);
        }

        return view('landing.index', compact(
            'currentSummit',
            'daysUntilSummit',
            'activities',
            'ministers',
            'featuredActivities'
        ));
    }

    /**
     * Display summit details.
     */
    public function summit(Summit $summit)
    {
        $summit->load(['activities', 'ministers']);

        return view('landing.summit', compact('summit'));
    }

    /**
     * Display summit activities.
     */
    public function activities(Summit $summit)
    {
        $activities = Activity::active()
            ->ordered()
            ->get();

        return view('landing.activities', compact('summit', 'activities'));
    }

    /**
     * Display summit ministers.
     */
    public function ministers(Summit $summit)
    {
        $ministers = Minister::active()
            ->ordered()
            ->get();

        return view('landing.ministers', compact('summit', 'ministers'));
    }
}
