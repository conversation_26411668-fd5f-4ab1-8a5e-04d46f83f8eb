<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Payment extends Model
{
    protected $fillable = [
        'user_id',
        'summit_registration_id',
        'payment_reference',
        'transaction_id',
        'external_reference',
        'amount',
        'payment_method',
        'gateway_provider',
        'status',
        'gateway_response',
        'gateway_data',
        'provider_data',
        'paid_at',
        'gateway_callback_at',
        'receipt_number',
        'admin_notes'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'gateway_data' => 'array',
        'provider_data' => 'array',
        'paid_at' => 'datetime',
        'gateway_callback_at' => 'datetime'
    ];

    /**
     * Get the user that owns the payment.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the summit registration that owns the payment.
     */
    public function summitRegistration(): BelongsTo
    {
        return $this->belongsTo(SummitRegistration::class);
    }

    /**
     * Scope a query to only include completed payments.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope a query to only include pending payments.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }
}
