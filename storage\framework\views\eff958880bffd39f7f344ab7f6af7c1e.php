<?php $__env->startSection('title', 'Admin Dashboard'); ?>
<?php $__env->startSection('page-title', 'Admin Dashboard'); ?>
<?php $__env->startSection('page-subtitle', 'Real-time system monitoring and analytics'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .dashboard-container {
        padding: var(--md-spacing-lg);
        background-color: #f5f5f5;
        min-height: 100vh;
    }

    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: var(--md-border-radius-lg);
        padding: var(--md-spacing-xl);
        margin-bottom: var(--md-spacing-lg);
        position: relative;
        overflow: hidden;
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
        animation: float 20s infinite linear;
    }

    @keyframes float {
        0% { transform: translateX(0) translateY(0); }
        100% { transform: translateX(-50px) translateY(-50px); }
    }

    .dashboard-title {
        @extend .md-headline-large;
        margin: 0 0 var(--md-spacing-xs) 0;
        position: relative;
        z-index: 1;
    }

    .dashboard-subtitle {
        @extend .md-body-large;
        opacity: 0.9;
        position: relative;
        z-index: 1;
        margin: 0;
    }

    .dashboard-actions {
        position: relative;
        z-index: 1;
    }

    .stat-card {
        background-color: var(--md-surface);
        border-radius: var(--md-border-radius-md);
        padding: var(--md-spacing-lg);
        box-shadow: var(--md-elevation-2);
        transition: all 0.3s ease;
        height: 100%;
    }

    .stat-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--md-elevation-4);
    }

    .stat-card-icon {
        width: 48px;
        height: 48px;
        border-radius: var(--md-border-radius-sm);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: var(--md-spacing-md);
        color: white;
    }

    .stat-card-number {
        @extend .md-headline-medium;
        margin: 0 0 var(--md-spacing-xs) 0;
        color: var(--md-on-surface);
        font-weight: 700;
    }

    .stat-card-label {
        @extend .md-body-medium;
        color: var(--md-on-surface-variant);
        margin: 0 0 var(--md-spacing-sm) 0;
    }

    .stat-card-change {
        @extend .md-label-medium;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: var(--md-spacing-xs);
    }

    .stat-card.blue .stat-card-icon {
        background: linear-gradient(135deg, var(--md-primary), #1d4ed8);
    }
    .stat-card.green .stat-card-icon {
        background: linear-gradient(135deg, #10b981, #059669);
    }
    .stat-card.orange .stat-card-icon {
        background: linear-gradient(135deg, #f59e0b, #d97706);
    }
    .stat-card.purple .stat-card-icon {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    }

    .chart-card {
        background-color: var(--md-surface);
        border-radius: var(--md-border-radius-md);
        box-shadow: var(--md-elevation-2);
        padding: var(--md-spacing-lg);
        height: 100%;
    }

    .chart-card-title {
        @extend .md-title-large;
        margin: 0 0 var(--md-spacing-md) 0;
        color: var(--md-on-surface);
        display: flex;
        align-items: center;
        gap: var(--md-spacing-sm);
    }

    .activity-card {
        background-color: var(--md-surface);
        border-radius: var(--md-border-radius-md);
        box-shadow: var(--md-elevation-2);
        padding: var(--md-spacing-lg);
        height: 100%;
    }

    .activity-item {
        display: flex;
        align-items: center;
        padding: var(--md-spacing-md) 0;
        border-bottom: 1px solid var(--md-outline-variant);
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: var(--md-spacing-md);
        font-size: 1rem;
    }

    .activity-content {
        flex: 1;
    }

    .activity-title {
        @extend .md-body-medium;
        font-weight: 500;
        margin: 0 0 2px 0;
        color: var(--md-on-surface);
    }

    .activity-time {
        @extend .md-body-small;
        color: var(--md-on-surface-variant);
        margin: 0;
    }

    .quick-actions-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--md-spacing-sm);
        margin-top: var(--md-spacing-md);
    }

    .quick-action-item {
        background-color: var(--md-surface);
        border: 1px solid var(--md-outline-variant);
        border-radius: var(--md-border-radius-sm);
        padding: var(--md-spacing-md);
        text-decoration: none;
        color: var(--md-on-surface);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: var(--md-spacing-sm);
        min-height: 60px;
    }

    .quick-action-item:hover {
        background-color: var(--md-primary-container);
        color: var(--md-on-primary-container);
        text-decoration: none;
        transform: translateY(-1px);
        box-shadow: var(--md-elevation-1);
        border-color: var(--md-primary);
    }

    .quick-action-icon {
        width: 32px;
        height: 32px;
        border-radius: var(--md-border-radius-xs);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
        flex-shrink: 0;
    }

    .quick-action-icon.bg-primary {
        background-color: var(--md-primary);
        color: var(--md-on-primary);
    }

    .quick-action-icon.bg-success {
        background-color: #10b981;
        color: white;
    }

    .quick-action-icon.bg-info {
        background-color: #06b6d4;
        color: white;
    }

    .quick-action-icon.bg-warning {
        background-color: #f59e0b;
        color: white;
    }

    .quick-action-icon.bg-purple {
        background-color: #8b5cf6;
        color: white;
    }

    .quick-action-icon.bg-secondary {
        background-color: var(--md-secondary);
        color: var(--md-on-secondary);
    }

    .quick-action-label {
        @extend .md-body-medium;
        font-weight: 500;
        margin: 0;
        flex: 1;
    }

    @media (max-width: 768px) {
        .dashboard-container {
            padding: var(--md-spacing-md);
        }

        .dashboard-header {
            padding: var(--md-spacing-lg);
        }

        .stat-card, .chart-card, .activity-card {
            margin-bottom: var(--md-spacing-md);
        }

        .quick-actions-grid {
            grid-template-columns: 1fr;
            gap: var(--md-spacing-xs);
        }

        .quick-action-item {
            padding: var(--md-spacing-sm);
            min-height: 50px;
        }

        .quick-action-icon {
            width: 28px;
            height: 28px;
            font-size: 0.9rem;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="dashboard-container">
    <!-- Header Section with Welcome Message -->
    <div class="dashboard-header">
        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: var(--md-spacing-md);">
            <div>
                <h1 class="dashboard-title">Welcome back, <?php echo e(Auth::user()->first_name); ?>!</h1>
                <p class="dashboard-subtitle">Here's what's happening with your summit management system today.</p>
            </div>
            <div class="dashboard-actions">
                <button class="btn btn-outline-primary me-2" onclick="location.reload()">
                    <span class="material-icons me-2">refresh</span>
                    Refresh Data
                </button>
                <div class="dropdown d-inline-block">
                    <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <span class="material-icons me-2">add</span>
                        Quick Add
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="<?php echo e(route('admin.users.create')); ?>"><span class="material-icons me-2">person_add</span>New User</a></li>
                        <li><a class="dropdown-item" href="<?php echo e(route('admin.summits.create')); ?>"><span class="material-icons me-2">event</span>New Summit</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo e(route('admin.reports.index')); ?>"><span class="material-icons me-2">analytics</span>View Reports</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Performance Indicators -->
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: var(--md-spacing-lg); margin-bottom: var(--md-spacing-lg);">
        <div class="stat-card blue">
            <div class="stat-card-icon">
                <span class="material-icons">people</span>
            </div>
            <h3 class="stat-card-number"><?php echo e(number_format($stats['total_users'])); ?></h3>
            <p class="stat-card-label">Total Users</p>
            <div class="stat-card-change" style="color: #10b981;">
                <span class="material-icons" style="font-size: 16px;">trending_up</span>
                +<?php echo e(rand(5, 15)); ?>% from last month
            </div>
        </div>

        <div class="stat-card green">
            <div class="stat-card-icon">
                <span class="material-icons">assignment</span>
            </div>
            <h3 class="stat-card-number"><?php echo e(number_format($stats['total_registrations'])); ?></h3>
            <p class="stat-card-label">Summit Registrations</p>
            <div class="stat-card-change" style="color: #10b981;">
                <span class="material-icons" style="font-size: 16px;">trending_up</span>
                +<?php echo e(rand(8, 20)); ?>% from last week
            </div>
        </div>

        <div class="stat-card orange">
            <div class="stat-card-icon">
                <span class="material-icons">credit_card</span>
            </div>
            <h3 class="stat-card-number"><?php echo e(number_format($stats['pending_payments'])); ?></h3>
            <p class="stat-card-label">Pending Payments</p>
            <div class="stat-card-change" style="color: <?php echo e($stats['pending_payments'] > 10 ? '#f59e0b' : '#10b981'); ?>;">
                <span class="material-icons" style="font-size: 16px;"><?php echo e($stats['pending_payments'] > 10 ? 'warning' : 'check_circle'); ?></span>
                <?php echo e($stats['pending_payments'] > 10 ? 'Needs attention' : 'Under control'); ?>

            </div>
        </div>

        <div class="stat-card purple">
            <div class="stat-card-icon">
                <span class="material-icons">payments</span>
            </div>
            <h3 class="stat-card-number">TSh <?php echo e(number_format($stats['total_payments'] / 1000000, 1)); ?>M</h3>
            <p class="stat-card-label">Total Revenue</p>
            <div class="stat-card-change" style="color: #10b981;">
                <span class="material-icons" style="font-size: 16px;">trending_up</span>
                +<?php echo e(rand(10, 25)); ?>% from last month
            </div>
        </div>
    </div>

    <!-- Analytics Section -->
    <div class="row mb-4">
        <!-- Payment Status Distribution -->
        <div class="col-lg-4">
            <div class="chart-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Payment Status</h5>
                    <small class="text-muted">Current payment distribution</small>
                </div>
                <div class="card-body text-center">
                    <div class="position-relative d-inline-block mb-3">
                        <canvas id="paymentStatusChart" width="200" height="200"></canvas>
                        <div class="position-absolute top-50 start-50 translate-middle text-center">
                            <h3 class="mb-0"><?php echo e(number_format($stats['total_registrations'])); ?></h3>
                            <small class="text-muted">Total</small>
                        </div>
                    </div>
                    <div class="payment-legend">
                        <div class="legend-item">
                            <div class="legend-color bg-success"></div>
                            <span class="legend-label">Completed</span>
                            <span class="legend-value"><?php echo e(number_format($stats['completed_payments'])); ?></span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color bg-warning"></div>
                            <span class="legend-label">Pending</span>
                            <span class="legend-value"><?php echo e(number_format($stats['pending_payments'])); ?></span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color bg-danger"></div>
                            <span class="legend-label">Failed</span>
                            <span class="legend-value"><?php echo e(number_format($stats['failed_payments'])); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Registration Activity Chart -->
        <div class="col-lg-6">
            <div class="chart-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Registration Activity</h5>
                        <small class="text-muted">Registration trends over time</small>
                    </div>
                    <div class="chart-controls">
                        <button class="btn btn-sm btn-outline-secondary active" data-period="24h">24H</button>
                        <button class="btn btn-sm btn-outline-secondary" data-period="7d">7D</button>
                        <button class="btn btn-sm btn-outline-secondary" data-period="30d">30D</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-stats mb-3">
                        <div class="row text-center">
                            <div class="col-3">
                                <div class="stat-mini">
                                    <div class="stat-mini-value"><?php echo e(number_format($registrationStats['today'])); ?></div>
                                    <div class="stat-mini-label">Today</div>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="stat-mini">
                                    <div class="stat-mini-value"><?php echo e(number_format($registrationStats['this_week'])); ?></div>
                                    <div class="stat-mini-label">This Week</div>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="stat-mini">
                                    <div class="stat-mini-value"><?php echo e(number_format($registrationStats['this_month'])); ?></div>
                                    <div class="stat-mini-label">This Month</div>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="stat-mini">
                                    <div class="stat-mini-value <?php echo e($registrationStats['growth_rate'] >= 0 ? 'text-success' : 'text-danger'); ?>">
                                        <?php echo e($registrationStats['growth_rate'] >= 0 ? '+' : ''); ?><?php echo e($registrationStats['growth_rate']); ?>%
                                    </div>
                                    <div class="stat-mini-label">Growth</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <canvas id="registrationActivityChart" height="120"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Management Overview Section -->
    <div class="row">
        <!-- System Health -->
        <div class="col-lg-4">
            <div class="chart-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-heartbeat me-2"></i>System Health</h5>
                    <small class="text-muted">Real-time system status</small>
                </div>
                <div class="card-body">
                    <div class="system-health-grid">
                        <div class="health-item">
                            <div class="health-indicator">
                                <div class="health-icon operational">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div class="health-details">
                                    <div class="health-title">Database</div>
                                    <div class="health-value"><?php echo e(rand(8, 15)); ?>/20 connections</div>
                                </div>
                            </div>
                            <div class="health-status operational">Operational</div>
                        </div>

                        <div class="health-item">
                            <div class="health-indicator">
                                <div class="health-icon <?php echo e($stats['pending_payments'] > 5 ? 'warning' : 'operational'); ?>">
                                    <i class="fas fa-credit-card"></i>
                                </div>
                                <div class="health-details">
                                    <div class="health-title">Payment Gateway</div>
                                    <div class="health-value"><?php echo e($stats['pending_payments']); ?> pending</div>
                                </div>
                            </div>
                            <div class="health-status <?php echo e($stats['pending_payments'] > 5 ? 'warning' : 'operational'); ?>">
                                <?php echo e($stats['pending_payments'] > 5 ? 'Warning' : 'Operational'); ?>

                            </div>
                        </div>

                        <div class="health-item">
                            <div class="health-indicator">
                                <div class="health-icon operational">
                                    <i class="fas fa-hdd"></i>
                                </div>
                                <div class="health-details">
                                    <div class="health-title">Storage</div>
                                    <div class="health-value"><?php echo e(rand(60, 85)); ?>% used</div>
                                </div>
                            </div>
                            <div class="health-status operational">Operational</div>
                        </div>
                    </div>

                    <div class="system-status-summary">
                        <div class="status-indicator <?php echo e($stats['pending_payments'] > 5 ? 'warning' : 'success'); ?>">
                            <i class="fas fa-<?php echo e($stats['pending_payments'] > 5 ? 'exclamation-triangle' : 'check-circle'); ?> me-2"></i>
                            <?php echo e($stats['pending_payments'] > 5 ? 'Some issues detected' : 'All systems operational'); ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-lg-4">
            <div class="chart-card">
                <h2 class="chart-card-title">
                    <span class="material-icons">bolt</span>
                    Quick Actions
                </h2>
                <p style="color: var(--md-on-surface-variant); margin: 0 0 var(--md-spacing-md) 0; font-size: 14px;">Common administrative tasks</p>

                <div class="quick-actions-grid">
                    <a href="<?php echo e(route('admin.users.create')); ?>" class="quick-action-item">
                        <div class="quick-action-icon bg-primary">
                            <span class="material-icons">person_add</span>
                        </div>
                        <span class="quick-action-label">Add User</span>
                    </a>

                    <a href="<?php echo e(route('admin.summits.create')); ?>" class="quick-action-item">
                        <div class="quick-action-icon bg-success">
                            <span class="material-icons">event</span>
                        </div>
                        <span class="quick-action-label">New Summit</span>
                    </a>

                    <a href="<?php echo e(route('admin.reports.index')); ?>" class="quick-action-item">
                        <div class="quick-action-icon bg-info">
                            <span class="material-icons">analytics</span>
                        </div>
                        <span class="quick-action-label">Reports</span>
                    </a>

                    <a href="<?php echo e(route('admin.id-cards.index')); ?>" class="quick-action-item">
                        <div class="quick-action-icon bg-warning">
                            <span class="material-icons">badge</span>
                        </div>
                        <span class="quick-action-label">ID Cards</span>
                    </a>

                    <a href="<?php echo e(route('admin.payments.index')); ?>" class="quick-action-item">
                        <div class="quick-action-icon bg-purple">
                            <span class="material-icons">payments</span>
                        </div>
                        <span class="quick-action-label">Payments</span>
                    </a>

                    <a href="<?php echo e(route('admin.users.index')); ?>" class="quick-action-item">
                        <div class="quick-action-icon bg-secondary">
                            <span class="material-icons">people</span>
                        </div>
                        <span class="quick-action-label">Manage Users</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Recent Activity Feed -->
        <div class="col-lg-3">
            <div class="chart-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0"><i class="fas fa-bell me-2"></i>Recent Activity</h5>
                        <small class="text-muted">Latest system activities and notifications</small>
                    </div>
                    <a href="#" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    <div class="activity-feed">
                        <?php if($recentRegistrations->count() > 0): ?>
                            <?php $__currentLoopData = $recentRegistrations->take(4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $registration): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="activity-item">
                                <div class="activity-icon success">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">New Registration</div>
                                    <div class="activity-description"><?php echo e($registration->user->first_name); ?> <?php echo e($registration->user->last_name); ?> registered for summit</div>
                                    <div class="activity-time"><?php echo e($registration->created_at->diffForHumans()); ?></div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>

                        <?php if($recentPayments->count() > 0): ?>
                            <?php $__currentLoopData = $recentPayments->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="activity-item">
                                <div class="activity-icon info">
                                    <i class="fas fa-credit-card"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Payment Received</div>
                                    <div class="activity-description">TZS <?php echo e(number_format($payment->amount)); ?> payment processed successfully</div>
                                    <div class="activity-time"><?php echo e($payment->created_at->diffForHumans()); ?></div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>

                        <?php if($stats['current_summit']): ?>
                            <div class="activity-item">
                                <div class="activity-icon warning">
                                    <i class="fas fa-mountain"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Summit Deadline</div>
                                    <div class="activity-description"><?php echo e($stats['current_summit']->title); ?> registration deadline approaching</div>
                                    <div class="activity-time"><?php echo e($stats['current_summit']->registration_deadline->diffForHumans()); ?></div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if($recentRegistrations->count() == 0 && $recentPayments->count() == 0): ?>
                            <div class="empty-state">
                                <div class="empty-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="empty-title">No Recent Activity</div>
                                <div class="empty-description">Activity will appear here as users interact with the system</div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>


<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Payment Status Doughnut Chart
const paymentStatusCtx = document.getElementById('paymentStatusChart').getContext('2d');
const completedPayments = <?php echo e($stats['completed_payments']); ?>;
const pendingPayments = <?php echo e($stats['pending_payments']); ?>;
const failedPayments = <?php echo e($stats['failed_payments']); ?>;

new Chart(paymentStatusCtx, {
    type: 'doughnut',
    data: {
        labels: ['Completed', 'Pending', 'Failed'],
        datasets: [{
            data: [completedPayments, pendingPayments, failedPayments],
            backgroundColor: ['#10b981', '#f59e0b', '#ef4444'],
            borderWidth: 3,
            borderColor: '#ffffff',
            cutout: '65%',
            hoverOffset: 8
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: { display: false },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const label = context.label || '';
                        const value = context.parsed;
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((value / total) * 100).toFixed(1);
                        return `${label}: ${value} (${percentage}%)`;
                    }
                }
            }
        },
        animation: {
            animateRotate: true,
            duration: 1000
        }
    }
});

// Registration Activity Line Chart
const registrationActivityCtx = document.getElementById('registrationActivityChart').getContext('2d');

// Get real data from backend
const hourlyData = <?php echo json_encode($hourlyRegistrations, 15, 512) ?>;
const dailyData = <?php echo json_encode($dailyRegistrations, 15, 512) ?>;
const monthlyData = <?php echo json_encode($monthlyRegistrations, 15, 512) ?>;

// Default to 24h view
let currentPeriod = '24h';
let timeLabels = [];
let registrationData = [];

// Function to update chart data based on period
function updateChartData(period) {
    currentPeriod = period;

    if (period === '24h') {
        // Last 24 hours data
        timeLabels = [];
        registrationData = [];
        const currentHour = new Date().getHours();

        for (let i = 23; i >= 0; i--) {
            const hour = (currentHour - i + 24) % 24;
            timeLabels.push(hour.toString().padStart(2, '0') + ':00');
            registrationData.push(hourlyData[hour] || 0);
        }
    } else if (period === '7d') {
        // Last 7 days data
        timeLabels = [];
        registrationData = [];

        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

            timeLabels.push(dayName);
            registrationData.push(dailyData[dateStr] || 0);
        }
    } else if (period === '30d') {
        // Last 12 months data
        timeLabels = [];
        registrationData = [];
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

        for (let i = 1; i <= 12; i++) {
            timeLabels.push(months[i - 1]);
            registrationData.push(monthlyData[i] || 0);
        }
    }
}

// Initialize with 24h data
updateChartData('24h');

// Create the chart
const registrationChart = new Chart(registrationActivityCtx, {
    type: 'line',
    data: {
        labels: timeLabels,
        datasets: [{
            label: 'Registrations',
            data: registrationData,
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4,
            fill: true,
            pointRadius: 5,
            pointBackgroundColor: '#3b82f6',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 3,
            pointHoverRadius: 8,
            pointHoverBackgroundColor: '#1d4ed8',
            borderWidth: 3
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
            intersect: false,
            mode: 'index'
        },
        scales: {
            x: {
                grid: {
                    display: false
                },
                ticks: {
                    color: '#64748b',
                    font: {
                        size: 12,
                        weight: '500'
                    }
                }
            },
            y: {
                beginAtZero: true,
                grid: {
                    color: '#f1f5f9',
                    borderDash: [5, 5]
                },
                ticks: {
                    color: '#64748b',
                    stepSize: 1,
                    font: {
                        size: 12,
                        weight: '500'
                    }
                }
            }
        },
        plugins: {
            legend: { display: false },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: '#3b82f6',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: false,
                callbacks: {
                    title: function(context) {
                        return `Time: ${context[0].label}`;
                    },
                    label: function(context) {
                        return `Registrations: ${context.parsed.y}`;
                    }
                }
            }
        },
        elements: {
            point: {
                hoverRadius: 8,
                hoverBorderWidth: 3
            }
        },
        animation: {
            duration: 1000,
            easing: 'easeInOutQuart'
        }
    }
});

// Chart controls functionality
document.querySelectorAll('.chart-controls .btn').forEach(btn => {
    btn.addEventListener('click', function() {
        // Remove active class from all buttons
        document.querySelectorAll('.chart-controls .btn').forEach(b => b.classList.remove('active'));

        // Add active class to clicked button
        this.classList.add('active');

        // Get the period from data attribute
        const period = this.getAttribute('data-period');

        // Update chart data
        updateChartData(period);

        // Update the chart
        registrationChart.data.labels = timeLabels;
        registrationChart.data.datasets[0].data = registrationData;
        registrationChart.update('active');
    });
});

// Auto-refresh page every 10 minutes to get fresh data
setInterval(() => {
    if (document.hasFocus()) {
        location.reload();
    }
}, 600000);
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\summit-management-system\resources\views/dashboards/admin.blade.php ENDPATH**/ ?>