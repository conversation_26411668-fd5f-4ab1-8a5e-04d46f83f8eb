<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($summit->title); ?> - TUCASA STU Summit</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        .info-card {
            transition: transform 0.3s;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .info-card:hover {
            transform: translateY(-5px);
        }
        .gallery-img {
            height: 200px;
            object-fit: cover;
            border-radius: 10px;
            transition: transform 0.3s;
        }
        .gallery-img:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<?php echo e(route('landing.index')); ?>">
                <i class="fas fa-mountain"></i> TUCASA STU Summit
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('landing.index')); ?>">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="<?php echo e(route('landing.summit', $summit)); ?>">Summit Details</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('landing.activities', $summit)); ?>">Activities</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('landing.ministers', $summit)); ?>">Ministers</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if(auth()->guard()->guest()): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('login')); ?>">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('register')); ?>">Register</a>
                    </li>
                    <?php else: ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <?php echo e(Auth::user()->name); ?>

                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?php echo e(route('logout')); ?>" 
                                   onclick="event.preventDefault(); document.getElementById('logout-form').submit();">Logout</a></li>
                        </ul>
                        <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" class="d-none">
                            <?php echo csrf_field(); ?>
                        </form>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-4 fw-bold"><?php echo e($summit->title); ?></h1>
                    <p class="lead"><?php echo e($summit->theme); ?></p>
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h5><i class="fas fa-calendar me-2"></i><?php echo e($summit->start_date->format('F j')); ?> - <?php echo e($summit->end_date->format('F j, Y')); ?></h5>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="fas fa-map-marker-alt me-2"></i><?php echo e($summit->venue); ?></h5>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <?php if($summit->poster_image): ?>
                    <img src="<?php echo e(Storage::url($summit->poster_image)); ?>" 
                         alt="Summit Poster" 
                         class="img-fluid rounded shadow">
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Summit Details -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <div class="card info-card mb-4">
                        <div class="card-body">
                            <h3 class="card-title"><i class="fas fa-info-circle text-primary me-2"></i>About This Summit</h3>
                            <p class="card-text"><?php echo e($summit->description); ?></p>
                        </div>
                    </div>

                    <div class="card info-card mb-4">
                        <div class="card-body">
                            <h3 class="card-title"><i class="fas fa-map-marker-alt text-primary me-2"></i>Venue Information</h3>
                            <h5><?php echo e($summit->venue); ?></h5>
                            <p class="text-muted"><?php echo e($summit->venue_address); ?></p>
                        </div>
                    </div>

                    <?php if($summit->gallery_images && count($summit->gallery_images) > 0): ?>
                    <div class="card info-card">
                        <div class="card-body">
                            <h3 class="card-title"><i class="fas fa-images text-primary me-2"></i>Gallery</h3>
                            <div class="row">
                                <?php $__currentLoopData = $summit->gallery_images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-4 mb-3">
                                    <img src="<?php echo e(Storage::url($image)); ?>" 
                                         alt="Summit Gallery" 
                                         class="img-fluid gallery-img w-100">
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="col-lg-4">
                    <!-- Registration Info -->
                    <div class="card info-card mb-4">
                        <div class="card-body text-center">
                            <h4 class="card-title"><i class="fas fa-ticket-alt text-primary me-2"></i>Registration</h4>
                            <h3 class="text-success">TSh <?php echo e(number_format($summit->registration_fee)); ?></h3>
                            <p class="text-muted">Registration Fee</p>
                            <hr>
                            <p><strong>Deadline:</strong><br><?php echo e($summit->registration_deadline->format('F j, Y')); ?></p>
                            <?php if(auth()->guard()->check()): ?>
                                <?php
                                    $userRegistration = Auth::user()->summitRegistrations()
                                        ->where('summit_id', $summit->id)
                                        ->whereIn('status', ['pending', 'confirmed'])
                                        ->first();
                                ?>
                                <?php if(!$userRegistration): ?>
                                <a href="<?php echo e(route('registrations.create', $summit)); ?>" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-user-plus me-2"></i>Register Now
                                </a>
                                <?php else: ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>You're registered!
                                    <div class="mt-2">
                                        <a href="<?php echo e(route('registrations.show', $userRegistration)); ?>" class="btn btn-sm btn-outline-success">
                                            View Registration
                                        </a>
                                    </div>
                                </div>
                                <?php endif; ?>
                            <?php else: ?>
                            <a href="<?php echo e(route('register')); ?>" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-user-plus me-2"></i>Register Now
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="card info-card mb-4">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-chart-bar text-primary me-2"></i>Summit Stats</h5>
                            <div class="row text-center">
                                <div class="col-6">
                                    <h4 class="text-primary"><?php echo e($summit->activities->count()); ?></h4>
                                    <small class="text-muted">Activities</small>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-primary"><?php echo e($summit->ministers->count()); ?></h4>
                                    <small class="text-muted">Ministers</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="card info-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-link text-primary me-2"></i>Quick Links</h5>
                            <div class="d-grid gap-2">
                                <a href="<?php echo e(route('landing.activities', $summit)); ?>" class="btn btn-outline-primary">
                                    <i class="fas fa-calendar-alt me-2"></i>View Activities
                                </a>
                                <a href="<?php echo e(route('landing.ministers', $summit)); ?>" class="btn btn-outline-primary">
                                    <i class="fas fa-users me-2"></i>Meet Ministers
                                </a>
                                <a href="<?php echo e(route('id-card.verify')); ?>" class="btn btn-outline-info">
                                    <i class="fas fa-shield-alt me-2"></i>Verify ID Card
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>TUCASA STU Summit</h5>
                    <p class="text-muted">Empowering students through faith and fellowship.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted">&copy; <?php echo e(date('Y')); ?> TUCASA STU. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
<?php /**PATH C:\wamp64\www\summit-management-system\resources\views/landing/summit.blade.php ENDPATH**/ ?>