<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('summit_activities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('summit_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->text('description');
            $table->enum('type', ['session', 'workshop', 'meal', 'worship', 'recreation', 'other']);
            $table->datetime('start_time');
            $table->datetime('end_time');
            $table->string('venue')->nullable();
            $table->string('facilitator')->nullable();
            $table->boolean('is_mandatory')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('summit_activities');
    }
};
