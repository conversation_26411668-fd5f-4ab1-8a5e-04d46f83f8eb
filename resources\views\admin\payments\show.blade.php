@extends('layouts.admin')

@section('title', 'Payment Details')
@section('page-title', 'Payment Details')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">Payment Details</h2>
                <p class="text-muted">Payment Reference: #{{ $payment->payment_reference }}</p>
            </div>
            <a href="{{ route('admin.payments.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Payments
            </a>
        </div>
    </div>
</div>

<!-- Payment Status Card -->
<div class="row mb-4">
    <div class="col-12">
        <div class="payment-details-card">
            <div class="card-header bg-{{ $payment->status === 'completed' ? 'success' : ($payment->status === 'pending' ? 'warning' : 'danger') }} text-white">
                <h5 class="mb-0">
                    <i class="fas fa-{{ $payment->status === 'completed' ? 'check-circle' : ($payment->status === 'pending' ? 'clock' : 'times-circle') }} me-2"></i>
                    Payment Status: {{ ucfirst($payment->status) }}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Payment Information</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Reference:</strong></td>
                                <td>#{{ $payment->payment_reference }}</td>
                            </tr>
                            @if($payment->transaction_id)
                            <tr>
                                <td><strong>Transaction ID:</strong></td>
                                <td>{{ $payment->transaction_id }}</td>
                            </tr>
                            @endif
                            <tr>
                                <td><strong>Amount:</strong></td>
                                <td><span class="fw-bold text-success">TSh {{ number_format($payment->amount) }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Payment Method:</strong></td>
                                <td><span class="badge bg-info">{{ ucfirst($payment->payment_method) }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Created:</strong></td>
                                <td>{{ $payment->created_at->format('F j, Y \a\t g:i A') }}</td>
                            </tr>
                            @if($payment->paid_at)
                            <tr>
                                <td><strong>Paid At:</strong></td>
                                <td>{{ $payment->paid_at->format('F j, Y \a\t g:i A') }}</td>
                            </tr>
                            @endif
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">User Information</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Name:</strong></td>
                                <td>{{ $payment->user->full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>{{ $payment->user->email }}</td>
                            </tr>
                            <tr>
                                <td><strong>Phone:</strong></td>
                                <td>{{ $payment->user->phone ?? 'N/A' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Branch:</strong></td>
                                <td>{{ $payment->user->branch->name ?? 'N/A' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Conference:</strong></td>
                                <td>{{ $payment->user->branch->zone->conference->name ?? 'N/A' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Summit Registration Details -->
<div class="row mb-4">
    <div class="col-12">
        <div class="payment-details-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-mountain me-2"></i>Summit Registration Details</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Summit Information</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Title:</strong></td>
                                <td>{{ $payment->summitRegistration->summit->title }}</td>
                            </tr>
                            <tr>
                                <td><strong>Theme:</strong></td>
                                <td>{{ $payment->summitRegistration->summit->theme }}</td>
                            </tr>
                            <tr>
                                <td><strong>Year:</strong></td>
                                <td>{{ $payment->summitRegistration->summit->year }}</td>
                            </tr>
                            <tr>
                                <td><strong>Dates:</strong></td>
                                <td>{{ $payment->summitRegistration->summit->start_date->format('M j') }} - {{ $payment->summitRegistration->summit->end_date->format('M j, Y') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Venue:</strong></td>
                                <td>{{ $payment->summitRegistration->summit->venue }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Registration Summary</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Registration #:</strong></td>
                                <td>#{{ $payment->summitRegistration->registration_number }}</td>
                            </tr>
                            <tr>
                                <td><strong>Total Amount:</strong></td>
                                <td>TSh {{ number_format($payment->summitRegistration->total_amount) }}</td>
                            </tr>
                            <tr>
                                <td><strong>Paid Amount:</strong></td>
                                <td>TSh {{ number_format($payment->summitRegistration->paid_amount) }}</td>
                            </tr>
                            <tr>
                                <td><strong>Balance:</strong></td>
                                <td>TSh {{ number_format($payment->summitRegistration->balance) }}</td>
                            </tr>
                            <tr>
                                <td><strong>Payment Status:</strong></td>
                                <td>
                                    <span class="badge bg-{{ $payment->summitRegistration->payment_complete ? 'success' : 'warning' }}">
                                        {{ $payment->summitRegistration->payment_complete ? 'Complete' : 'Pending' }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Gateway Response (if available) -->
@if($payment->gateway_response || $payment->gateway_data)
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-server me-2"></i>Gateway Response</h5>
            </div>
            <div class="card-body">
                @if($payment->gateway_response)
                <div class="mb-3">
                    <h6>Response Message:</h6>
                    <p class="text-muted">{{ $payment->gateway_response }}</p>
                </div>
                @endif
                
                @if($payment->gateway_data)
                <div>
                    <h6>Gateway Data:</h6>
                    <pre class="bg-light p-3 rounded"><code>{{ json_encode($payment->gateway_data, JSON_PRETTY_PRINT) }}</code></pre>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endif

<!-- Admin Actions -->
@if($payment->status === 'pending')
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Admin Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <form method="POST" action="{{ route('admin.payments.approve', $payment) }}">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="btn btn-success" 
                                    onclick="return confirm('Are you sure you want to approve this payment?')">
                                <i class="fas fa-check me-2"></i>Approve Payment
                            </button>
                        </form>
                    </div>
                    <div class="col-md-6">
                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#rejectModal">
                            <i class="fas fa-times me-2"></i>Reject Payment
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reject Payment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('admin.payments.reject', $payment) }}">
                @csrf
                @method('PATCH')
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">Rejection Reason</label>
                        <textarea name="rejection_reason" id="rejection_reason" class="form-control" rows="3" 
                                  placeholder="Please provide a reason for rejecting this payment..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Reject Payment</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif

<!-- Update Status Form -->
<div class="row">
    <div class="col-12">
        <div class="payment-details-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Update Payment</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.payments.update', $payment) }}">
                    @csrf
                    @method('PATCH')
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select name="status" id="status" class="form-select" required>
                                    <option value="pending" {{ $payment->status === 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="completed" {{ $payment->status === 'completed' ? 'selected' : '' }}>Completed</option>
                                    <option value="failed" {{ $payment->status === 'failed' ? 'selected' : '' }}>Failed</option>
                                    <option value="cancelled" {{ $payment->status === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="admin_notes" class="form-label">Admin Notes</label>
                                <textarea name="admin_notes" id="admin_notes" class="form-control" rows="3"
                                          placeholder="Add any notes about this payment...">{{ $payment->admin_notes }}</textarea>
                            </div>
                        </div>
                    </div>
                    <div class="payment-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Payment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
