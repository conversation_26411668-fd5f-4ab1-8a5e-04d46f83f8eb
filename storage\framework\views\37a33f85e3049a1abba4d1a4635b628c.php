<?php $__env->startSection('title', 'ID Card Preview'); ?>

<?php $__env->startSection('content'); ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">ID Card Print Preview</h2>
                <p class="text-muted">Preview your ID card before printing</p>
            </div>
            <div class="d-flex gap-2">
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="fas fa-print me-2"></i>Print Card
                </button>
                <a href="<?php echo e(route('id-cards.download', $idCard)); ?>" class="btn btn-success">
                    <i class="fas fa-download me-2"></i>Download PDF
                </a>
                <a href="<?php echo e(route('id-cards.show', $idCard)); ?>" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Print Instructions -->
<div class="row mb-4 no-print">
    <div class="col-12">
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>Printing Instructions</h6>
            <ul class="mb-0">
                <li>Use standard A4 paper (210 × 297 mm)</li>
                <li>Set printer to "Actual Size" or "100%" scale</li>
                <li>Use high-quality print settings for best results</li>
                <li>Cut along the dotted lines after printing</li>
                <li>Consider laminating for durability</li>
            </ul>
        </div>
    </div>
</div>

<!-- Printable ID Card -->
<div class="row justify-content-center">
    <div class="col-12">
        <div class="print-container">
            <!-- Front of Card -->
            <div class="id-card-print front-card">
                <div class="card-content">
                    <!-- Header -->
                    <div class="card-header-section">
                        <h1 class="organization-name">TUCASA SUMMIT</h1>
                        <h2 class="summit-title"><?php echo e($idCard->summit->title); ?></h2>
                        <div class="summit-year"><?php echo e($idCard->summit->year); ?></div>
                    </div>
                    
                    <!-- Main Content -->
                    <div class="card-main">
                        <!-- Profile Section -->
                        <div class="profile-section">
                            <?php if($idCard->user->profile_image): ?>
                                <img src="<?php echo e(Storage::url($idCard->user->profile_image)); ?>" 
                                     alt="Profile" class="profile-image">
                            <?php else: ?>
                                <div class="profile-placeholder">
                                    <i class="fas fa-user"></i>
                                </div>
                            <?php endif; ?>
                            
                            <?php if($idCard->qr_code_path && Storage::disk('public')->exists($idCard->qr_code_path)): ?>
                            <div class="qr-section">
                                <img src="<?php echo e(Storage::url($idCard->qr_code_path)); ?>" 
                                     alt="QR Code" class="qr-code">
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Details Section -->
                        <div class="details-section">
                            <h3 class="user-name"><?php echo e($idCard->user->full_name); ?></h3>
                            <p class="user-email"><?php echo e($idCard->user->email); ?></p>
                            
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="label">Branch:</span>
                                    <span class="value"><?php echo e(Str::limit($idCard->user->branch->name ?? 'N/A', 20)); ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="label">Conference:</span>
                                    <span class="value"><?php echo e(Str::limit($idCard->user->branch->zone->conference->name ?? 'N/A', 20)); ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="label">University:</span>
                                    <span class="value"><?php echo e(Str::limit($idCard->user->university->name ?? 'N/A', 20)); ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="label">Student ID:</span>
                                    <span class="value"><?php echo e($idCard->user->student_id ?? 'N/A'); ?></span>
                                </div>
                            </div>
                            
                            <div class="card-number-section">
                                <span class="card-number-label">CARD NUMBER</span>
                                <span class="card-number"><?php echo e($idCard->card_number); ?></span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Footer -->
                    <div class="card-footer-section">
                        <div class="footer-item">
                            <span class="footer-label">ISSUED</span>
                            <span class="footer-value"><?php echo e($idCard->issued_at->format('M j, Y')); ?></span>
                        </div>
                        <div class="footer-item">
                            <span class="footer-label">EXPIRES</span>
                            <span class="footer-value"><?php echo e($idCard->expires_at->format('M j, Y')); ?></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Cutting Guidelines -->
            <div class="cutting-guide no-print">
                <p class="text-center text-muted mt-3">
                    <i class="fas fa-scissors me-2"></i>
                    Cut along the dotted border to create your ID card
                </p>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
        margin: 0;
        padding: 0;
    }
    
    .container-fluid {
        padding: 0 !important;
    }
    
    .print-container {
        page-break-inside: avoid;
    }
}

/* ID Card Styles */
.print-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    padding: 20px;
}

.id-card-print {
    width: 85.6mm;
    height: 53.98mm;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: 2px dashed #ccc;
    border-radius: 8px;
    color: white;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.card-content {
    padding: 8px;
    height: 100%;
    position: relative;
    z-index: 2;
}

.card-header-section {
    text-align: center;
    margin-bottom: 6px;
}

.organization-name {
    font-size: 10px;
    font-weight: bold;
    margin: 0 0 2px 0;
    letter-spacing: 1px;
}

.summit-title {
    font-size: 8px;
    margin: 0 0 1px 0;
    font-weight: normal;
}

.summit-year {
    font-size: 6px;
    opacity: 0.8;
}

.card-main {
    display: flex;
    align-items: flex-start;
    gap: 6px;
}

.profile-section {
    flex: 0 0 auto;
    text-align: center;
}

.profile-image {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: 2px solid white;
    object-fit: cover;
    margin-bottom: 4px;
}

.profile-placeholder {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: 2px solid white;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4px;
    font-size: 14px;
}

.qr-section {
    margin-top: 4px;
}

.qr-code {
    width: 25px;
    height: 25px;
    background: white;
    padding: 2px;
    border-radius: 3px;
}

.details-section {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-size: 9px;
    font-weight: bold;
    margin: 0 0 2px 0;
    line-height: 1.1;
}

.user-email {
    font-size: 6px;
    opacity: 0.8;
    margin: 0 0 3px 0;
    word-break: break-all;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3px;
    margin-bottom: 4px;
}

.info-item {
    font-size: 5px;
}

.label {
    opacity: 0.7;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-bottom: 1px;
}

.value {
    font-weight: bold;
    font-size: 6px;
    line-height: 1.1;
    display: block;
}

.card-number-section {
    background: rgba(255,255,255,0.15);
    border-radius: 4px;
    padding: 3px;
    margin-top: 3px;
}

.card-number-label {
    font-size: 4px;
    opacity: 0.8;
    display: block;
    margin-bottom: 1px;
}

.card-number {
    font-size: 7px;
    font-weight: bold;
    letter-spacing: 0.5px;
}

.card-footer-section {
    position: absolute;
    bottom: 4px;
    left: 8px;
    right: 8px;
    display: flex;
    justify-content: space-between;
    border-top: 1px solid rgba(255,255,255,0.3);
    padding-top: 2px;
}

.footer-item {
    font-size: 4px;
}

.footer-label {
    opacity: 0.7;
    display: block;
    margin-bottom: 1px;
}

.footer-value {
    font-weight: bold;
    font-size: 5px;
}

/* Screen-only styles */
@media screen {
    .cutting-guide {
        margin-top: 20px;
    }
    
    .id-card-print {
        transform: scale(2);
        margin: 40px auto;
    }
    
    .print-container {
        padding: 80px 20px;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\summit-management-system\resources\views/user/id-cards/preview.blade.php ENDPATH**/ ?>