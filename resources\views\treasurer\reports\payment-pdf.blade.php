<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Payment Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            color: #333;
            font-size: 24px;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .summary {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }
        .summary-item {
            text-align: center;
        }
        .summary-item h3 {
            margin: 0;
            color: #28a745;
            font-size: 18px;
        }
        .summary-item p {
            margin: 5px 0 0 0;
            color: #666;
            font-size: 11px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .status-completed {
            background-color: #d4edda;
            color: #155724;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
        }
        .status-failed {
            background-color: #f8d7da;
            color: #721c24;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #666;
            font-size: 10px;
        }
        .amount {
            font-weight: bold;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>TUCASA Summit Management</h1>
        <h2>Payment Report</h2>
        <p>Period: {{ $summary['date_from'] }} to {{ $summary['date_to'] }}</p>
        <p>Generated on: {{ $summary['generated_at'] }} by {{ $summary['generated_by'] }}</p>
    </div>

    <div class="summary">
        <h3>Summary</h3>
        <div class="summary-grid">
            <div class="summary-item">
                <h3>{{ number_format($summary['total_amount'], 0) }} TZS</h3>
                <p>Total Amount</p>
            </div>
            <div class="summary-item">
                <h3>{{ $summary['total_count'] }}</h3>
                <p>Total Transactions</p>
            </div>
            <div class="summary-item">
                <h3>{{ $summary['completed_count'] }}</h3>
                <p>Completed</p>
            </div>
            <div class="summary-item">
                <h3>{{ $summary['failed_count'] }}</h3>
                <p>Failed</p>
            </div>
        </div>
    </div>

    <h3>Payment Transactions</h3>
    <table>
        <thead>
            <tr>
                <th>Date</th>
                <th>Participant</th>
                <th>Branch</th>
                <th>Conference</th>
                <th>Amount</th>
                <th>Status</th>
                <th>Method</th>
                <th>Reference</th>
            </tr>
        </thead>
        <tbody>
            @foreach($payments as $payment)
            <tr>
                <td>{{ $payment->created_at->format('M j, Y H:i') }}</td>
                <td>
                    <strong>{{ $payment->user->full_name }}</strong><br>
                    <small>{{ $payment->user->email }}</small>
                </td>
                <td>{{ $payment->user->branch->name ?? 'N/A' }}</td>
                <td>{{ $payment->user->branch->zone->conference->name ?? 'N/A' }}</td>
                <td class="amount">{{ number_format($payment->amount, 0) }} TZS</td>
                <td>
                    @if($payment->status === 'completed')
                        <span class="status-completed">Completed</span>
                    @elseif($payment->status === 'pending')
                        <span class="status-pending">Pending</span>
                    @else
                        <span class="status-failed">Failed</span>
                    @endif
                </td>
                <td>{{ ucfirst($payment->payment_method ?? 'N/A') }}</td>
                <td>{{ $payment->reference_number ?? 'N/A' }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>

    @if($payments->count() === 0)
    <div style="text-align: center; padding: 40px; color: #666;">
        <p>No payments found for the selected period.</p>
    </div>
    @endif

    <div class="footer">
        <p>This report was generated automatically by TUCASA Summit Management System</p>
        <p>For questions or concerns, please contact the system administrator</p>
    </div>
</body>
</html>
