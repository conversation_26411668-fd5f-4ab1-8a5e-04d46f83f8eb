<?php

namespace App\Jobs;

use App\Models\Payment;
use App\Services\AzamPayService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class RetryFailedPayment implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $payment;
    protected $maxRetries = 3;

    public function __construct(Payment $payment)
    {
        $this->payment = $payment;
    }

    public function handle(AzamPayService $azamPayService)
    {
        // Only retry if payment is in failed state and hasn't exceeded max retries
        if ($this->payment->status !== 'failed' || $this->attempts() > $this->maxRetries) {
            return;
        }

        Log::info('Retrying failed payment', [
            'payment_id' => $this->payment->id,
            'attempt' => $this->attempts()
        ]);

        // Check payment status first
        $statusResult = $azamPayService->checkPaymentStatus($this->payment);
        
        if ($statusResult['success'] && $statusResult['status'] === 'success') {
            Log::info('Payment actually succeeded on retry check', [
                'payment_id' => $this->payment->id
            ]);
            return;
        }

        // If still failed, retry based on payment method
        if (in_array($this->payment->payment_method, ['azampay_mobile', 'azampay_bank'])) {
            $this->retryAzamPayPayment($azamPayService);
        }
    }

    private function retryAzamPayPayment(AzamPayService $azamPayService)
    {
        $registration = $this->payment->summitRegistration;
        $gatewayData = $this->payment->gateway_data;

        if ($this->payment->payment_method === 'azampay_mobile') {
            $result = $azamPayService->initiateMobileMoneyPayment(
                $registration,
                $this->payment->amount,
                $gatewayData['phone_number'],
                $gatewayData['provider']
            );
        } elseif ($this->payment->payment_method === 'azampay_bank') {
            $result = $azamPayService->initiateBankPayment(
                $registration,
                $this->payment->amount,
                $gatewayData['bank_code']
            );
        }

        if (isset($result) && $result['success']) {
            Log::info('Payment retry successful', [
                'payment_id' => $this->payment->id,
                'attempt' => $this->attempts()
            ]);
        } else {
            Log::warning('Payment retry failed', [
                'payment_id' => $this->payment->id,
                'attempt' => $this->attempts(),
                'error' => $result['message'] ?? 'Unknown error'
            ]);
            
            // Schedule next retry with exponential backoff
            if ($this->attempts() < $this->maxRetries) {
                $delay = pow(2, $this->attempts()) * 60; // 2, 4, 8 minutes
                self::dispatch($this->payment)->delay(now()->addSeconds($delay));
            }
        }
    }

    public function failed(\Throwable $exception)
    {
        Log::error('Payment retry job failed permanently', [
            'payment_id' => $this->payment->id,
            'error' => $exception->getMessage()
        ]);
    }
}
