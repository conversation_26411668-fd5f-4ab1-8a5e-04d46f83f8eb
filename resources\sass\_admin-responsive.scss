// Responsive Admin Page Styles
.admin-page-container {
    padding: 20px;
    max-width: 100%;
}

.page-header {
    background-color: var(--md-surface, white);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-title {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: #1a1a1a;
}

.page-subtitle {
    font-size: 16px;
    color: #666;
    margin: 0;
}

.search-card {
    background-color: var(--md-surface, white);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #1a1a1a;
}

.search-form {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 16px;
    align-items: end;
}

.search-inputs {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

.form-input, .form-select {
    padding: 12px 16px;
    border: 1.5px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.2s ease;
    background: white;
}

.form-input:focus, .form-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-actions {
    display: flex;
    gap: 8px;
}

.btn-primary {
    background-color: #667eea;
    border: none;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary:hover {
    background-color: #5a67d8;
    transform: translateY(-1px);
    color: white;
    text-decoration: none;
}

.btn-outline-secondary {
    background-color: transparent;
    border: 1.5px solid #e5e7eb;
    color: #6b7280;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-outline-secondary:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
    color: #374151;
    text-decoration: none;
}

.data-table-card {
    background-color: var(--md-surface, white);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-header {
    background-color: #f8fafc;
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.table-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #1a1a1a;
}

.table-responsive {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 800px;
}

.data-table th {
    background-color: #f8fafc;
    padding: 16px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
    font-size: 14px;
}

.data-table td {
    padding: 16px;
    border-bottom: 1px solid #f1f5f9;
    color: #1f2937;
}

.data-table tr:hover {
    background-color: #f8fafc;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.status-active {
    background-color: #d1fae5;
    color: #065f46;
}

.status-inactive {
    background-color: #f3f4f6;
    color: #6b7280;
}

.count-badge {
    background-color: #dbeafe;
    color: #1e40af;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.action-buttons {
    display: flex;
    gap: 4px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 6px;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.empty-state-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #374151;
}

.empty-state-text {
    font-size: 16px;
    margin: 0 0 20px 0;
}

// Responsive Design
@media (max-width: 768px) {
    .admin-page-container {
        padding: 16px;
    }
    
    .page-header {
        padding: 20px;
    }
    
    .page-header > div {
        flex-direction: column;
        gap: 16px;
        align-items: stretch !important;
    }
    
    .search-form {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .search-inputs {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .search-actions {
        justify-content: stretch;
    }
    
    .search-actions .btn-primary,
    .search-actions .btn-outline-secondary {
        flex: 1;
        justify-content: center;
    }
    
    .data-table-card {
        margin: 0 -16px;
        border-radius: 0;
    }
    
    .table-header {
        padding: 16px;
    }
    
    .data-table th,
    .data-table td {
        padding: 12px 8px;
        font-size: 14px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 4px;
    }
    
    .btn-sm {
        padding: 8px 12px;
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 24px;
    }
    
    .data-table {
        min-width: 600px;
    }
    
    .data-table th,
    .data-table td {
        padding: 8px 6px;
        font-size: 13px;
    }
}
