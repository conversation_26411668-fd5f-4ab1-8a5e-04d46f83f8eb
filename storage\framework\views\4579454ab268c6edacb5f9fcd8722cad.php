<?php $__env->startSection('title', 'Registration Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">
                    Registration Details
                    <?php if($registration->status === 'cancelled'): ?>
                        <span class="badge bg-danger ms-2">CANCELLED</span>
                    <?php elseif($registration->status === 'confirmed'): ?>
                        <span class="badge bg-success ms-2">CONFIRMED</span>
                    <?php else: ?>
                        <span class="badge bg-warning ms-2">PENDING</span>
                    <?php endif; ?>
                </h2>
                <p class="text-muted"><?php echo e($registration->summit->title); ?> (<?php echo e($registration->summit->year); ?>)</p>
            </div>
            <div class="d-flex gap-2">
                <?php if($registration->balance > 0 && $registration->status !== 'cancelled'): ?>
                <a href="<?php echo e(route('payments.create', $registration)); ?>" class="btn btn-success">
                    <i class="fas fa-credit-card me-2"></i>Make Payment
                </a>
                <?php endif; ?>
                <?php if($registration->canBeCancelledByUser()): ?>
                <button type="button" class="btn btn-outline-danger" onclick="cancelRegistration()">
                    <i class="fas fa-times me-2"></i>Cancel Registration
                </button>
                <?php endif; ?>
                <a href="<?php echo e(route('registrations.index')); ?>" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Registrations
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Registration Information -->
<?php if($registration->status === 'cancelled'): ?>
<div class="alert alert-danger">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <strong>Registration Cancelled:</strong> This registration has been cancelled and is no longer active.
</div>
<?php endif; ?>

<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card <?php echo e($registration->status === 'cancelled' ? 'border-danger' : ''); ?>">
            <div class="card-header <?php echo e($registration->status === 'cancelled' ? 'bg-light' : ''); ?>">
                <h5 class="mb-0 <?php echo e($registration->status === 'cancelled' ? 'text-muted' : ''); ?>">
                    <i class="fas fa-clipboard-list me-2"></i>Registration Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Registration #:</strong></td>
                                <td><span class="badge bg-primary"><?php echo e($registration->registration_number); ?></span></td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    <span class="badge bg-<?php echo e($registration->status === 'confirmed' ? 'success' : ($registration->status === 'cancelled' ? 'danger' : 'warning')); ?>">
                                        <?php echo e(ucfirst($registration->status)); ?>

                                    </span>
                                    <?php if($registration->status === 'cancelled'): ?>
                                        <small class="text-muted d-block">This registration has been cancelled</small>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Summit:</strong></td>
                                <td><?php echo e($registration->summit->title); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Year:</strong></td>
                                <td><?php echo e($registration->summit->year); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Venue:</strong></td>
                                <td><?php echo e($registration->summit->venue); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Dates:</strong></td>
                                <td><?php echo e($registration->summit->start_date->format('M j')); ?> - <?php echo e($registration->summit->end_date->format('M j, Y')); ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Registration Date:</strong></td>
                                <td><?php echo e($registration->created_at->format('F j, Y')); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Total Amount:</strong></td>
                                <td><span class="fw-bold text-primary">TSh <?php echo e(number_format($registration->total_amount)); ?></span></td>
                            </tr>
                            <tr>
                                <td><strong>Paid Amount:</strong></td>
                                <td><span class="fw-bold text-success">TSh <?php echo e(number_format($registration->paid_amount)); ?></span></td>
                            </tr>
                            <tr>
                                <td><strong>Balance:</strong></td>
                                <td>
                                    <?php if($registration->balance > 0): ?>
                                        <span class="fw-bold text-warning">TSh <?php echo e(number_format($registration->balance)); ?></span>
                                    <?php else: ?>
                                        <span class="fw-bold text-success">TSh 0 (Paid)</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Payment Status:</strong></td>
                                <td>
                                    <span class="badge bg-<?php echo e($registration->payment_complete ? 'success' : 'warning'); ?>">
                                        <?php echo e($registration->payment_complete ? 'Complete' : 'Pending'); ?>

                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if($registration->balance > 0): ?>
                    <a href="<?php echo e(route('payments.create', $registration)); ?>" class="btn btn-success">
                        <i class="fas fa-credit-card me-2"></i>Make Payment
                    </a>
                    <?php endif; ?>
                    
                    <?php if($registration->payment_complete): ?>
                    <a href="<?php echo e(route('id-cards.index')); ?>" class="btn btn-primary">
                        <i class="fas fa-id-card me-2"></i>Get ID Card
                    </a>
                    <?php endif; ?>
                    
                    <a href="<?php echo e(route('payments.index')); ?>" class="btn btn-outline-info">
                        <i class="fas fa-history me-2"></i>Payment History
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Summit Information -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-mountain me-2"></i>Summit Details</h5>
            </div>
            <div class="card-body">
                <h6><?php echo e($registration->summit->title); ?></h6>
                <p class="text-muted mb-2"><?php echo e($registration->summit->theme); ?></p>
                
                <div class="mb-2">
                    <small class="text-muted d-block">Venue</small>
                    <strong><?php echo e($registration->summit->venue); ?></strong>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted d-block">Duration</small>
                    <strong><?php echo e($registration->summit->start_date->format('M j')); ?> - <?php echo e($registration->summit->end_date->format('M j, Y')); ?></strong>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted d-block">Registration Fee</small>
                    <strong>TSh <?php echo e(number_format($registration->summit->registration_fee)); ?></strong>
                </div>
                
                <?php if($registration->summit->description): ?>
                <div class="mt-3">
                    <small class="text-muted d-block">Description</small>
                    <p class="small"><?php echo e($registration->summit->description); ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Payment History -->
<?php if($registration->payments->count() > 0): ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Payment History</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Reference</th>
                                <th>Amount</th>
                                <th>Method</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $registration->payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <div>
                                        <div class="fw-bold">#<?php echo e($payment->payment_reference); ?></div>
                                        <?php if($payment->transaction_id): ?>
                                            <small class="text-muted"><?php echo e($payment->transaction_id); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-bold text-success">TSh <?php echo e(number_format($payment->amount)); ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo e(ucfirst($payment->payment_method)); ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo e($payment->status === 'completed' ? 'success' : ($payment->status === 'pending' ? 'warning' : 'danger')); ?>">
                                        <?php echo e(ucfirst($payment->status)); ?>

                                    </span>
                                </td>
                                <td>
                                    <div>
                                        <div><?php echo e($payment->created_at->format('M j, Y')); ?></div>
                                        <?php if($payment->paid_at): ?>
                                            <small class="text-muted">Paid: <?php echo e($payment->paid_at->format('M j, Y')); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <a href="<?php echo e(route('payments.show', $payment)); ?>" 
                                       class="btn btn-outline-primary btn-sm" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Cancel Registration Modal -->
<div class="modal fade" id="cancelRegistrationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Registration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to cancel your registration for <strong><?php echo e($registration->summit->title); ?></strong>?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> This action cannot be undone. You will need to register again if you change your mind.
                </div>
                <?php if($registration->hasSuccessfulPayments()): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-ban me-2"></i>
                    <strong>Note:</strong> You have made successful payments for this registration. Please contact the admin for refund processing.
                </div>
                <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Note:</strong> Any pending or failed payments will also be cancelled automatically.
                </div>
                <?php endif; ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Keep Registration</button>
                <form method="POST" action="<?php echo e(route('registrations.cancel', $registration)); ?>" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PATCH'); ?>
                    <button type="submit" class="btn btn-danger">Cancel Registration</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function cancelRegistration() {
    const modal = new bootstrap.Modal(document.getElementById('cancelRegistrationModal'));
    modal.show();
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\summit-management-system\resources\views/user/registrations/show.blade.php ENDPATH**/ ?>