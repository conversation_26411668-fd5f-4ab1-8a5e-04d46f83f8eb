@extends('layouts.admin')

@section('title', 'Zone Details')
@section('page-title', 'Zone Details')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">{{ $zone->name }}</h2>
                <p class="text-muted">Zone details and statistics.</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('admin.zones.edit', $zone) }}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>Edit Zone
                </a>
                <a href="{{ route('admin.zones.index') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Zones
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Zone Information -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-map me-2"></i>Zone Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Name:</strong></td>
                                <td>{{ $zone->name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Code:</strong></td>
                                <td><span class="badge bg-primary">{{ $zone->code }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Conference:</strong></td>
                                <td>{{ $zone->conference->name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    <span class="badge bg-{{ $zone->is_active ? 'success' : 'secondary' }}">
                                        {{ $zone->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Total Branches:</strong></td>
                                <td><span class="badge bg-info">{{ $zone->branches->count() }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Created:</strong></td>
                                <td>{{ $zone->created_at->format('F j, Y') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Last Updated:</strong></td>
                                <td>{{ $zone->updated_at->format('F j, Y') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                @if($zone->description)
                <div class="mt-3">
                    <h6>Description</h6>
                    <p class="text-muted">{{ $zone->description }}</p>
                </div>
                @endif
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Quick Stats</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Active Branches</span>
                    <span class="badge bg-success">{{ $zone->branches->where('is_active', true)->count() }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Inactive Branches</span>
                    <span class="badge bg-secondary">{{ $zone->branches->where('is_active', false)->count() }}</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Branches -->
@if($zone->branches->count() > 0)
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-code-branch me-2"></i>Branches ({{ $zone->branches->count() }})</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Branch Name</th>
                                <th>Code</th>
                                <th>University</th>
                                <th>Users</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($zone->branches as $branch)
                            <tr>
                                <td>
                                    <div class="fw-bold">{{ $branch->name }}</div>
                                    @if($branch->description)
                                        <small class="text-muted">{{ Str::limit($branch->description, 40) }}</small>
                                    @endif
                                </td>
                                <td><span class="badge bg-primary">{{ $branch->code }}</span></td>
                                <td>{{ $branch->university->name ?? 'N/A' }}</td>
                                <td>
                                    <span class="badge bg-info">{{ $branch->users->count() }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $branch->is_active ? 'success' : 'secondary' }}">
                                        {{ $branch->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('admin.branches.show', $branch) }}" 
                                           class="btn btn-outline-primary" title="View Branch">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.branches.edit', $branch) }}" 
                                           class="btn btn-outline-secondary" title="Edit Branch">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endif
@endsection
