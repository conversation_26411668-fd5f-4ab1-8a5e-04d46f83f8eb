<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\URL;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Force HTTPS when using ngrok or in production
        if (config('app.env') !== 'local' || request()->header('x-forwarded-proto') === 'https') {
            URL::forceScheme('https');
        }

        // Force HTTPS for ngrok
        if (str_contains(config('app.url'), 'ngrok')) {
            URL::forceScheme('https');
        }

        // Configure rate limiting for payments
        \Illuminate\Support\Facades\RateLimiter::for('payment', function (\Illuminate\Http\Request $request) {
            return \Illuminate\Cache\RateLimiting\Limit::perMinute(5)->by($request->user()?->id ?: $request->ip());
        });

        // Register policies for multi-level admin
        \Illuminate\Support\Facades\Gate::policy(\App\Models\Conference::class, \App\Policies\MultiLevelAdminPolicy::class);
        \Illuminate\Support\Facades\Gate::policy(\App\Models\Zone::class, \App\Policies\MultiLevelAdminPolicy::class);
        \Illuminate\Support\Facades\Gate::policy(\App\Models\Branch::class, \App\Policies\MultiLevelAdminPolicy::class);
    }
}
