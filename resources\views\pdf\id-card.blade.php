<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TUCASA Summit ID Card - {{ $idCard->card_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: #f5f5f5;
            padding: 20px;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        .id-card {
            width: 3.375in; /* Credit card standard width */
            height: 2.125in; /* Credit card standard height */
            /* Replace gradient with solid color for better PDF compatibility */
            background: #667eea;
            background-image: none;
            border-radius: 8px;
            color: white !important;
            position: relative;
            overflow: hidden;
            margin: 0 auto;
            box-shadow: none; /* Remove shadow for PDF */
            border: 2px solid #764ba2;
        }
        
        .card-content {
            padding: 8px;
            height: 100%;
            position: relative;
            z-index: 2;
        }
        
        .background-pattern {
            position: absolute;
            top: -20px;
            right: -20px;
            font-size: 60px;
            opacity: 0.1;
            z-index: 1;
        }
        
        .header {
            text-align: center;
            margin-bottom: 6px;
        }
        
        .header h1 {
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 2px;
            letter-spacing: 1px;
            color: #ffffff !important;
            text-shadow: none; /* Remove text shadow for PDF */
        }

        .header h2 {
            font-size: 8px;
            margin-bottom: 1px;
            font-weight: normal;
            color: #ffffff !important;
            text-shadow: none; /* Remove text shadow for PDF */
        }

        .header .year {
            font-size: 7px;
            color: #ffffff !important;
            text-shadow: none; /* Remove text shadow for PDF */
        }
        
        .main-content {
            display: flex;
            align-items: flex-start;
            gap: 6px;
        }
        
        .profile-section {
            flex: 0 0 auto;
            text-align: center;
        }
        
        .profile-image {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            border: 2px solid white;
            object-fit: cover;
            margin-bottom: 4px;
        }
        
        .profile-placeholder {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            border: 2px solid white;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 4px;
            font-size: 14px;
        }
        
        .qr-code {
            width: 80px;
            height: 80px;
            background: white !important;
            padding: 4px;
            border-radius: 4px;
            border: 1px solid #ccc;
        }
        
        .qr-code img {
            width: 100%;
            height: 100%;
        }
        
        .details-section {
            flex: 1;
            min-width: 0;
        }
        
        .name {
            font-size: 10px;
            font-weight: bold;
            margin-bottom: 2px;
            line-height: 1.1;
            color: #ffffff !important;
            text-shadow: none; /* Remove text shadow for PDF */
        }

        .email {
            font-size: 7px;
            color: #ffffff !important;
            margin-bottom: 3px;
            word-break: break-all;
            text-shadow: none; /* Remove text shadow for PDF */
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3px;
            margin-bottom: 4px;
        }
        
        .info-item {
            font-size: 5px;
        }
        
        .info-label {
            color: #ffffff !important;
            opacity: 0.8;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 1px;
        }

        .info-value {
            font-weight: bold;
            font-size: 6px;
            line-height: 1.1;
            color: #ffffff !important;
        }
        
        .card-number-section {
            background: #764ba2 !important;
            border: 1px solid #ffffff;
            border-radius: 4px;
            padding: 4px;
            margin-top: 3px;
        }

        .card-number-label {
            font-size: 4px;
            color: #ffffff !important;
            margin-bottom: 1px;
            text-transform: uppercase;
        }

        .card-number {
            font-size: 8px;
            font-weight: bold;
            letter-spacing: 0.5px;
            color: #ffffff !important;
            text-shadow: none; /* Remove text shadow for PDF */
        }
        
        .footer {
            position: absolute;
            bottom: 4px;
            left: 8px;
            right: 8px;
            display: flex;
            justify-content: space-between;
            border-top: 1px solid #ffffff;
            padding-top: 2px;
        }

        .footer-item {
            font-size: 4px;
            color: #ffffff !important;
        }

        .footer-label {
            color: #ffffff !important;
            opacity: 0.8;
            margin-bottom: 1px;
        }

        .footer-value {
            font-weight: bold;
            font-size: 5px;
            color: #ffffff !important;
        }
        
        /* Print styles */
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .id-card {
                box-shadow: none;
                page-break-inside: avoid;
            }
        }
        
        /* Back of card */
        .card-back {
            width: 3.375in;
            height: 2.125in;
            /* Replace gradient with solid color for better PDF compatibility */
            background: #764ba2;
            background-image: none;
            border-radius: 8px;
            color: white !important;
            margin: 20px auto 0;
            padding: 8px;
            text-align: center;
            position: relative;
            overflow: hidden;
            border: 2px solid #667eea;
        }
        
        .back-content {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 2;
        }
        
        .summit-info {
            margin-bottom: 6px;
        }
        
        .summit-title {
            font-size: 8px;
            font-weight: bold;
            margin-bottom: 2px;
            color: #ffffff !important;
        }

        .summit-details {
            font-size: 6px;
            color: #ffffff !important;
            opacity: 0.9;
            line-height: 1.2;
        }

        .verification-info {
            background: #667eea !important;
            border-radius: 4px;
            padding: 4px;
            margin-top: 4px;
            border: 1px solid #ffffff;
        }

        .verification-title {
            font-size: 6px;
            font-weight: bold;
            margin-bottom: 2px;
            color: #ffffff !important;
        }

        .verification-url {
            font-size: 4px;
            word-break: break-all;
            color: #ffffff !important;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <!-- Front of ID Card -->
    <div class="id-card">
        <div class="background-pattern">🏔️</div>
        <div class="card-content">
            <!-- Header -->
            <div class="header">
                <h1>TUCASA SUMMIT</h1>
                <h2>{{ $idCard->summit->title }}</h2>
                <div class="year">{{ $idCard->summit->year }}</div>
            </div>
            
            <!-- Main Content -->
            <div class="main-content">
                <!-- Profile Section -->
                <div class="profile-section">
                    @if($idCard->user->profile_image && file_exists(storage_path('app/public/' . $idCard->user->profile_image)))
                        <img src="{{ storage_path('app/public/' . $idCard->user->profile_image) }}" 
                             alt="Profile" class="profile-image">
                    @else
                        <div class="profile-placeholder">👤</div>
                    @endif
                    
                    @if($idCard->qr_code_path && file_exists(storage_path('app/public/' . $idCard->qr_code_path)))
                        <div class="qr-code">
                            <img src="{{ storage_path('app/public/' . $idCard->qr_code_path) }}" alt="QR Code">
                        </div>
                    @endif
                </div>
                
                <!-- Details Section -->
                <div class="details-section">
                    <div class="name">{{ $idCard->user->full_name }}</div>
                    <div class="email">{{ $idCard->user->email }}</div>
                    
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">Branch</div>
                            <div class="info-value">{{ Str::limit($idCard->user->branch->name ?? 'N/A', 15) }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Conference</div>
                            <div class="info-value">{{ Str::limit($idCard->user->branch->zone->conference->name ?? 'N/A', 15) }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">University</div>
                            <div class="info-value">{{ Str::limit($idCard->user->university->name ?? 'N/A', 15) }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Student ID</div>
                            <div class="info-value">{{ $idCard->user->student_id ?? 'N/A' }}</div>
                        </div>
                    </div>
                    
                    <div class="card-number-section">
                        <div class="card-number-label">CARD NUMBER</div>
                        <div class="card-number">{{ $idCard->card_number }}</div>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="footer">
                <div class="footer-item">
                    <div class="footer-label">ISSUED</div>
                    <div class="footer-value">{{ $idCard->issued_at->format('M j, Y') }}</div>
                </div>
                <div class="footer-item">
                    <div class="footer-label">EXPIRES</div>
                    <div class="footer-value">{{ $idCard->expires_at->format('M j, Y') }}</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Back of ID Card -->
    <div class="card-back">
        <div class="background-pattern">🏔️</div>
        <div class="back-content">
            <div class="summit-info">
                <div class="summit-title">{{ $idCard->summit->title }}</div>
                <div class="summit-details">
                    📅 {{ $idCard->summit->start_date->format('M j') }} - {{ $idCard->summit->end_date->format('M j, Y') }}<br>
                    📍 {{ $idCard->summit->venue }}<br>
                    🎯 {{ $idCard->summit->theme }}
                </div>
            </div>
            
            <div class="verification-info">
                <div class="verification-title">VERIFY THIS CARD</div>
                <div class="verification-url">{{ route('id-card.verify', $idCard->card_number) }}</div>
            </div>
            
            <div style="margin-top: 6px; font-size: 4px; opacity: 0.7;">
                This card is valid only for the specified summit and expires on {{ $idCard->expires_at->format('M j, Y') }}
            </div>
        </div>
    </div>
</body>
</html>
