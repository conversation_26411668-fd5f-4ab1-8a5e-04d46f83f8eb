<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TUCASA Summit ID Card - {{ $idCard->card_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: #f5f5f5;
            padding: 10px;
            margin: 0;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        .card-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 0;
            margin: 0;
        }

        .id-card {
            width: 4in;
            height: 6in;
            background: #ffffff !important;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            border: 2px solid #1a73e8;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
            page-break-inside: avoid;
        }

        .header-section {
            background: #1a73e8 !important;
            color: #ffffff !important;
            text-align: center;
            padding: 24px 20px;
            border-radius: 12px 12px 0 0;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        .header-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;
            letter-spacing: 0.3px;
            color: #ffffff !important;
            text-shadow: none;
        }

        .header-year {
            font-size: 16px;
            font-weight: 400;
            color: #ffffff !important;
            opacity: 0.95;
            text-shadow: none;
        }

        .card-body {
            padding: 32px 24px;
            text-align: center;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            background: #ffffff !important;
        }

        .profile-image {
            width: 96px;
            height: 96px;
            border-radius: 50%;
            border: 3px solid #1a73e8;
            object-fit: cover;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(26, 115, 232, 0.2);
        }

        .profile-placeholder {
            width: 96px;
            height: 96px;
            border-radius: 50%;
            border: 3px solid #1a73e8;
            background: #f8f9fa !important;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: #5f6368 !important;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(26, 115, 232, 0.2);
        }

        .user-name {
            font-size: 22px;
            font-weight: 600;
            color: #202124 !important;
            margin-bottom: 6px;
            line-height: 1.2;
        }

        .user-email {
            font-size: 14px;
            color: #5f6368 !important;
            margin-bottom: 4px;
            font-weight: 400;
        }

        .user-phone {
            font-size: 14px;
            color: #5f6368 !important;
            margin-bottom: 16px;
            font-weight: 400;
        }

        .card-number {
            background: #202124 !important;
            color: #ffffff !important;
            padding: 10px 20px;
            border-radius: 24px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 16px;
            letter-spacing: 1.2px;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .institution-info {
            text-align: center;
            margin-bottom: 20px;
            padding: 12px;
            background: #f8f9fa !important;
            border-radius: 8px;
            border: 1px solid #e8eaed;
        }

        .institution-name {
            font-size: 15px;
            color: #202124 !important;
            margin-bottom: 4px;
            font-weight: 500;
            line-height: 1.3;
        }

        .conference-name {
            font-size: 15px;
            color: #202124 !important;
            font-weight: 500;
            line-height: 1.3;
        }

        .qr-code {
            width: 96px;
            height: 96px;
            margin: 20px auto;
            background: #ffffff !important;
            padding: 8px;
            border-radius: 12px;
            border: 2px solid #e8eaed;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        .status-badge {
            background: #34a853 !important;
            color: #ffffff !important;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 600;
            margin-bottom: 20px;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
            box-shadow: 0 1px 3px rgba(52, 168, 83, 0.3);
        }

        .status-badge::before {
            content: "●";
            font-size: 10px;
        }

        .expiry-info {
            font-size: 14px;
            color: #202124 !important;
            font-weight: 600;
            margin-top: auto;
            padding: 12px 16px;
            background: #fff3cd !important;
            border-radius: 8px;
            border: 1px solid #ffeaa7;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        /* Print styles for optimal printing */
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            body {
                background: white !important;
                padding: 0 !important;
                margin: 0 !important;
            }

            .card-container {
                padding: 0 !important;
                margin: 0 !important;
                min-height: auto !important;
            }

            .id-card {
                box-shadow: none !important;
                page-break-inside: avoid !important;
                margin: 0 auto !important;
                transform: none !important;
            }
        }

        /* Ensure all colors print correctly */
        * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
    </style>
</head>
<body>
<div class="card-container">
    <div class="id-card">
        <!-- Header Section -->
        <div class="header-section">
            <div class="header-title">{{ $idCard->summit->title }}</div>
            <div class="header-year">{{ $idCard->summit->year }}</div>
        </div>

        <!-- Card Body -->
        <div class="card-body">
            <!-- Profile Image -->
            @if($profileImageBase64)
                <img src="{{ $profileImageBase64 }}" class="profile-image" alt="Profile Image">
            @else
                <div class="profile-placeholder">👤</div>
            @endif

            <!-- User Name -->
            <div class="user-name">{{ $idCard->user->full_name }}</div>

            <!-- User Email -->
            <div class="user-email">{{ $idCard->user->email }}</div>

            <!-- User Phone -->
            @if($idCard->user->phone)
            <div class="user-phone">{{ $idCard->user->phone }}</div>
            @endif

            <!-- Card Number -->
            <div class="card-number">{{ $idCard->card_number }}</div>

            <!-- Institution Info -->
            <div class="institution-info">
                <div class="institution-name">{{ $idCard->user->university->name ?? 'N/A' }}</div>
                <div class="conference-name">{{ $idCard->user->branch->zone->conference->name ?? 'N/A' }}</div>
            </div>

            <!-- QR Code -->
            @if($qrCodeBase64)
                <img src="{{ $qrCodeBase64 }}" class="qr-code" alt="QR Code">
            @endif

            <!-- Status Badge -->
            <div class="status-badge">Active</div>

            <!-- Expiry Info -->
            <div class="expiry-info">
                Expires: {{ $idCard->expires_at->format('M j, Y') }}
            </div>
        </div>
    </div>
</div>
</body>
</html>
