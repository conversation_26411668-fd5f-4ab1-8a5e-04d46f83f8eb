<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TUCASA Summit ID Card - {{ $idCard->card_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: #f5f5f5;
            padding: 20px;
            margin: 0;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        .card-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .id-card {
            width: 300px;
            height: 450px;
            background: #f8f9fa;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            position: relative;
            border: 1px solid #e0e0e0;
        }

        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white !important;
            text-align: center;
            padding: 20px 15px;
            border-radius: 20px 20px 0 0;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        .header-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
            letter-spacing: 0.5px;
            color: white !important;
        }

        .header-year {
            font-size: 14px;
            font-weight: normal;
            color: white !important;
        }

        .card-body {
            padding: 25px 20px;
            text-align: center;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .profile-image {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid #667eea;
            object-fit: cover;
            margin-bottom: 15px;
        }

        .profile-placeholder {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid #667eea;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 30px;
            color: #999;
            margin-bottom: 15px;
        }

        .user-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .user-email {
            font-size: 12px;
            color: #333 !important;
            margin-bottom: 8px;
        }

        .user-phone {
            font-size: 12px;
            color: #333 !important;
            margin-bottom: 15px;
        }

        .card-number {
            background: #333 !important;
            color: white !important;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 15px;
            letter-spacing: 1px;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        .institution-info {
            text-align: center;
            margin-bottom: 20px;
        }

        .institution-name {
            font-size: 13px;
            color: #333 !important;
            margin-bottom: 3px;
            font-weight: 500;
        }

        .conference-name {
            font-size: 13px;
            color: #333 !important;
            font-weight: 500;
        }

        .qr-code {
            width: 80px;
            height: 80px;
            margin: 15px auto;
            background: white;
            padding: 5px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .status-badge {
            background: #28a745 !important;
            color: white !important;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 15px;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        .status-badge::before {
            content: "●";
            font-size: 8px;
        }

        .expiry-info {
            font-size: 12px;
            color: #333 !important;
            font-weight: bold;
            margin-top: auto;
            padding: 8px;
            background: #f8f9fa !important;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
<div class="card-container">
    <div class="id-card">
        <!-- Header Section -->
        <div class="header-section">
            <div class="header-title">{{ $idCard->summit->title }}</div>
            <div class="header-year">{{ $idCard->summit->year }}</div>
        </div>

        <!-- Card Body -->
        <div class="card-body">
            <!-- Profile Image -->
            @if($profileImageBase64)
                <img src="{{ $profileImageBase64 }}" class="profile-image" alt="Profile Image">
            @else
                <div class="profile-placeholder">👤</div>
            @endif

            <!-- User Name -->
            <div class="user-name">{{ $idCard->user->full_name }}</div>

            <!-- User Email -->
            <div class="user-email">{{ $idCard->user->email }}</div>

            <!-- User Phone -->
            @if($idCard->user->phone)
            <div class="user-phone">{{ $idCard->user->phone }}</div>
            @endif

            <!-- Card Number -->
            <div class="card-number">{{ $idCard->card_number }}</div>

            <!-- Institution Info -->
            <div class="institution-info">
                <div class="institution-name">{{ $idCard->user->university->name ?? 'N/A' }}</div>
                <div class="conference-name">{{ $idCard->user->branch->zone->conference->name ?? 'N/A' }}</div>
            </div>

            <!-- QR Code -->
            @if($qrCodeBase64)
                <img src="{{ $qrCodeBase64 }}" class="qr-code" alt="QR Code">
            @endif

            <!-- Status Badge -->
            <div class="status-badge">Active</div>

            <!-- Expiry Info -->
            <div class="expiry-info">
                Expires: {{ $idCard->expires_at->format('M j, Y') }}
            </div>
        </div>
    </div>
</div>
</body>
</html>
