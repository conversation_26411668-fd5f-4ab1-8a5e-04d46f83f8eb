<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TUCASA Summit ID Card - {{ $idCard->card_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: #f5f5f5;
            padding: 20px;
            margin: 0;
            color: #fff;
        }

        .print-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 400px;
            padding: 20px;
        }

        .id-card-print {
            width: 640px;
            height: 400px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            padding: 20px;
            position: relative;
        }

        .header {
            text-align: center;
            color: #fff;
            margin-bottom: 12px;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .header h2 {
            font-size: 16px;
            margin-bottom: 2px;
            font-weight: normal;
        }

        .header span {
            font-size: 12px;
            opacity: 0.85;
        }

        .card-body {
            display: flex;
            justify-content: space-between;
            margin-top: 12px;
        }

        .left-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 180px;
        }

        .left-section img.profile-image {
            width: 90px;
            height: 90px;
            border-radius: 50%;
            border: 2px solid white;
            object-fit: cover;
            margin-bottom: 10px;
        }

        .left-section img.qr-code {
            width: 70px;
            height: 70px;
            background: white;
            padding: 6px;
            border-radius: 8px;
        }

        .right-section {
            flex-grow: 1;
            padding-left: 20px;
        }

        .user-info {
            margin-bottom: 16px;
        }

        .user-info h3 {
            font-size: 18px;
            margin-bottom: 4px;
        }

        .user-info p {
            font-size: 12px;
            opacity: 0.85;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px 20px;
            font-size: 11px;
        }

        .info-grid .label {
            font-weight: bold;
            font-size: 10px;
        }

        .card-number-box {
            background: rgba(255,255,255,0.15);
            padding: 10px;
            border-radius: 6px;
            margin-top: 12px;
            font-weight: bold;
            font-size: 13px;
        }

        .footer {
            position: absolute;
            bottom: 16px;
            left: 20px;
            right: 20px;
            display: flex;
            justify-content: space-between;
            font-size: 10px;
            border-top: 1px solid rgba(255,255,255,0.3);
            padding-top: 6px;
        }
    </style>
</head>
<body>
<div class="print-container">
    <div class="id-card-print">
        <div class="header">
            <h1>TUCASA SUMMIT</h1>
            <h2>{{ $idCard->summit->title }}</h2>
            <span>{{ $idCard->summit->year }}</span>
        </div>

        <div class="card-body">
            <div class="left-section">
                @if($profileImageBase64)
                    <img src="{{ $profileImageBase64 }}" class="profile-image" alt="Profile Image">
                @endif

                @if($qrCodeBase64)
                    <img src="{{ $qrCodeBase64 }}" class="qr-code" alt="QR Code">
                @endif
            </div>

            <div class="right-section">
                <div class="user-info">
                    <h3>{{ $idCard->user->full_name }}</h3>
                    <p>{{ $idCard->user->email }}</p>
                </div>

                <div class="info-grid">
                    <div>
                        <div class="label">BRANCH:</div>
                        <div>{{ Str::limit($idCard->user->branch->name ?? 'N/A', 25) }}</div>
                    </div>
                    <div>
                        <div class="label">CONFERENCE:</div>
                        <div>{{ Str::limit($idCard->user->branch->zone->conference->name ?? 'N/A', 25) }}</div>
                    </div>
                    <div>
                        <div class="label">UNIVERSITY:</div>
                        <div>{{ Str::limit($idCard->user->university->name ?? 'N/A', 25) }}</div>
                    </div>
                    <div>
                        <div class="label">STUDENT ID:</div>
                        <div>{{ $idCard->user->student_id ?? 'N/A' }}</div>
                    </div>
                </div>

                <div class="card-number-box">
                    {{ $idCard->card_number }}
                </div>
            </div>
        </div>

        <div class="footer">
            <div>
                <div>ISSUED</div>
                <strong>{{ $idCard->issued_at->format('M d, Y') }}</strong>
            </div>
            <div>
                <div>EXPIRES</div>
                <strong>{{ $idCard->expires_at->format('M d, Y') }}</strong>
            </div>
        </div>
    </div>
</div>
</body>
</html>
